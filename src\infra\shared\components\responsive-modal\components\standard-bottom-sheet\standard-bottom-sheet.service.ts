import { Injectable, TemplateRef, Type } from '@angular/core';
import { MatBottomSheet, MatBottomSheetConfig, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { TranslateService } from '@ngx-translate/core';
import { Observable, firstValueFrom } from 'rxjs';

import { StandardBottomSheetWrapperComponent } from './standard-bottom-sheet.component';
import { StandardBottomSheetConfig } from './models/standard-bottom-sheet-config.interface';
import { StandardBottomSheetResult } from '../../models/view/standard-bottom-sheet-result.interface';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * StandardBottomSheetService - Drop-in replacement cho MatBottomSheet với enhanced features
 *
 * Chức năng chính:
 * - Wrapper around MatBottomSheet với enhanced configuration
 * - Dynamic component loading với auto-render features
 * - Type-safe methods với generics support
 * - Backward compatibility với MatBottomSheet API
 * - Auto-inject title, close button, và default actions
 * - Auto-apply Threads-like animation (scale down .app-drawer + slide-up bottom sheet)
 *
 * Threads-like Animation Features:
 * - Tự động scale down .app-drawer element khi bottom sheet mở
 * - Apply slide-up animation cho bottom sheet panel
 * - Auto cleanup animation khi bottom sheet đóng
 * - Timing: 280ms slide-up, 250ms scale với easing curves
 * - Scale down trước, khi scale được 50% thì slide up
 *
 * Sử dụng:
 * - Thay thế MatBottomSheet injection bằng StandardBottomSheetService
 * - API tương tự MatBottomSheet nhưng với StandardBottomSheetConfig
 * - Có thể mở bất kỳ component nào với enhanced features
 * - Animation được áp dụng tự động, không cần configuration
 */
@Injectable({
  providedIn: 'root'
})
export class StandardBottomSheetService {

  // Threads Animation State Management
  private isAnimationActive = false;
  private appDrawerElement: HTMLElement | null = null;
  private animationTimeouts: number[] = [];

  constructor(
    private readonly matBottomSheet: MatBottomSheet,
    private readonly translateService: TranslateService
  ) {}

  // ===================================================================
  // THREADS-LIKE ANIMATION METHODS
  // ===================================================================

  /**
   * Apply Threads-like animation khi mở bottom sheet
   * - Scale down .app-drawer element với CSS classes
   * - Apply slide-up animation cho bottom sheet panel
   * - Sử dụng CSS transitions để có animation mượt mà
   */
  private applyThreadsAnimation(): void {
    if (this.isAnimationActive) {
      return; // Prevent multiple animations
    }

    // Find .app-drawer element
    this.appDrawerElement = document.querySelector('.app-drawer') as HTMLElement;

    if (!this.appDrawerElement) {
      console.warn('🎭 Threads Animation: .app-drawer element not found, skipping scale animation');
      return;
    }

    this.isAnimationActive = true;

    // Apply scale down animation với CSS class
    // CSS class sẽ handle transition và transform để có animation mượt hơn
    this.appDrawerElement.classList.add('threads-animation-active');

    console.log('🎭 Applied Threads scale animation to .app-drawer element');
  }

  /**
   * Cleanup Threads-like animation khi đóng bottom sheet (fallback method)
   * - Sử dụng khi parallel animation không được trigger
   * - Remove scale effect từ .app-drawer element
   * - Clear animation timeouts
   */
  private cleanupThreadsAnimation(): void {
    if (!this.isAnimationActive) {
      return;
    }

    console.log('🎭 StandardBottomSheetService: Using fallback cleanup animation');

    // Sử dụng finalize method để cleanup
    this.finalizeAnimationCleanup();
  }

  /**
   * Build panel classes với Threads animation support
   * @param customClasses - Custom classes từ config
   * @returns string[] - Array of CSS classes với Threads animation classes
   */
  private buildThreadsPanelClasses(customClasses?: string | string[]): string[] {
    const baseClasses = ['standard-bottom-sheet-panel'];

    // Add Threads animation classes
    baseClasses.push('threads-menu-slide-up');

    if (customClasses) {
      if (typeof customClasses === 'string') {
        baseClasses.push(customClasses);
      } else {
        baseClasses.push(...customClasses);
      }
    }

    return baseClasses;
  }

  /**
   * Setup animation cleanup khi bottom sheet đóng với parallel animation
   * @param bottomSheetRef - Reference đến bottom sheet để listen dismiss events
   */
  private setupAnimationCleanup<TResult>(
    bottomSheetRef: MatBottomSheetRef<StandardBottomSheetWrapperComponent, TResult>
  ): void {
    // Hook vào dismiss event sớm hơn để có parallel animation
    // Sử dụng backdrop click detection và dismiss method override
    this.setupEarlyDismissDetection(bottomSheetRef);

    // Fallback cleanup nếu early detection không hoạt động
    bottomSheetRef.afterDismissed().subscribe(() => {
      // Chỉ cleanup nếu animation vẫn còn active (early detection failed)
      if (this.isAnimationActive) {
        this.cleanupThreadsAnimation();
        console.log('🎭 StandardBottomSheetService: Fallback cleanup after dismiss');
      }
    });
  }

  /**
   * Setup early dismiss detection để trigger parallel animation
   * @param bottomSheetRef - Reference đến bottom sheet
   */
  private setupEarlyDismissDetection<TResult>(
    bottomSheetRef: MatBottomSheetRef<StandardBottomSheetWrapperComponent, TResult>
  ): void {
    // Override dismiss method để trigger cleanup ngay lập tức
    const originalDismiss = bottomSheetRef.dismiss.bind(bottomSheetRef);

    bottomSheetRef.dismiss = (result?: TResult) => {
      // Trigger parallel animation cleanup ngay khi dismiss được gọi
      this.triggerParallelCleanupAnimation();

      // Gọi original dismiss method
      return originalDismiss(result);
    };

    // Listen for backdrop click để trigger early cleanup
    this.setupBackdropClickDetection(bottomSheetRef);
  }

  /**
   * Setup backdrop click detection để trigger parallel animation
   */
  private setupBackdropClickDetection<TResult>(
    _bottomSheetRef: MatBottomSheetRef<StandardBottomSheetWrapperComponent, TResult>
  ): void {
    // Tìm backdrop element và listen for click
    const backdropTimeout = window.setTimeout(() => {
      const backdrop = document.querySelector('.cdk-overlay-backdrop');
      if (backdrop) {
        backdrop.addEventListener('click', () => {
          // Trigger parallel animation khi click backdrop
          this.triggerParallelCleanupAnimation();
          console.log('🎭 StandardBottomSheetService: Backdrop click detected, triggering parallel cleanup');
        }, { once: true });
      }
    }, 100); // Delay nhỏ để backdrop được render

    this.animationTimeouts.push(backdropTimeout);
  }

  /**
   * Trigger parallel cleanup animation để có animation mượt mà như Threads app
   * - App drawer scale-up animation chạy đồng thời với bottom sheet slide-down
   * - Timing được đồng bộ để cả 2 animations kết thúc cùng lúc
   */
  private triggerParallelCleanupAnimation(): void {
    if (!this.isAnimationActive || !this.appDrawerElement) {
      return;
    }

    console.log('🎭 StandardBottomSheetService: Triggering parallel cleanup animation');

    // 1. Trigger app drawer scale-up animation
    this.appDrawerElement.classList.add('threads-animation-closing');
    this.appDrawerElement.classList.remove('threads-animation-active');

    // 2. Trigger bottom sheet slide-down animation
    this.triggerBottomSheetSlideDown();

    // 3. Cleanup sau khi cả 2 animations hoàn thành
    const cleanupTimeout = window.setTimeout(() => {
      this.finalizeAnimationCleanup();
    }, 280); // Match với animation duration

    this.animationTimeouts.push(cleanupTimeout);
  }

  /**
   * Trigger bottom sheet slide-down animation để parallel với app drawer scale-up
   */
  private triggerBottomSheetSlideDown(): void {
    // Tìm bottom sheet container và add closing animation class
    const bottomSheetContainer = document.querySelector('.mat-mdc-bottom-sheet-container');
    if (bottomSheetContainer) {
      bottomSheetContainer.classList.add('threads-parallel-closing');
      console.log('🎭 StandardBottomSheetService: Triggered bottom sheet slide-down animation');
    }
  }

  /**
   * Finalize animation cleanup sau khi parallel animations hoàn thành
   */
  private finalizeAnimationCleanup(): void {
    if (this.appDrawerElement) {
      // Remove all animation classes và styles
      this.appDrawerElement.classList.remove('threads-animation-closing');
      this.appDrawerElement.style.transition = '';
      this.appDrawerElement.style.transform = '';

      console.log('🎭 StandardBottomSheetService: Finalized parallel animation cleanup');
    }

    // Clear animation state
    this.isAnimationActive = false;
    this.appDrawerElement = null;

    // Clear remaining timeouts
    this.animationTimeouts.forEach(timeout => clearTimeout(timeout));
    this.animationTimeouts = [];
  }

  /**
   * Tạo enhanced config với Threads animation classes
   * @param component - Component class để load động
   * @param config - StandardBottomSheetConfig
   * @returns MatBottomSheetConfig với enhanced data và Threads animation classes
   */
  private createEnhancedConfigWithThreadsAnimation<TComponent, TData = unknown>(
    component: Type<TComponent>,
    config?: StandardBottomSheetConfig<TData>
  ): MatBottomSheetConfig<StandardBottomSheetConfig<TData> & { component: Type<TComponent> }> {
    // Default responsive configuration
    const defaultConfig = this.getDefaultConfig();

    // Merge config với defaults sử dụng API mới
    const enhancedConfig: StandardBottomSheetConfig<TData> = {
      // Sử dụng title từ BaseStandardModalConfig
      title: config?.title,
      enableClose: config?.enableClose ?? true,

      // Sử dụng actions config từ BaseStandardModalConfig
      actions: {
        useDefault: config?.actions?.useDefault ?? true,
        customActionsTemplate: config?.actions?.customActionsTemplate
      },

      data: config?.data || config?.componentData,
      hasBackdrop: config?.hasBackdrop ?? true,
      disableClose: config?.disableClose ?? false,
      backdropClass: config?.backdropClass,
      panelClass: this.buildThreadsPanelClasses(config?.panelClass), // Use Threads panel classes
      direction: config?.direction,
      ...defaultConfig,
      ...config // Spread remaining MatBottomSheetConfig properties
    };

    // Tạo MatBottomSheetConfig với component reference
    const matConfig: MatBottomSheetConfig<StandardBottomSheetConfig<TData> & { component: Type<TComponent> }> = {
      ...enhancedConfig,
      data: {
        ...enhancedConfig,
        component
      },
      ariaLabel: typeof enhancedConfig.title === 'string' ? enhancedConfig.title : 'Bottom Sheet'
    };

    return matConfig;
  }

  /**
   * Mở component với StandardBottomSheetService enhanced features
   * Drop-in replacement cho MatBottomSheet.open() với enhanced configuration
   * Auto-apply Threads-like animation (scale down .app-drawer + slide-up bottom sheet)
   *
   * Type Safety: Component phải implement StrictModalComponent interface
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param config - Cấu hình bottom sheet theo StandardBottomSheetConfig interface
   * @returns MatBottomSheetRef với wrapper component
   */
  open<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    config?: StandardBottomSheetConfig<TData>
  ): MatBottomSheetRef<StandardBottomSheetWrapperComponent, TResult> {
    // Apply Threads-like animation trước khi mở bottom sheet
    this.applyThreadsAnimation();

    // Tạo enhanced config với component reference và Threads animation classes
    const enhancedConfig = this.createEnhancedConfigWithThreadsAnimation(component, config);

    // Mở bottom sheet với StandardBottomSheetWrapperComponent
    const bottomSheetRef: MatBottomSheetRef<StandardBottomSheetWrapperComponent, TResult> =
      this.matBottomSheet.open(StandardBottomSheetWrapperComponent, enhancedConfig);

    // Setup cleanup animation khi bottom sheet đóng
    this.setupAnimationCleanup(bottomSheetRef);

    console.log('🎭 StandardBottomSheetService: Opened with Threads animation');

    return bottomSheetRef;
  }

  /**
   * Async version của open method để dễ sử dụng với async/await
   * Auto-apply Threads-like animation (scale down .app-drawer + slide-up bottom sheet)
   *
   * Type Safety: Component phải implement StrictModalComponent interface
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param config - Cấu hình bottom sheet theo StandardBottomSheetConfig interface
   * @returns Promise<TResult | undefined> - Kết quả khi bottom sheet đóng
   */
  async openAsync<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    config?: StandardBottomSheetConfig<TData>
  ): Promise<TResult | undefined> {
    // Sử dụng method open() đã được enhanced với Threads animation
    const bottomSheetRef = this.open<TData, TResult, TComponent>(component, config);

    console.log('🎭 StandardBottomSheetService: OpenAsync with Threads animation');

    return firstValueFrom(bottomSheetRef.afterDismissed());
  }

  // TODO: Implement convenience methods với new API
  // Các methods này sẽ được implement sau khi có SimpleConfirmComponent và SimpleAlertComponent

  /*
  openConfirm(title: string, message?: string): Promise<StandardBottomSheetResult | undefined>
  openAlert(title: string, message?: string): Promise<StandardBottomSheetResult | undefined>
  openWithCustomTitle(titleTemplate: TemplateRef<any>, config: Partial<StandardBottomSheetConfig>): Promise<StandardBottomSheetResult | undefined>
  openWithCustomActions(title: string, actionsTemplate: TemplateRef<any>, config: Partial<StandardBottomSheetConfig>): Promise<StandardBottomSheetResult | undefined>
  */

  /**
   * Đóng tất cả bottom sheets đang mở
   * Auto trigger parallel cleanup animation
   */
  dismiss(): void {
    // Trigger parallel animation cleanup ngay lập tức
    this.triggerParallelCleanupAnimation();

    // Dismiss tất cả bottom sheets
    this.matBottomSheet.dismiss();

    console.log('🎭 StandardBottomSheetService: Dismissed with parallel Threads animation cleanup');
  }



  /**
   * Lấy default configuration cho bottom sheet
   *
   * @returns Partial<StandardBottomSheetConfig>
   */
  private getDefaultConfig(): Partial<StandardBottomSheetConfig> {
    return {
      hasBackdrop: true,
      disableClose: false,
      autoFocus: true,
      restoreFocus: true
    };
  }

}
