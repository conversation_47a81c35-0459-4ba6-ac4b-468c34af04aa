import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { Warehouse } from '@mock/product_form';
import { WarehouseLocation } from '@shared/models/api/warehouse-entities.dto';
import { WarehouseAndLocationPickerComponent } from '@shared/components/warehouse-and-location-picker/warehouse-and-location-picker.component';
import { Supplier } from 'salehub_shared_contracts/requests/shared/supplier';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface VariantWarehousePickerModalData {
  warehouses: Warehouse[];
  suppliers: Supplier[];
  locations: WarehouseLocation[];
  initialData?: {
    warehouseId: string;
    supplierId?: string;
    locationId?: string;
    quantity: number;
  };
}

/**
 * Interface cho kết quả trả về từ modal
 */
export interface VariantWarehousePickerModalResult {
  warehouseId: string;
  supplierId?: string;
  locationId?: string;
  quantity: number;
  action?: string;
}

/**
 * Modal chọn kho hàng cho sản phẩm biến thể
 * Implements StrictModalComponent interface để có type safety đầy đủ
 */
@Component({
  selector: 'app-variant-warehouse-picker-modal',
  templateUrl: './variant-warehouse-picker-modal.component.html',
  styleUrls: ['./variant-warehouse-picker-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReactiveFormsModule,
    TranslateModule,
    WarehouseAndLocationPickerComponent
  ]
})
export class VariantWarehousePickerModalComponent implements OnInit, StrictModalComponent<VariantWarehousePickerModalData, VariantWarehousePickerModalResult> {
  /**
   * Form dữ liệu kho hàng
   */
  warehouseForm: FormGroup;

  /**
   * Danh sách kho hàng
   */
  warehouses: Warehouse[] = [];

  /**
   * Danh sách nhà cung cấp
   */
  suppliers: Supplier[] = [];

  /**
   * Danh sách vị trí trong kho
   */
  locations: WarehouseLocation[] = [];

  /**
   * Dữ liệu đầu vào cho modal
   */
  data: VariantWarehousePickerModalData;

  /**
   * Constructor để khởi tạo component
   */
  constructor(
    private fb: FormBuilder,
    @Optional() private dialogRef?: MatDialogRef<VariantWarehousePickerModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<VariantWarehousePickerModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: VariantWarehousePickerModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: VariantWarehousePickerModalData
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      warehouses: [],
      suppliers: [],
      locations: []
    };

    this.warehouseForm = this.fb.group({
      warehouseSection: this.fb.group({
        warehouseId: ['', Validators.required],
        supplierId: [''],
        locationId: [''],
        quantity: [0, [Validators.required, Validators.min(0)]]
      })
    });

    // Lấy dữ liệu từ data input
    this.warehouses = this.data.warehouses;
    this.suppliers = this.data.suppliers;
    this.locations = this.data.locations;
  }

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    // Nếu có dữ liệu ban đầu thì gán vào form
    if (this.data.initialData) {
      this.warehouseForm.get('warehouseSection')?.patchValue(this.data.initialData);
    }
  }

  /**
   * Xử lý sự kiện hủy bỏ
   */
  onCancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }

  /**
   * Xử lý sự kiện lưu
   */
  onSave(): void {
    if (this.warehouseForm.valid) {
      const result: VariantWarehousePickerModalResult = this.warehouseForm.get('warehouseSection')?.value;
      if (this.dialogRef) {
        this.dialogRef.close(result);
      } else if (this.bottomSheetRef) {
        this.bottomSheetRef.dismiss(result);
      }
    } else {
      // Đánh dấu tất cả các trường là đã chạm để hiển thị lỗi
      this.markFormGroupTouched(this.warehouseForm);
    }
  }

  /**
   * Đánh dấu tất cả các trường trong form là đã touched
   */
  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }

  /**
   * Thêm nhà cung cấp mới
   * (Stub function, sẽ emit event đến parent component để xử lý)
   */
  onAddSupplier(): void {
    // Modal sẽ được đóng và trả về action để parent component xử lý
    const result: VariantWarehousePickerModalResult = {
      warehouseId: '',
      quantity: 0,
      action: 'addSupplier'
    };

    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): VariantWarehousePickerModalResult {
    if (!this.warehouseForm.valid) {
      return {
        warehouseId: '',
        quantity: 0,
        action: 'cancel'
      };
    }

    const formValue = this.warehouseForm.get('warehouseSection')?.value;
    return {
      warehouseId: formValue.warehouseId || '',
      supplierId: formValue.supplierId,
      locationId: formValue.locationId,
      quantity: formValue.quantity || 0,
      action: 'save'
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.warehouseForm.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: VariantWarehousePickerModalData): void {
    this.data = data;
    this.warehouses = data.warehouses;
    this.suppliers = data.suppliers;
    this.locations = data.locations;

    // Cập nhật form với dữ liệu mới
    if (data.initialData) {
      this.warehouseForm.get('warehouseSection')?.patchValue(data.initialData);
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào field đầu tiên khi modal mở
    setTimeout(() => {
      const firstSelect = document.querySelector('mat-select[formControlName="warehouseId"]') as HTMLElement;
      if (firstSelect) {
        firstSelect.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Kiểm tra xem có thay đổi nào chưa được lưu không
    if (this.warehouseForm.dirty && this.warehouseForm.valid) {
      // Có thể thêm logic confirm nếu cần
      return true;
    }
    return true; // Luôn cho phép đóng modal
  }
}
