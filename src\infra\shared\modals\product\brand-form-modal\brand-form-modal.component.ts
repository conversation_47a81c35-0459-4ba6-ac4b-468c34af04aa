import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ProductBrand } from '@mock/product_form';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu đầu vào của BrandFormModalComponent
 */
export interface BrandFormModalData {
  brand?: ProductBrand;
}

/**
 * Interface cho kết quả trả về từ BrandFormModalComponent
 */
export interface BrandFormModalResult {
  _id: string;
  name: string;
  code: string;
  country?: string;
  description?: string;
  website?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

@Component({
  selector: 'app-brand-form-modal',
  templateUrl: './brand-form-modal.component.html',
  styleUrls: ['./brand-form-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule
  ]
})
export class BrandFormModalComponent implements OnInit, StrictModalComponent<BrandFormModalData, BrandFormModalResult> {
  brandForm: FormGroup;
  dialogTitle = 'Thêm thương hiệu mới';
  data: BrandFormModalData;

  constructor(
    private fb: FormBuilder,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: BrandFormModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: BrandFormModalData,
    @Optional() private dialogRef?: MatDialogRef<BrandFormModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<BrandFormModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {};

    this.brandForm = this.fb.group({
      _id: ['', []],
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      country: ['', []],
      description: ['', []],
      website: ['', []],
      isActive: [true, []]
    });
  }

  ngOnInit(): void {
    if (this.data && this.data.brand) {
      this.dialogTitle = 'Chỉnh sửa thương hiệu';
      this.brandForm.patchValue(this.data.brand);
    }
  }

  onNoClick(): void {
    this.close();
  }

  close(result?: any): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  onSubmit(): void {
    if (this.brandForm.valid) {
      const formValue = this.brandForm.value;

      // Tạo ID mới nếu là thêm mới
      if (!formValue._id) {
        formValue._id = `brand_${Date.now().toString()}`;
      }

      this.close(formValue);
    } else {
      // Đánh dấu tất cả các trường là đã chạm để hiển thị lỗi
      Object.keys(this.brandForm.controls).forEach(key => {
        const control = this.brandForm.get(key);
        control?.markAsTouched();
      });
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): BrandFormModalResult {
    if (!this.brandForm.valid) {
      throw new Error('Form is not valid');
    }

    const formValue = this.brandForm.value;

    // Tạo ID mới nếu là thêm mới
    if (!formValue._id) {
      formValue._id = `brand_${Date.now().toString()}`;
    }

    return {
      _id: formValue._id,
      name: formValue.name,
      code: formValue.code,
      country: formValue.country,
      description: formValue.description,
      website: formValue.website,
      isActive: formValue.isActive,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.brandForm.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: BrandFormModalData): void {
    this.data = data;

    if (data.brand) {
      this.dialogTitle = 'Chỉnh sửa thương hiệu';
      this.brandForm.patchValue(data.brand);
    } else {
      this.dialogTitle = 'Thêm thương hiệu mới';
      this.brandForm.reset({
        _id: '',
        name: '',
        code: '',
        country: '',
        description: '',
        website: '',
        isActive: true
      });
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào field đầu tiên khi modal mở
    setTimeout(() => {
      const firstInput = document.querySelector('input[formControlName="name"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Kiểm tra xem có thay đổi nào chưa được lưu không
    if (this.brandForm.dirty && this.brandForm.valid) {
      // Có thể thêm logic confirm nếu cần
      return true;
    }
    return true; // Luôn cho phép đóng modal
  }
}
