// StandardDialogComponent Styles
// Responsive design v<PERSON><PERSON> classes và Angular Material theming

.standard-dialog-container {
  // Container ch<PERSON><PERSON> c<PERSON>a dialog
  min-width: 300px;
  max-width: 90vw;
  
  // Responsive width với <PERSON>trap breakpoints
  @media (min-width: 576px) {
    min-width: 400px;
  }
  
  @media (min-width: 768px) {
    min-width: 500px;
    max-width: 600px;
  }
  
  @media (min-width: 992px) {
    max-width: 700px;
  }
  
  @media (min-width: 1200px) {
    max-width: 800px;
  }
}

// Dialog Title Styling
.standard-dialog-title {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  margin-bottom: 0;
  
  .title-content {
    h2 {
      font-size: 1.25rem;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.87);
      margin: 0;
      line-height: 1.4;
      
      // Responsive font size
      @media (max-width: 575px) {
        font-size: 1.1rem;
      }
    }
  }
  
  .close-button {
    color: rgba(0, 0, 0, 0.54);
    width: 32px;
    height: 32px;
    line-height: 32px;
    
    &:hover {
      color: rgba(0, 0, 0, 0.87);
      background-color: rgba(0, 0, 0, 0.04);
    }
    
    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

// Dialog Content Styling
.standard-dialog-content {
  padding: 20px 24px;
  max-height: 60vh;
  overflow-y: auto;
  
  // Responsive padding
  @media (max-width: 575px) {
    padding: 16px 20px;
    max-height: 50vh;
  }
  
  // Custom scrollbar cho webkit browsers
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

// Dialog Actions Styling
.standard-dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  margin-top: 0;
  
  // Responsive padding
  @media (max-width: 575px) {
    padding: 12px 20px;
    flex-direction: column-reverse;
    gap: 8px !important;
    
    .default-actions,
    .custom-actions,
    .fallback-actions {
      width: 100%;
      
      button {
        width: 100%;
        margin: 0;
      }
    }
    
    .default-actions {
      flex-direction: column-reverse;
      gap: 8px;
    }
  }
  
  // Button styling
  button {
    min-width: 80px;
    height: 36px;
    
    &.cancel-button {
      color: rgba(0, 0, 0, 0.54);
      
      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
    
    &.confirm-button {
      font-weight: 500;
    }
    
    // Responsive button size
    @media (max-width: 575px) {
      height: 40px;
      font-size: 0.9rem;
    }
  }
}

// Gap utilities cho actions
.gap-2 {
  gap: 8px;
}

// Custom focus styles cho accessibility
button:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

// Animation cho dialog entrance
.standard-dialog-container {
  animation: dialogEnter 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes dialogEnter {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .standard-dialog-title {
    border-bottom-color: rgba(255, 255, 255, 0.12);
    
    .title-content h2 {
      color: rgba(255, 255, 255, 0.87);
    }
    
    .close-button {
      color: rgba(255, 255, 255, 0.54);
      
      &:hover {
        color: rgba(255, 255, 255, 0.87);
        background-color: rgba(255, 255, 255, 0.04);
      }
    }
  }
  
  .standard-dialog-actions {
    border-top-color: rgba(255, 255, 255, 0.12);
    
    .cancel-button {
      color: rgba(255, 255, 255, 0.54);
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.04);
      }
    }
  }
}
