import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * SimpleTestComponent - Component đơn giản để test StandardDialogService
 *
 * Chức năng:
 * - Hiển thị message được truyền vào
 * - Test dynamic component loading
 * - Verify data passing functionality
 */
/**
 * Interface cho dữ liệu đầu vào của SimpleTestComponent
 */
export interface SimpleTestData {
  message?: string;
  testData?: any;
  userName?: string;
  action?: string;
  options?: string[];
  content?: string;
  testFeature?: string;
}

/**
 * Interface cho kết quả trả về từ SimpleTestComponent
 */
export interface SimpleTestResult {
  action: 'confirm' | 'cancel' | 'custom';
  data?: any;
  customAction?: { name: string; value: any };
}

@Component({
  selector: 'app-simple-test',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="simple-test-content p-3">
      <h4>Simple Test Component</h4>
      <p *ngIf="message">{{ message }}</p>
      <p *ngIf="data">Data: {{ data | json }}</p>
      <div class="mt-3">
        <p class="text-muted">
          Đây là component được load động bởi StandardDialogService.
          Component này nhận data từ config và hiển thị trong dialog.
        </p>
      </div>
    </div>
  `,
  styles: [`
    .simple-test-content {
      min-height: 100px;
    }

    h4 {
      color: #333;
      margin-bottom: 1rem;
    }

    p {
      margin-bottom: 0.5rem;
    }

    .text-muted {
      font-size: 0.9rem;
      color: #6c757d;
    }
  `]
})
export class SimpleTestComponent implements StrictModalComponent<SimpleTestData, SimpleTestResult> {
  @Input() message?: string;
  @Input() data?: unknown;

  // Internal data từ modal service
  private modalData?: SimpleTestData;

  constructor() {
    console.log('SimpleTestComponent created');
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): SimpleTestResult {
    return {
      action: 'confirm',
      data: this.modalData || { message: this.message, data: this.data }
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return true; // Test component luôn valid
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: SimpleTestData): void {
    this.modalData = data;
    this.message = data.message;
    this.data = data.testData;
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    console.log('SimpleTestComponent modal opened');
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    console.log('SimpleTestComponent modal closing');
    return true; // Luôn cho phép đóng modal
  }
}
