import { Component, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Test data interface cho StrictTestModalComponent
 */
export interface StrictTestModalData {
  userName?: string;
  email?: string;
  message?: string;
}

/**
 * Test result interface cho StrictTestModalComponent
 */
export interface StrictTestModalResult {
  userName: string;
  email: string;
  termsAccepted: boolean;
  timestamp: Date;
}

/**
 * StrictTestModalComponent - Test component implement StrictModalComponent interface
 * 
 * <PERSON><PERSON><PERSON> đích: Test type safety của modal system mới
 */
@Component({
  selector: 'app-strict-test-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    FormsModule,
    TranslateModule
  ],
  template: `
    <div class="p-4">
      <h4 class="mb-3">🧪 Strict Test Modal Component</h4>
      
      <div class="mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Tên người dùng *</mat-label>
          <input matInput [ngModel]="userName()" (ngModelChange)="userName.set($event)" required>
        </mat-form-field>
      </div>

      <div class="mb-3">
        <mat-form-field appearance="outline" class="w-100">
          <mat-label>Email *</mat-label>
          <input matInput type="email" [ngModel]="email()" (ngModelChange)="email.set($event)" required>
        </mat-form-field>
      </div>

      <div class="mb-3">
        <mat-checkbox [ngModel]="termsAccepted()" (ngModelChange)="termsAccepted.set($event)">
          Tôi đồng ý với các điều khoản và điều kiện *
        </mat-checkbox>
      </div>
      
      <div class="mb-3">
        <strong>Trạng thái validation:</strong>
        <div [class]="isValidSignal() ? 'text-success' : 'text-danger'">
          {{ isValidSignal() ? '✅ Form hợp lệ - có thể submit' : '❌ Form chưa hợp lệ - nút Xác nhận sẽ bị disable' }}
        </div>
      </div>

      <div class="mb-3">
        <strong>Debug Info:</strong><br>
        Name valid: {{ isNameValidSignal() }}<br>
        Email valid: {{ isEmailValidSignal() }}<br>
        Terms agreed: {{ termsAccepted() }}<br>
        Overall valid: {{ isValidSignal() }}
      </div>
      
      @if (data()?.message) {
        <div class="alert alert-info">
          <strong>Message:</strong> {{ data()?.message }}
        </div>
      }
    </div>
  `,
  styles: [`
    .w-100 {
      width: 100%;
    }
    .text-success {
      color: #28a745;
    }
    .text-danger {
      color: #dc3545;
    }
  `]
})
export class StrictTestModalComponent implements StrictModalComponent<StrictTestModalData, StrictTestModalResult> {
  // Data signals - public để template có thể truy cập
  readonly data = signal<StrictTestModalData | undefined>(undefined);

  // Form fields - sử dụng signals để trigger change detection
  readonly userName = signal('');
  readonly email = signal('');
  readonly termsAccepted = signal(false);

  // Computed signals cho validation để trigger change detection
  readonly isNameValidSignal = computed(() => this.userName().trim().length >= 2);
  readonly isEmailValidSignal = computed(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(this.email());
  });
  readonly isValidSignal = computed(() =>
    this.isNameValidSignal() && this.isEmailValidSignal() && this.termsAccepted()
  );

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): StrictTestModalResult {
    return {
      userName: this.userName(),
      email: this.email(),
      termsAccepted: this.termsAccepted(),
      timestamp: new Date()
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.isValidSignal();
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: StrictTestModalData): void {
    this.data.set(data);

    // Populate form fields từ data
    if (data.userName) {
      this.userName.set(data.userName);
    }
    if (data.email) {
      this.email.set(data.email);
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    console.log('StrictTestModalComponent: Modal opened');
    
    // Focus vào field đầu tiên
    setTimeout(() => {
      const firstInput = document.querySelector('input[matInput]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean | Promise<boolean> {
    console.log('StrictTestModalComponent: Modal closing');
    
    // Kiểm tra có data chưa được save không
    if ((this.userName() || this.email()) && !this.isValid()) {
      const shouldClose = confirm('Bạn có chắc muốn đóng modal mà không hoàn thành form?');
      return shouldClose;
    }
    
    return true;
  }

  // Helper methods - public để template có thể truy cập (backward compatibility)
  isNameValid(): boolean {
    return this.isNameValidSignal();
  }

  isEmailValid(): boolean {
    return this.isEmailValidSignal();
  }
}
