import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue } from '@domain/entities/field.entity';
import { generatePlaceHolderData, getFieldIcon } from '../../../../../../utils/field.utils';
import { DateFieldTypes } from '@domain/entities/field.entity';

/**
 * Component chuyên biệt xử lý các date-based fields
 * 
 * Supported field types:
 * - date: Ngày (chỉ ngày)
 * - datetime: Ngày giờ (ngày + giờ)
 * 
 * Features:
 * - View mode: Hiển thị mock data với formatting
 * - Form mode: Date picker với validation
 * - Date formatting theo locale
 * - Min/max date validation
 * - Permission-based visibility và read-only state
 * - i18n support
 */
@Component({
  selector: 'app-date-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './date-field.component.html',
  styleUrls: ['./date-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DateFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  @Input({ required: true }) config!: FieldItemConfig;

  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: DateFieldTypes[] = ['date', 'datetime'];
    if (!supportedTypes.includes(this.config.field.type as DateFieldTypes)) {
      console.warn(`DateFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generatePlaceholderValue(): void {
    const fieldType = this.config.field.type as any;
    const placeHolderData = generatePlaceHolderData(fieldType, this.translateService);

    // Format placeholder data based on field type
    if (fieldType === 'datetime') {
      this.placeholderValue.set(this.formatDateTime(new Date(placeHolderData)));
    } else {
      this.placeholderValue.set(this.formatDate(new Date(placeHolderData)));
    }
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected override getValidators(): any[] {
    return this.getCommonValidators();
  }

 


  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Format date để hiển thị
   */
  formatDate(date: Date): string {
    return date.toLocaleDateString('vi-VN');
  }

  /**
   * Format datetime để hiển thị
   */
  formatDateTime(date: Date): string {
    return date.toLocaleString('vi-VN');
  }

  /**
   * Format display value cho date field
   */
  formatDisplayValue(value: FieldValue): string {
    if (value === null || value === undefined || value === '') {
      return '';
    }

    // Convert to Date object
    let date: Date;
    if (value instanceof Date) {
      date = value;
    } else {
      date = new Date(String(value));
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return String(value);
    }

    // Format based on field type
    switch (this.config.field.type) {
      case 'datetime':
        return this.formatDateTime(date);
      case 'date':
      default:
        return this.formatDate(date);
    }
  }
}
