// Section Component Styles
.section-container {
  // Section Header Styles
  .section-header {
    .section-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #111;
      padding: 0 0 10px 0;
    }
  }

  // Section Content Styles
  .section-content {
    // Không cần padding vì fields sẽ có margin riêng
  }
}

// Fields Container - kh<PERSON>i phục logic single/double column
.section-fields-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;

  // Base field item styling
  .field-item {
    flex: 1 1 auto;
    min-width: 0; // Cho phép field shrink khi cần
  }

  // Single Column Layout - mỗi field chiếm full width
  &.single-column {
    flex-direction: column;

    .field-item {
      flex: 1 1 100%;
      width: 100%;
    }
  }

  // Double Column Layout - fields tự động chia đều 2 cột
  &.double-column {
    flex-direction: row;

    .field-item {
      flex: 1 1 calc(50% - 0.75rem); // 50% width trừ đi gap
      max-width: calc(50% - 0.75rem);
    }
  }
}

// Responsive Design
@media (max-width: 992px) {
  // Force single column layout on tablet và nhỏ hơn
  .section-fields-container.double-column {
    flex-direction: column;
    gap: 1.5rem;

    .field-item {
      flex: 1 1 100%;
      max-width: 100%;
    }
  }
}

@media (max-width: 768px) {
  .section-container {
    .section-header {
      .section-title {
        font-size: 1rem;
      }
    }
  }

  // Ensure single column on mobile với gap nhỏ hơn
  .section-fields-container {
    gap: 1rem;

    &.double-column {
      flex-direction: column;

      .field-item {
        flex: 1 1 100%;
        max-width: 100%;
      }
    }
  }
}

@media (max-width: 576px) {
  .section-container {
    .section-header {
      .section-title {
        font-size: 0.95rem;
      }
    }
  }

  // Single column with reduced gap on small mobile
  .section-fields-container {
    gap: 0.75rem;

    &.double-column {
      flex-direction: column;

      .field-item {
        flex: 1 1 100%;
        max-width: 100%;
      }
    }
  }
}
