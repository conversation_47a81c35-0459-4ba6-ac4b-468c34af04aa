// StandardBottomSheet Module Exports
// Xuất tất cả components, services, interfaces và utilities

// Components
export { StandardBottomSheetWrapperComponent } from './standard-bottom-sheet.component';
// Note: StandardBottomSheetWrapperComponent là internal component, không nên sử dụng trực tiếp

// Service
export { StandardBottomSheetService } from './standard-bottom-sheet.service';

// Interfaces
export type { StandardBottomSheetConfig } from './models/standard-bottom-sheet-config.interface';
export type {
  StandardBottomSheetResult
} from '../../models/view/standard-bottom-sheet-result.interface';
export {
  isBottomSheetConfirmResult,
  isBottomSheetCancelResult,
  isBottomSheetCloseResult,
  isBottomSheetDismissResult,
  isBottomSheetCustomResult
} from '../../models/view/standard-bottom-sheet-result.interface';

// Re-export Angular Material BottomSheet types for convenience
export {
  MatBottomSheetRef,
  MAT_BOTTOM_SHEET_DATA
} from '@angular/material/bottom-sheet';
