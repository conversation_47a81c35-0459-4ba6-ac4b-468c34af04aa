import { TemplateRef, Type } from '@angular/core';
import { StrictModalComponent } from '../../../models/view/modal-component.interface';

/**
 * Interface cho actions configuration của StandardOverlayModal
 */
export interface StandardOverlayModalActions {
  /**
   * Có sử dụng default actions (Cancel/Confirm) không
   * @default true
   */
  useDefault?: boolean;

  /**
   * Custom actions template để override default actions
   */
  customActionsTemplate?: TemplateRef<any>;

  /**
   * Custom action handlers
   */
  customActions?: {
    [actionName: string]: (payload?: any) => void;
  };
}

/**
 * Interface cho positioning configuration của StandardOverlayModal
 */
export interface StandardOverlayModalPositioning {
  /**
   * Element reference để positioning modal (fallback positioning)
   */
  elementRef?: HTMLElement;

  /**
   * Vị trí modal overlay khi nhấn vào con trỏ chuột
   * - 'center': Modal center tại vị trí con trỏ chuột (legacy)
   * - 'left': Modal bắt đầu từ vị trí con trỏ chuột (left edge = mouse X, legacy)
   * - 'right': <PERSON><PERSON> kết thúc tại vị trí con trỏ chuột (right edge = mouse X, legacy)
   * - 'top-left': Modal hiển thị phía trên bên trái vị trí con trỏ chuột
   * - 'top-center': Modal hiển thị phía trên và center theo vị trí con trỏ chuột
   * - 'top-right': Modal hiển thị phía trên bên phải vị trí con trỏ chuột
   * - 'bottom-left': Modal hiển thị phía dưới bên trái vị trí con trỏ chuột
   * - 'bottom-center': Modal hiển thị phía dưới và center theo vị trí con trỏ chuột (default)
   * - 'bottom-right': Modal hiển thị phía dưới bên phải vị trí con trỏ chuột
   * @default 'bottom-center'
   */
  overlayModalPosition?: 'center' | 'left' | 'right' | 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';

  /**
   * Modal dimensions để tính toán positioning
   */
  dimensions?: {
    width?: number;
    height?: number;
    maxWidth?: string;
    maxHeight?: string;
    minWidth?: string;
    minHeight?: string;
  };

  /**
   * Offset từ vị trí trigger
   */
  offset?: {
    x?: number;
    y?: number;
  };

  /**
   * Margin tối thiểu từ viewport edges
   */
  viewportMargin?: number;
}

/**
 * Interface chính cho StandardOverlayModal configuration
 * Tương đồng với StandardDialogConfig và StandardBottomSheetConfig
 */
export interface StandardOverlayModalConfig<TData = any> {
  /**
   * Tiêu đề của modal - có thể là string hoặc TemplateRef
   */
  title?: string | TemplateRef<any>;

  /**
   * Data truyền vào component con
   */
  data?: TData;

  /**
   * Component sẽ được render trong modal
   */
  component?: Type<StrictModalComponent>;

  /**
   * Có cho phép đóng modal không (hiển thị nút X)
   * @default true
   */
  enableClose?: boolean;

  /**
   * Configuration cho actions (buttons)
   */
  actions?: StandardOverlayModalActions;

  /**
   * Configuration cho positioning
   */
  positioning?: StandardOverlayModalPositioning;

  /**
   * CSS classes để apply cho overlay container
   */
  panelClass?: string | string[];

  /**
   * CSS classes để apply cho backdrop
   */
  backdropClass?: string | string[];

  /**
   * Có cho phép click backdrop để đóng modal không
   * @default true
   */
  disableClose?: boolean;

  /**
   * Auto focus element khi modal mở
   * @default true
   */
  autoFocus?: boolean;

  /**
   * Restore focus về element trước đó khi modal đóng
   * @default true
   */
  restoreFocus?: boolean;

  /**
   * Có sử dụng keyboard navigation không (ESC để đóng)
   * @default true
   */
  hasKeyboardSupport?: boolean;

  /**
   * Z-index cho overlay
   */
  zIndex?: number;

  /**
   * Animation duration (ms)
   * @default 200
   */
  animationDuration?: number;

  /**
   * Custom scroll strategy
   */
  scrollStrategy?: 'reposition' | 'close' | 'block';
}

/**
 * Type helper cho StandardOverlayModalConfig với component type
 */
export interface StandardOverlayModalConfigWithComponent<
  TComponent extends StrictModalComponent = StrictModalComponent,
  TData = any
> extends StandardOverlayModalConfig<TData> {
  component: Type<TComponent>;
}

/**
 * Default configuration values
 */
export const DEFAULT_OVERLAY_MODAL_CONFIG: Partial<StandardOverlayModalConfig> = {
  enableClose: true,
  disableClose: false,
  autoFocus: true,
  restoreFocus: true,
  hasKeyboardSupport: true,
  animationDuration: 200,
  scrollStrategy: 'reposition',
  actions: {
    useDefault: true
  },
  positioning: {
    dimensions: {
      minWidth: '300px',
      maxWidth: '600px',
      maxHeight: '80vh'
    },
    offset: {
      x: 0,
      y: 8
    },
    viewportMargin: 16
  }
};
