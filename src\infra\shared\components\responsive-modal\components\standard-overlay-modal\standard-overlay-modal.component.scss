/* Standard Overlay Modal Wrapper Styles */

.standard-overlay-modal-container {
  @apply bg-white rounded-lg shadow-xl border border-gray-200;
  min-width: 300px;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  /* Dark mode support */
  .dark & {
    @apply bg-gray-800 border-gray-600;
  }
}

.standard-overlay-modal-header {
  @apply px-6 py-4 border-b border-gray-200 flex items-center justify-between;
  flex-shrink: 0;

  .dark & {
    @apply border-gray-600;
  }
}

.standard-overlay-modal-title {
  @apply flex-1;

  h4 {
    @apply text-lg font-semibold text-gray-900 mb-0;

    .dark & {
      @apply text-gray-100;
    }
  }
}

.standard-overlay-modal-content {
  @apply flex-1 overflow-auto;
  
  /* Scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    @apply bg-gray-100;
    
    .dark & {
      @apply bg-gray-700;
    }
  }

  &::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
    
    .dark & {
      @apply bg-gray-500;
    }

    &:hover {
      @apply bg-gray-400;
      
      .dark & {
        @apply bg-gray-400;
      }
    }
  }
}

.standard-overlay-modal-footer {
  @apply px-6 py-4 border-t border-gray-200;
  flex-shrink: 0;

  .dark & {
    @apply border-gray-600;
  }
}

.standard-overlay-modal-actions {
  @apply flex items-center justify-end gap-3;
}

/* Responsive Design */
@media (max-width: 640px) {
  .standard-overlay-modal-container {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    max-height: calc(100vh - 64px);
  }

  .standard-overlay-modal-header,
  .standard-overlay-modal-footer {
    @apply px-4 py-3;
  }

  .standard-overlay-modal-actions {
    @apply flex-col gap-2;

    .btn {
      @apply w-full;
    }
  }
}

/* Animation Classes */
.standard-overlay-modal-container {
  animation: overlayModalFadeIn 0.2s ease-out;
}

@keyframes overlayModalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Focus Management */
.standard-overlay-modal-container {
  &:focus {
    outline: none;
  }
}

/* Accessibility */
.standard-overlay-modal-container {
  /* Ensure proper focus management */
  &[tabindex="-1"] {
    outline: none;
  }
}

/* Button Styling Overrides */
.standard-overlay-modal-header .btn-icon {
  @apply w-8 h-8 p-0 flex items-center justify-center;
  
  i {
    @apply text-gray-500;
    
    .dark & {
      @apply text-gray-400;
    }
  }

  &:hover i {
    @apply text-gray-700;
    
    .dark & {
      @apply text-gray-200;
    }
  }
}

.standard-overlay-modal-actions .btn {
  @apply min-w-[100px];
  
  i {
    @apply me-2;
  }
}

/* Loading State */
.standard-overlay-modal-container.loading {
  .standard-overlay-modal-content {
    @apply flex items-center justify-center min-h-[200px];
    
    &::before {
      content: '';
      @apply w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin;
    }
  }
}

/* Error State */
.standard-overlay-modal-container.error {
  .standard-overlay-modal-content {
    @apply flex items-center justify-center min-h-[200px] text-red-500;
  }
}
