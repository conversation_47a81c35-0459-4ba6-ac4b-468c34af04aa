import { Component, signal, computed, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { TranslateModule } from '@ngx-translate/core';

import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';
import { SettingsListComponent, SettingChangeEvent, SettingsSaveEvent } from '@shared/components/settings/settings-list.component';
import { FlashMessageService } from '@core/services/flash_message.service';
import { 
  IntegrationAccountViewModalData, 
  IntegrationAccountViewModalResult 
} from './integration-account-view-modal.interfaces';
import { IntegrationAccountLog } from '@/mock/settings/integration_view_account.mock';

/**
 * Modal component để xem chi tiết ConnectedAccount theo IntegrationPlatform
 * Bao gồm 2 tabs: Settings và Logs
 */
@Component({
  selector: 'app-integration-account-view-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatDividerModule,
    ScrollingModule,
    TranslateModule,
    SettingsListComponent
  ],
  templateUrl: './integration-account-view-modal.component.html',
  styleUrls: ['./integration-account-view-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IntegrationAccountViewModalComponent implements StrictModalComponent<IntegrationAccountViewModalData, IntegrationAccountViewModalResult> {
  
  // Dữ liệu đầu vào từ modal service
  data = signal<IntegrationAccountViewModalData | null>(null);
  
  // Trạng thái tab hiện tại
  selectedTabIndex = signal<number>(0);
  
  // Trạng thái thay đổi settings
  settingsChanged = signal<boolean>(false);
  changedSettings = signal<Record<string, any>>({});
  
  // Computed properties
  platformName = computed(() => this.data()?.platformName || '');
  accountName = computed(() => this.data()?.accountName || '');
  config = computed(() => this.data()?.config);
  logs = computed(() => this.data()?.logs || []);
  
  // Computed cho title modal
  modalTitle = computed(() => {
    const platform = this.platformName();
    const account = this.accountName();
    if (account) {
      return `${platform} - ${account}`;
    }
    return platform;
  });
  
  constructor(
    private flashMessageService: FlashMessageService
  ) {}
  
  /**
   * Xử lý thay đổi tab
   * @param index Index của tab được chọn
   */
  onTabChange(index: number): void {
    this.selectedTabIndex.set(index);
  }
  
  /**
   * Xử lý thay đổi setting (khi autoSave = true)
   * @param event Sự kiện thay đổi setting
   */
  onSettingChange(event: SettingChangeEvent): void {
    // Cập nhật trạng thái thay đổi
    this.settingsChanged.set(true);
    
    // Cập nhật changed settings
    const currentChanged = this.changedSettings();
    this.changedSettings.set({
      ...currentChanged,
      [event.settingId]: event.value
    });
    
    console.log('Setting changed:', event);
  }
  
  /**
   * Xử lý lưu settings (khi autoSave = false)
   * @param event Sự kiện lưu settings
   */
  onSettingsSave(event: SettingsSaveEvent): void {
    // Cập nhật trạng thái thay đổi
    this.settingsChanged.set(true);
    this.changedSettings.set(event.changedSettings);
    
    // Hiển thị thông báo thành công
    this.flashMessageService.success('SETTINGS_SAVED_SUCCESSFULLY');
    
    console.log('Settings saved:', event);
  }
  
  /**
   * Xử lý hủy thay đổi settings
   */
  onSettingsCancel(): void {
    // Reset trạng thái thay đổi
    this.settingsChanged.set(false);
    this.changedSettings.set({});
    
    console.log('Settings changes cancelled');
  }
  
  /**
   * Lấy CSS class cho log entry dựa trên type
   * @param log Log entry
   * @returns CSS class string
   */
  getLogTypeClass(log: IntegrationAccountLog): string {
    switch (log.type) {
      case 'success':
        return 'text-success';
      case 'error':
        return 'text-danger';
      case 'warning':
        return 'text-warning';
      case 'info':
      default:
        return 'text-info';
    }
  }
  
  /**
   * Format thời gian log theo định dạng [HH:mm:ss DD/MM/YYYY]
   * @param date Date object
   * @returns Formatted time string
   */
  formatLogTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `[${hours}:${minutes}:${seconds} ${day}/${month}/${year}]`;
  }
  
  // ===== StrictModalComponent Interface Implementation =====
  
  /**
   * Cập nhật dữ liệu cho modal
   */
  updateData(data: IntegrationAccountViewModalData): void {
    this.data.set(data);
  }
  
  /**
   * Trả về kết quả từ modal component
   */
  getModalResult(): IntegrationAccountViewModalResult {
    return {
      settingsChanged: this.settingsChanged(),
      changedSettings: this.settingsChanged() ? this.changedSettings() : undefined,
      action: this.settingsChanged() ? 'save' : 'close'
    };
  }
  
  /**
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return true; // Modal này luôn valid
  }
  
  /**
   * Method được gọi khi modal được mở
   */
  onModalOpen?(): void {
    console.log('Integration Account View Modal opened');
  }
  
  /**
   * Method được gọi trước khi modal đóng
   */
  onModalClose?(): boolean | Promise<boolean> {
    console.log('Integration Account View Modal closing');
    return true;
  }
}
