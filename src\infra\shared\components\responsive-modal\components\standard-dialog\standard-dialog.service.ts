import { Injectable, TemplateRef, Type, ComponentRef } from '@angular/core';
import { MatDialog, MatDialogConfig, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { Observable, firstValueFrom } from 'rxjs';
import { filter } from 'rxjs/operators';

import { StandardDialogWrapperComponent } from './standard-dialog.component';
import { StandardDialogConfig } from '../../models/view/standard-dialog-config.interface';
import { StandardDialogResult } from '../../models/view/standard-dialog-result.interface';
import { StrictModalComponent } from '../../models/view/modal-component.interface';

/**
 * StandardDialogService - Drop-in replacement cho MatDialog với enhanced features
 *
 * Chức năng chính:
 * - Wrapper around MatDialog với enhanced configuration
 * - Dynamic component loading với auto-render features
 * - Type-safe methods với generics support
 * - Backward compatibility với MatDialog API
 * - Auto-inject title, close button, và default actions
 *
 * Sử dụng:
 * - Thay thế MatDialog injection bằng StandardDialogService
 * - API tương tự MatDialog nhưng với StandardDialogConfig
 * - Có thể mở bất kỳ component nào với enhanced features
 */
@Injectable({
  providedIn: 'root'
})
export class StandardDialogService {

  constructor(
    private readonly matDialog: MatDialog,
    private readonly translateService: TranslateService
  ) {}

  /**
   * Mở component với StandardDialogService enhanced features
   * Drop-in replacement cho MatDialog.open() với enhanced configuration
   *
   * Type Safety: Component phải implement StrictModalComponent interface
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param config - Cấu hình dialog theo StandardDialogConfig interface
   * @returns MatDialogRef với wrapper component
   */
  open<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    config?: StandardDialogConfig<TData>
  ): MatDialogRef<StandardDialogWrapperComponent<TComponent>, TResult> {
    // Tạo enhanced config với component reference
    const enhancedConfig = this.createEnhancedConfig(component, config);

    // Mở dialog với StandardDialogWrapperComponent
    const dialogRef = this.matDialog.open(StandardDialogWrapperComponent, enhancedConfig);

    return dialogRef as MatDialogRef<StandardDialogWrapperComponent<TComponent>, TResult>;
  }

  /**
   * Async version của open method để dễ sử dụng với async/await
   *
   * Type Safety: Component phải implement StrictModalComponent interface
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param config - Cấu hình dialog theo StandardDialogConfig interface
   * @returns Promise<TResult | undefined> - Kết quả khi dialog đóng
   */
  async openAsync<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    config?: StandardDialogConfig<TData>
  ): Promise<TResult | undefined> {
    const dialogRef = this.open<TData, TResult, TComponent>(component, config);
    return firstValueFrom(dialogRef.afterClosed());
  }

  /**
   * Mở dialog xác nhận đơn giản với title và message
   * Sử dụng SimpleConfirmComponent internal
   *
   * @param title - Tiêu đề dialog (sẽ được translate nếu là key)
   * @param message - Nội dung message (optional)
   * @returns Promise<StandardDialogResult | undefined>
   */
  async openConfirm(title: string, message?: string): Promise<StandardDialogResult | undefined> {
    // Sử dụng title được cung cấp hoặc fallback sang default
    const dialogTitle = title || this.translateService.instant('STANDARD_DIALOG.CONFIRM_DIALOG.DEFAULT_TITLE');

    const config: StandardDialogConfig<{ message?: string }> = {
      title: dialogTitle, // Sử dụng title mới
      actions: {
        useDefault: true
      },
      enableClose: true,
      data: { message },
      width: '400px'
    };

    // TODO: Sử dụng SimpleConfirmComponent (sẽ tạo sau)
    // return this.openAsync(SimpleConfirmComponent, config);
    throw new Error('SimpleConfirmComponent chưa được implement');
  }

  // TODO: Implement convenience methods với new API
  // Các methods này sẽ được implement sau khi có SimpleConfirmComponent và SimpleAlertComponent

  /*
  openAlert(title: string, message?: string): Promise<StandardDialogResult | undefined>
  openWithCustomTitle(titleTemplate: TemplateRef<any>, config: Partial<StandardDialogConfig>): Promise<StandardDialogResult | undefined>
  openWithCustomActions(title: string, actionsTemplate: TemplateRef<any>, config: Partial<StandardDialogConfig>): Promise<StandardDialogResult | undefined>
  */

  /**
   * Đóng tất cả dialogs đang mở
   */
  closeAll(): void {
    this.matDialog.closeAll();
  }

  /**
   * Kiểm tra xem có dialog nào đang mở không
   *
   * @returns boolean - true nếu có dialog đang mở
   */
  hasOpenDialogs(): boolean {
    return this.matDialog.openDialogs.length > 0;
  }

  /**
   * Tạo enhanced config cho StandardDialogWrapperComponent
   * Kết hợp StandardDialogConfig với component reference
   *
   * @param component - Component class để load động
   * @param config - StandardDialogConfig
   * @returns MatDialogConfig với enhanced data
   */
  private createEnhancedConfig<TComponent, TData = unknown>(
    component: Type<TComponent>,
    config?: StandardDialogConfig<TData>
  ): MatDialogConfig<StandardDialogConfig<TData> & { component: Type<TComponent> }> {
    // Default responsive width dựa trên viewport
    const defaultWidth = this.getResponsiveWidth();

    // Merge config với defaults sử dụng API mới
    const enhancedConfig: StandardDialogConfig<TData> = {
      // Sử dụng title từ BaseStandardModalConfig
      title: config?.title,
      enableClose: config?.enableClose ?? true,

      // Sử dụng actions config từ BaseStandardModalConfig
      actions: {
        useDefault: config?.actions?.useDefault ?? true,
        customActionsTemplate: config?.actions?.customActionsTemplate
      },

      data: config?.data || config?.componentData,
      width: config?.width || defaultWidth,
      height: config?.height,
      disableClose: config?.disableClose ?? false,
      panelClass: this.buildPanelClasses(config?.panelClass),
      position: config?.position,
      autoFocus: config?.autoFocus ?? true,
      restoreFocus: config?.restoreFocus ?? true,
      ...config // Spread remaining MatDialogConfig properties
    };

    // Tạo MatDialogConfig với component reference
    const matConfig: MatDialogConfig<StandardDialogConfig<TData> & { component: Type<TComponent> }> = {
      ...enhancedConfig,
      data: {
        ...enhancedConfig,
        component
      },
      role: 'dialog',
      ariaLabel: typeof enhancedConfig.title === 'string' ? enhancedConfig.title : 'Dialog'
    };

    return matConfig;
  }

  /**
   * Tính toán responsive width dựa trên viewport size
   *
   * @returns string - CSS width value
   */
  private getResponsiveWidth(): string {
    const viewportWidth = window.innerWidth;

    if (viewportWidth < 576) {
      return '95vw'; // Mobile: gần full width
    } else if (viewportWidth < 768) {
      return '80vw'; // Tablet nhỏ
    } else if (viewportWidth < 992) {
      return '60vw'; // Tablet lớn
    } else if (viewportWidth < 1200) {
      return '50vw'; // Desktop nhỏ
    } else {
      return '40vw'; // Desktop lớn
    }
  }

  /**
   * Xây dựng CSS classes cho dialog panel
   *
   * @param customClasses - Custom classes từ config
   * @returns string[] - Array of CSS classes
   */
  private buildPanelClasses(customClasses?: string | string[]): string[] {
    const baseClasses = ['standard-dialog-panel'];

    if (customClasses) {
      if (typeof customClasses === 'string') {
        baseClasses.push(customClasses);
      } else {
        baseClasses.push(...customClasses);
      }
    }

    return baseClasses;
  }
}
