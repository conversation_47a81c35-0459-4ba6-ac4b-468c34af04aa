import { Component, Inject, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { NoteModalData, NoteModalResult } from './models/note-modal.model';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

@Component({
  selector: 'app-note-modal',
  templateUrl: './note-modal.component.html',
  styleUrls: ['./note-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule
  ]
})
export class NoteModalComponent implements StrictModalComponent<NoteModalData, NoteModalResult> {
  // Component state
  selectedNoteType: 'internal' | 'public' = 'internal';
  noteContent: string = '';

  // Dữ liệu từ inject
  data: NoteModalData;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: NoteModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: NoteModalData,
    @Optional() private dialogRef?: MatDialogRef<NoteModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<NoteModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      internalNote: '',
      note: ''
    };

    // Khởi tạo nội dung dựa trên ghi chú hiện có
    if (this.data.internalNote) {
      this.selectedNoteType = 'internal';
      this.noteContent = this.data.internalNote;
    } else if (this.data.note) {
      this.selectedNoteType = 'public';
      this.noteContent = this.data.note;
    }
  }

  /**
   * Xử lý khi nhấn nút hủy
   */
  onCancel(): void {
    this.close(null);
  }

  /**
   * Xử lý khi nhấn nút lưu
   */
  onSave(): void {
    const result: NoteModalResult = {
      internalNote: this.selectedNoteType === 'internal' ? this.noteContent : '',
      note: this.selectedNoteType === 'public' ? this.noteContent : ''
    };
    this.close(result);
  }

  /**
   * Đóng modal
   */
  private close(result: NoteModalResult | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): NoteModalResult {
    return {
      internalNote: this.selectedNoteType === 'internal' ? this.noteContent : '',
      note: this.selectedNoteType === 'public' ? this.noteContent : ''
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // Note modal luôn valid vì có thể để trống
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: NoteModalData): void {
    this.data = data;

    // Cập nhật nội dung dựa trên ghi chú hiện có
    if (data.internalNote) {
      this.selectedNoteType = 'internal';
      this.noteContent = data.internalNote;
    } else if (data.note) {
      this.selectedNoteType = 'public';
      this.noteContent = data.note;
    } else {
      this.selectedNoteType = 'internal';
      this.noteContent = '';
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào textarea khi modal mở
    setTimeout(() => {
      const textarea = document.querySelector('textarea[matInput]') as HTMLTextAreaElement;
      if (textarea) {
        textarea.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
