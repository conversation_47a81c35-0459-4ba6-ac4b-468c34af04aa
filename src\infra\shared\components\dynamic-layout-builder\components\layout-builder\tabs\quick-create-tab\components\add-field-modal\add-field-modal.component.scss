// Add Field Modal Styles
.add-field-modal-container {
  min-width: 320px;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  // Modal Header
  .modal-header {
    flex-shrink: 0;
    
    .modal-title {
      color: var(--bs-primary);
      font-weight: 600;
      
      mat-icon {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }
    
    .modal-description {
      font-size: 0.875rem;
      line-height: 1.4;
    }
    
    .close-button {
      flex-shrink: 0;
      
      &:hover {
        background-color: rgba(var(--bs-danger-rgb), 0.1);
        color: var(--bs-danger);
      }
    }
  }

  // Modal Content
  .modal-content {
    flex: 1;
    overflow-y: auto;
    padding-right: 4px; // Space for scrollbar
    margin-right: -4px;
    
    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 4px;
    }
    
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    
    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
      
      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
  }

  // Section Header
  .section-header {
    .small-icon {
      font-size: 1rem;
      width: 1rem;
      height: 1rem;
    }
    
    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
  }

  // Fields Grid
  .fields-grid {
    display: grid;
    gap: 8px;
    
    // Single column layout for better mobile experience
    grid-template-columns: 1fr;
    
    @media (min-width: 576px) {
      // Two columns on larger screens
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
  }

  // Field Item Button
  .field-item-button {
    padding: 12px 16px;
    border: 1px solid var(--bs-border-color);
    border-radius: 8px;
    background: white;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    min-height: 60px;
    
    &:hover {
      border-color: var(--bs-primary);
      background: rgba(var(--bs-primary-rgb), 0.05);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    .field-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      flex-shrink: 0;
    }
    
    .field-info {
      .field-label {
        font-size: 0.875rem;
        line-height: 1.3;
        margin-bottom: 2px;
      }
      
      .field-type {
        font-size: 0.75rem;
        line-height: 1.2;
      }
    }
    
    .add-icon {
      font-size: 1.25rem;
      width: 1.25rem;
      height: 1.25rem;
      flex-shrink: 0;
      opacity: 0.7;
      transition: opacity 0.2s ease;
    }
    
    &:hover .add-icon {
      opacity: 1;
    }
  }

  // No Fields Messages
  .no-fields-message,
  .no-fields-available {
    mat-icon {
      font-size: 2rem;
      width: 2rem;
      height: 2rem;
      opacity: 0.5;
    }
  }
  
  .no-fields-available {
    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
    }
    
    h5 {
      font-weight: 500;
      margin-bottom: 0.5rem;
    }
  }

  // Modal Footer
  .modal-footer {
    flex-shrink: 0;
    
    .cancel-button {
      mat-icon {
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .add-field-modal-container {
    .field-item-button {
      background: var(--bs-dark);
      border-color: var(--bs-border-color-translucent);
      color: var(--bs-light);
      
      &:hover {
        background: rgba(var(--bs-primary-rgb), 0.1);
      }
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .add-field-modal-container {
    .field-item-button {
      border-width: 2px;
      
      &:hover {
        border-width: 2px;
      }
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .add-field-modal-container {
    .field-item-button {
      transition: none;
      
      &:hover {
        transform: none;
      }
    }
  }
}

// Mobile optimizations
@media (max-width: 575px) {
  .add-field-modal-container {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    
    .modal-header {
      .modal-title {
        font-size: 1.1rem;
      }
    }
    
    .field-item-button {
      padding: 10px 12px;
      min-height: 56px;
      
      .field-icon {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
}
