import { Injectable, Type, ComponentRef, ViewContainerRef, TemplateRef, ElementRef } from '@angular/core';
import { Overlay, OverlayRef, ConnectedPosition } from '@angular/cdk/overlay';
import { TemplatePortal, ComponentPortal } from '@angular/cdk/portal';
import { Observable, Subject } from 'rxjs';
import { take } from 'rxjs/operators';

import { StrictModalComponent } from '../standard-dialog/interfaces/modal-component.interface';
import { StandardOverlayModalConfig } from './models/standard-overlay-modal-config.interface';

/**
 * ConnectedOverlayModalService - Service sử dụng cdkConnectedOverlay pattern
 * 
 * Approach này tạo overlay kết nối trực tiếp với element trigger, hoạt động giống mat-menu:
 * - Overlay được append vào parent element thay vì body
 * - Sử dụng position: absolute relative to connected element
 * - Không bị ảnh hưởng bởi scroll trang
 * - Tự động tính toán vị trí tối ưu
 */
@Injectable({
  providedIn: 'root'
})
export class ConnectedOverlayModalService {
  constructor(private overlay: Overlay) {}

  /**
   * Mở modal với connected overlay pattern (giống mat-menu)
   *
   * @param component - Component sẽ được render trong modal
   * @param connectedElement - HTMLElement để kết nối modal
   * @param config - Configuration cho modal
   * @param mouseEvent - MouseEvent để lấy vị trí click chuột (optional)
   * @returns Observable<TResult | undefined> - Kết quả từ modal
   */
  openWithConnectedElement<
    TComponent extends StrictModalComponent = StrictModalComponent,
    TData = any,
    TResult = any
  >(
    component: Type<TComponent>,
    connectedElement: HTMLElement,
    config: StandardOverlayModalConfig<TData> = {},
    mouseEvent?: MouseEvent
  ): Observable<TResult | undefined> {
    // Tạo virtual element tại vị trí click chuột nếu có mouseEvent
    const targetElement = mouseEvent
      ? this.createVirtualElementAtMousePosition(mouseEvent, connectedElement)
      : connectedElement;

    // Tạo overlay với connected positioning
    const overlayRef = this.createConnectedOverlay(targetElement, config);

    // Tạo component portal
    const portal = new ComponentPortal(component);
    const componentRef = overlayRef.attach(portal);

    // Inject data vào component
    if (config.data) {
      Object.assign(componentRef.instance, { data: config.data });
    }

    // Setup result handling với cleanup cho virtual element
    return this.setupResultHandling<TResult>(overlayRef, componentRef, targetElement !== connectedElement ? targetElement : undefined);
  }

  /**
   * Mở modal với template và connected element
   */
  openTemplateWithConnectedElement<TResult = any>(
    template: TemplateRef<any>,
    viewContainerRef: ViewContainerRef,
    connectedElement: HTMLElement,
    config: StandardOverlayModalConfig = {}
  ): Observable<TResult | undefined> {
    // Tạo overlay với connected positioning
    const overlayRef = this.createConnectedOverlay(connectedElement, config);
    
    // Tạo template portal
    const portal = new TemplatePortal(template, viewContainerRef);
    overlayRef.attach(portal);

    // Setup result handling cho template
    return this.setupTemplateResultHandling<TResult>(overlayRef);
  }

  /**
   * Tạo virtual element tại vị trí click chuột để sử dụng làm anchor point
   */
  private createVirtualElementAtMousePosition(mouseEvent: MouseEvent, parentElement: HTMLElement): HTMLElement {
    // Tạo invisible div tại vị trí click chuột
    const virtualElement = document.createElement('div');
    virtualElement.style.position = 'absolute';
    virtualElement.style.width = '1px';
    virtualElement.style.height = '1px';
    virtualElement.style.visibility = 'hidden';
    virtualElement.style.pointerEvents = 'none';
    virtualElement.className = 'virtual-mouse-anchor';

    // Tính toán vị trí relative to parent element
    const parentRect = parentElement.getBoundingClientRect();
    const parentStyle = window.getComputedStyle(parentElement);
    const parentPosition = parentStyle.position;

    if (parentPosition === 'relative' || parentPosition === 'absolute' || parentPosition === 'fixed') {
      // Parent có positioning, sử dụng coordinates relative to parent
      virtualElement.style.left = `${mouseEvent.clientX - parentRect.left}px`;
      virtualElement.style.top = `${mouseEvent.clientY - parentRect.top}px`;
      parentElement.appendChild(virtualElement);
    } else {
      // Parent không có positioning, append vào body với absolute coordinates
      virtualElement.style.left = `${mouseEvent.clientX}px`;
      virtualElement.style.top = `${mouseEvent.clientY}px`;
      virtualElement.style.zIndex = '9999';
      document.body.appendChild(virtualElement);
    }

    return virtualElement;
  }

  /**
   * Tạo connected overlay với positioning giống mat-menu
   */
  private createConnectedOverlay(
    connectedElement: HTMLElement,
    config: StandardOverlayModalConfig
  ): OverlayRef {
    const positioning = config.positioning || {};
    const overlayModalPosition = positioning.overlayModalPosition || 'bottom-center';

    // Tính toán connected positions dựa trên overlayModalPosition
    const positions = this.calculateConnectedPositions(overlayModalPosition);

    // Tạo position strategy với flexibleConnectedTo
    const positionStrategy = this.overlay
      .position()
      .flexibleConnectedTo(connectedElement)
      .withPositions(positions)
      .withPush(false) // Tự động đẩy modal khi vượt viewport
      .withViewportMargin(positioning.viewportMargin || 16);

    // Tạo scroll strategy - sử dụng 'reposition' để modal tự động điều chỉnh vị trí khi scroll
    let scrollStrategy;
    switch (config.scrollStrategy) {
      case 'close':
        scrollStrategy = this.overlay.scrollStrategies.close();
        break;
      case 'block':
        scrollStrategy = this.overlay.scrollStrategies.block();
        break;
      case 'reposition':
        scrollStrategy = this.overlay.scrollStrategies.reposition();
        break;
      default:
        // Mặc định sử dụng 'reposition' để modal tự động điều chỉnh vị trí khi scroll
        scrollStrategy = this.overlay.scrollStrategies.noop();
        break;
    }

    // Tạo overlay config
    const overlayConfig = {
      hasBackdrop: !config.disableClose, // Sử dụng disableClose thay vì hasBackdrop
      backdropClass: config.backdropClass || 'cdk-overlay-transparent-backdrop',
      panelClass: config.panelClass || 'connected-overlay-modal-panel',
      positionStrategy,
      scrollStrategy,
      disposeOnNavigation: true,
      width: positioning.dimensions?.width || 'auto',
      height: positioning.dimensions?.height || 'auto',
      maxWidth: positioning.dimensions?.maxWidth || '600px',
      maxHeight: positioning.dimensions?.maxHeight || '80vh',
      minWidth: positioning.dimensions?.minWidth || '300px',
      minHeight: positioning.dimensions?.minHeight || 'auto'
    };

    return this.overlay.create(overlayConfig);
  }

  /**
   * Tính toán connected positions dựa trên overlayModalPosition
   */
  private calculateConnectedPositions(overlayModalPosition: string): ConnectedPosition[] {
    const positions: ConnectedPosition[] = [];

    switch (overlayModalPosition) {
      case 'top-left':
        positions.push(
          { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' },
          { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' }
        );
        break;

      case 'top-center':
        positions.push(
          { originX: 'center', originY: 'top', overlayX: 'center', overlayY: 'bottom' },
          { originX: 'center', originY: 'bottom', overlayX: 'center', overlayY: 'top' }
        );
        break;

      case 'top-right':
        positions.push(
          { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' },
          { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' }
        );
        break;

      case 'bottom-left':
        positions.push(
          { originX: 'start', originY: 'bottom', overlayX: 'start', overlayY: 'top' },
          { originX: 'start', originY: 'top', overlayX: 'start', overlayY: 'bottom' }
        );
        break;

      case 'bottom-right':
        positions.push(
          { originX: 'end', originY: 'bottom', overlayX: 'end', overlayY: 'top' },
          { originX: 'end', originY: 'top', overlayX: 'end', overlayY: 'bottom' }
        );
        break;

      case 'left':
        positions.push(
          { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'center' },
          { originX: 'end', originY: 'center', overlayX: 'start', overlayY: 'center' }
        );
        break;

      case 'right':
        positions.push(
          { originX: 'end', originY: 'center', overlayX: 'start', overlayY: 'center' },
          { originX: 'start', originY: 'center', overlayX: 'end', overlayY: 'center' }
        );
        break;

      case 'center':
        positions.push(
          { originX: 'center', originY: 'center', overlayX: 'center', overlayY: 'center' }
        );
        break;

      case 'bottom-center':
      default:
        positions.push(
          { originX: 'center', originY: 'bottom', overlayX: 'center', overlayY: 'top' },
          { originX: 'center', originY: 'top', overlayX: 'center', overlayY: 'bottom' }
        );
        break;
    }

    return positions;
  }

  /**
   * Setup result handling cho component modal
   */
  private setupResultHandling<TResult>(
    overlayRef: OverlayRef,
    componentRef: ComponentRef<StrictModalComponent>,
    virtualElement?: HTMLElement
  ): Observable<TResult | undefined> {
    const resultSubject = new Subject<TResult | undefined>();

    // Listen for modal close
    const component = componentRef.instance;
    
    // Handle backdrop click
    if (overlayRef.getConfig().hasBackdrop) {
      overlayRef.backdropClick().pipe(take(1)).subscribe(() => {
        const result = component.getModalResult ? component.getModalResult() as TResult : undefined;
        resultSubject.next(result);
        resultSubject.complete();
        overlayRef.dispose();
      });
    }

    // Handle component result (nếu component có method để emit result)
    // Note: onModalClose thường là method để override, không phải property
    if (component && typeof (component as any).closeModal === 'function') {
      // Wrap closeModal để capture result
      const originalCloseModal = (component as any).closeModal.bind(component);
      (component as any).closeModal = (result?: TResult) => {
        resultSubject.next(result);
        resultSubject.complete();
        overlayRef.dispose();
        return originalCloseModal(result);
      };
    }

    // Cleanup khi overlay bị dispose
    overlayRef.detachments().pipe(take(1)).subscribe(() => {
      // Cleanup virtual element nếu có
      if (virtualElement && virtualElement.parentNode) {
        virtualElement.parentNode.removeChild(virtualElement);
      }

      if (!resultSubject.closed) {
        resultSubject.next(undefined);
        resultSubject.complete();
      }
    });

    return resultSubject.asObservable();
  }

  /**
   * Setup result handling cho template modal
   */
  private setupTemplateResultHandling<TResult>(
    overlayRef: OverlayRef
  ): Observable<TResult | undefined> {
    const resultSubject = new Subject<TResult | undefined>();

    // Handle backdrop click
    if (overlayRef.getConfig().hasBackdrop) {
      overlayRef.backdropClick().pipe(take(1)).subscribe(() => {
        resultSubject.next(undefined);
        resultSubject.complete();
        overlayRef.dispose();
      });
    }

    // Cleanup khi overlay bị dispose
    overlayRef.detachments().pipe(take(1)).subscribe(() => {
      if (!resultSubject.closed) {
        resultSubject.next(undefined);
        resultSubject.complete();
      }
    });

    return resultSubject.asObservable();
  }
}
