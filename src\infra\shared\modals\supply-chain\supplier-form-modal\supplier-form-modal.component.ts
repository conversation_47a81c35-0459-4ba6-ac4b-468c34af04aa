import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Supplier } from 'salehub_shared_contracts/requests/shared/supplier';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface SupplierFormModalData {
  supplier?: Supplier;
}

/**
 * Kiểu dữ liệu trả về từ modal
 */
export type SupplierFormModalResult = Supplier | undefined;

@Component({
  selector: 'app-supplier-form-modal',
  templateUrl: './supplier-form-modal.component.html',
  styleUrls: ['./supplier-form-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule
  ]
})
export class SupplierFormModalComponent implements OnInit, StrictModalComponent<SupplierFormModalData, SupplierFormModalResult> {
  supplierForm: FormGroup;
  dialogTitle = 'Thêm nhà cung cấp mới';
  data: SupplierFormModalData;

  constructor(
    private fb: FormBuilder,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: SupplierFormModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: SupplierFormModalData,
    @Optional() private dialogRef?: MatDialogRef<SupplierFormModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<SupplierFormModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {};

    this.supplierForm = this.fb.group({
      _id: ['', []],
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      contactPerson: ['', []],
      phone: ['', []],
      email: ['', [Validators.email]],
      address: ['', []],
      taxCode: ['', []],
      paymentTerm: ['', []],
      isActive: [true, []]
    });
  }

  ngOnInit(): void {
    if (this.data && this.data.supplier) {
      this.dialogTitle = 'Chỉnh sửa nhà cung cấp';
      this.supplierForm.patchValue(this.data.supplier);
    }
  }

  onNoClick(): void {
    this.close();
  }

  close(result?: any): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  onSubmit(): void {
    if (this.supplierForm.valid) {
      const formValue = this.supplierForm.value;

      // Tạo ID mới nếu là thêm mới
      if (!formValue._id) {
        formValue._id = `supplier_${Date.now().toString()}`;
      }

      this.close(formValue);
    } else {
      // Đánh dấu tất cả các trường là đã chạm để hiển thị lỗi
      Object.keys(this.supplierForm.controls).forEach(key => {
        const control = this.supplierForm.get(key);
        control?.markAsTouched();
      });
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): SupplierFormModalResult {
    if (!this.supplierForm.valid) {
      return undefined;
    }

    const formValue = this.supplierForm.value;

    // Tạo ID mới nếu là thêm mới
    if (!formValue._id) {
      formValue._id = `supplier_${Date.now().toString()}`;
    }

    return formValue as Supplier;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.supplierForm.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: SupplierFormModalData): void {
    this.data = data;

    if (data.supplier) {
      this.dialogTitle = 'Chỉnh sửa nhà cung cấp';
      this.supplierForm.patchValue(data.supplier);
    } else {
      this.dialogTitle = 'Thêm nhà cung cấp mới';
      this.supplierForm.reset();
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào field đầu tiên khi modal mở
    setTimeout(() => {
      const firstInput = document.querySelector('input[formControlName="name"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Kiểm tra xem có thay đổi nào chưa được lưu không
    if (this.supplierForm.dirty && this.supplierForm.valid) {
      // Có thể thêm logic confirm nếu cần
      return true;
    }
    return true; // Luôn cho phép đóng modal
  }
}
