/**
 * Interface bắt buộc cho tất cả modal components
 *
 * Đảm bảo wrapper components (Dialog/BottomSheet) có thể lấy kết quả
 * từ component con một cách nhất quán
 */
export interface ModalComponent<T = unknown> {
  /**
   * Method bắt buộc để trả về kết quả từ modal component
   * Được gọi bởi wrapper component khi user click Confirm/Save/etc
   *
   * @returns Dữ liệu kết quả từ modal component
   */
  getModalResult(): T;
}

/**
 * Interface mở rộng cho modal components có thể nhận data
 */
export interface ModalComponentWithData<TData = unknown, TResult = unknown> extends ModalComponent<TResult> {
  /**
   * Method optional để cập nhật data cho component
   * Được gọi bởi wrapper component khi inject data
   *
   * @param data Dữ liệu được truyền vào component
   */
  updateData?(data: TData): void;
}

/**
 * Interface cho modal components có thể tự validate
 */
export interface ValidatableModalComponent<T = unknown> extends ModalComponent<T> {
  /**
   * Method để kiểm tra component có valid không
   * Wrapper có thể disable Confirm button nếu invalid
   *
   * @returns true nếu component valid, false nếu không
   */
  isValid(): boolean;
}

/**
 * Interface đầy đủ cho modal components advanced
 */
export interface AdvancedModalComponent<TData = unknown, TResult = unknown>
  extends ModalComponentWithData<TData, TResult>, ValidatableModalComponent<TResult> {

  /**
   * Method optional được gọi khi modal được mở
   */
  onModalOpen?(): void;

  /**
   * Method optional được gọi trước khi modal đóng
   * Có thể return false để prevent đóng modal
   */
  onModalClose?(): boolean | Promise<boolean>;
}

/**
 * Type helper để check xem component có implement ModalComponent không
 */
export function isModalComponent(component: unknown): component is ModalComponent {
  return component !== null &&
         component !== undefined &&
         typeof component === 'object' &&
         'getModalResult' in component &&
         typeof (component as Record<string, unknown>).getModalResult === 'function';
}

/**
 * Type helper để check xem component có implement ModalComponentWithData không
 */
export function isModalComponentWithData(component: unknown): component is ModalComponentWithData {
  return isModalComponent(component) && 'updateData' in component && typeof component.updateData === 'function';
}

/**
 * Type helper để check xem component có implement ValidatableModalComponent không
 */
export function isValidatableModalComponent(component: unknown): component is ValidatableModalComponent {
  return isModalComponent(component) && 'isValid' in component && typeof component.isValid === 'function';
}

/**
 * StrictModalComponent - Interface mới cho type-safe modal system
 *
 * Sử dụng cho các component mới muốn có type safety đầy đủ
 * Backward compatible với AdvancedModalComponent
 */
export interface StrictModalComponent<TData = unknown, TResult = unknown> extends AdvancedModalComponent<TData, TResult> {
  /**
   * Method bắt buộc để trả về kết quả từ modal component
   */
  getModalResult(): TResult;

  /**
   * Method bắt buộc để validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean;

  /**
   * Method để cập nhật data cho component (optional nhưng recommended)
   */
  updateData?(data: TData): void;

  /**
   * Method được gọi khi modal được mở (optional)
   */
  onModalOpen?(): void;

  /**
   * Method được gọi trước khi modal đóng (optional)
   */
  onModalClose?(): boolean | Promise<boolean>;
}

/**
 * Type helper để check xem component có implement StrictModalComponent không
 */
export function isStrictModalComponent(component: unknown): component is StrictModalComponent {
  return isModalComponent(component) &&
         isValidatableModalComponent(component) &&
         typeof component.getModalResult === 'function' &&
         typeof component.isValid === 'function';
}
