.responsive-bottom-sheet {
  .mat-bottom-sheet-container {
    padding: 0;
  }
}

.standard-bottom-sheet-container {
  // Container ch<PERSON>h của bottom sheet
  min-height: 200px;
  max-height: 90vh;
  width: 100%;
}

// Bottom Sheet Title Styling
.standard-bottom-sheet-title {
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  margin-bottom: 16px;

  .title-content {
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.87);
      margin: 0;
      line-height: 1.4;

      // Responsive font size
      @media (max-width: 575px) {
        font-size: 1.1rem;
      }
    }
  }

  .close-button {
    color: rgba(0, 0, 0, 0.54);
    width: 32px;
    height: 32px;
    line-height: 32px;

    &:hover {
      color: rgba(0, 0, 0, 0.87);
      background-color: rgba(0, 0, 0, 0.04);
    }

    mat-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
    }
  }
}

// Bottom Sheet Content Styling
.standard-bottom-sheet-content {
  flex: 1;
  overflow-y: auto;
  max-height: 60vh;

  // Responsive max height
  @media (max-width: 575px) {
    max-height: 50vh;
  }

  // Custom scrollbar cho webkit browsers
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Bottom Sheet Actions Styling
.standard-bottom-sheet-actions {
  padding-top: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.12);
  margin-top: 16px;

  // Responsive layout
  @media (max-width: 575px) {
    flex-direction: column-reverse;
    gap: 8px !important;

    .default-actions,
    .custom-actions,
    .fallback-actions {
      width: 100%;

      button {
        width: 100%;
        margin: 0;
      }
    }

    .default-actions {
      flex-direction: column-reverse;
      gap: 8px;
    }
  }

  // Button styling
  button {
    min-width: 80px;
    height: 36px;

    &.cancel-button {
      color: rgba(0, 0, 0, 0.54);

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }
    }

    &.confirm-button {
      font-weight: 500;
    }

    // Responsive button size
    @media (max-width: 575px) {
      height: 40px;
      font-size: 0.9rem;
    }
  }
}

// Gap utilities cho actions
.gap-2 {
  gap: 8px;
}

// Custom focus styles cho accessibility
button:focus-visible {
  outline: 2px solid #1976d2;
  outline-offset: 2px;
}

// Animation cho bottom sheet entrance
.standard-bottom-sheet-container {
  animation: bottomSheetEnter 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes bottomSheetEnter {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .standard-bottom-sheet-title {
    border-bottom-color: rgba(255, 255, 255, 0.12);

    .title-content h3 {
      color: rgba(255, 255, 255, 0.87);
    }

    .close-button {
      color: rgba(255, 255, 255, 0.54);

      &:hover {
        color: rgba(255, 255, 255, 0.87);
        background-color: rgba(255, 255, 255, 0.04);
      }
    }
  }

  .standard-bottom-sheet-actions {
    border-top-color: rgba(255, 255, 255, 0.12);

    .cancel-button {
      color: rgba(255, 255, 255, 0.54);

      &:hover {
        background-color: rgba(255, 255, 255, 0.04);
      }
    }
  }
}

// Swipe indicator cho mobile
.standard-bottom-sheet-container::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 2px;

  @media (min-width: 768px) {
    display: none;
  }
}

// Responsive container adjustments
@media (max-width: 575px) {
  .standard-bottom-sheet-container {
    padding-top: 20px; // Extra space for swipe indicator
  }
}
