/**
 * Kết quả trả về từ StandardDialogComponent
 * Chứa thông tin về hành động người dùng đã thực hiện và dữ liệu kèm theo
 */
export interface StandardDialogResult<T = any> {
  /**
   * Loại hành động mà người dùng đã thực hiện
   * - 'confirm': Người dùng nhấn nút Xác nhận
   * - 'cancel': Người dùng nhấn nút Hủy
   * - 'close': Người dùng đóng dialog bằng nút X hoặc ESC
   * - 'custom': Hành động tùy chỉnh từ customActionsTemplate
   */
  action: 'confirm' | 'cancel' | 'close' | 'custom';

  /**
   * Dữ liệu kèm theo kết quả (nếu có)
   * C<PERSON> thể là form data, selection data, hoặc bất kỳ dữ liệu nào
   */
  data?: T;

  /**
   * Thông tin bổ sung về hành động tùy chỉnh
   * Chỉ được sử dụng khi action = 'custom'
   */
  customAction?: {
    /**
     * Tên của hành động tùy chỉnh
     */
    name: string;

    /**
     * Dữ liệu bổ sung cho hành động tùy chỉnh
     */
    payload?: any;
  };
}

/**
 * Type guard để kiểm tra xem result có phải là confirm action không
 */
export function isConfirmResult(result: StandardDialogResult): boolean {
  return result.action === 'confirm';
}

/**
 * Type guard để kiểm tra xem result có phải là cancel action không
 */
export function isCancelResult(result: StandardDialogResult): boolean {
  return result.action === 'cancel';
}

/**
 * Type guard để kiểm tra xem result có phải là close action không
 */
export function isCloseResult(result: StandardDialogResult): boolean {
  return result.action === 'close';
}

/**
 * Type guard để kiểm tra xem result có phải là custom action không
 */
export function isCustomResult(result: StandardDialogResult): boolean {
  return result.action === 'custom';
}
