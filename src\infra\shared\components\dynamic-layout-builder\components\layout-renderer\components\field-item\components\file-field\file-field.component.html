@if (isVisible()) {
  <div [class]="config.currentViewMode === 'form' ? 'field-row-form' : 'field-row-view'">

    <!-- Label Column -->
    <div class="field-label-column">
      <label class="field-label-text">
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>

      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Value Column -->
    <div class="field-value-column">

      <!-- PREVIEW MODE: Display placeholder data -->
      @if (config.currentViewMode === 'preview') {
        <div class="field-view-value" [class.empty]="!placeholderValue()">
          <span>{{ placeholderValue() || ('DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate) }}</span>
        </div>
      }

      <!-- VIEW MODE: Display actual values hoặc inline edit -->
      @else if (config.currentViewMode === 'view') {
        <div class="field-view-value" [class.empty]="!currentValue()">
          @if (currentValue()) {
            <span>{{ currentValue() }}</span>
          } @else {
            <span class="empty">{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate }}</span>
          }

          <!-- Edit Actions - File field không support inline edit, chỉ hiển thị thông báo -->
          @if (canEditField()) {
            <div class="field-edit-actions">
              <span class="text-muted small">{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.FILE_EDIT_IN_FORM' | translate }}</span>
            </div>
          }
        </div>
      }

      @else {
        <div class="file-upload-container">
          <input
            type="file"
            #fileInput
            (change)="onFileSelected($event)"
            [accept]="getAcceptedTypes()"
            [disabled]="isReadOnlyState()"
            style="display: none;">

          <button
            mat-raised-button
            color="primary"
            [disabled]="isReadOnly()"
            (click)="fileInput.click()"
            [class.disabled]="isReadOnly()">
            <mat-icon>{{ getFieldIcon() }}</mat-icon>
            {{ getPlaceholder() | translate }}
          </button>

          @if (formControl().value) {
            <div class="selected-file">
              <mat-icon class="file-icon">description</mat-icon>
              <span>{{ formControl().value }}</span>
            </div>
          }
        </div>
      }

      <!-- Field-Level Validation Errors -->
      @if (shouldShowValidationErrors()) {
        <div class="field-validation-errors" aria-live="polite">
          @for (error of getValidationErrors(); track error) {
            <div class="validation-error-item">
              <mat-icon class="error-icon">error</mat-icon>
              <span class="error-message">{{ error | translate }}</span>
            </div>
          }
        </div>
      }

      <!-- Field Tooltip/Description -->
      @if (config.field.tooltip) {
        <div class="field-tooltip">
          <small class="text-muted">{{ config.field.tooltip }}</small>
        </div>
      }
    </div>
  </div>
}
