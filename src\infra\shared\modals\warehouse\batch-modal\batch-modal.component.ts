import { ChangeDetectionStrategy, Component, Inject, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu lô hàng
 */
export interface BatchData {
  batchNumber: string;
  manufacturingDate: Date;
  expiryDate: Date | null;
  quantity: number | null;
}


/**
 * Component modal quản lý thông tin lô hàng
 */
@Component({
  selector: 'app-batch-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    TranslateModule,
  ],
  templateUrl: './batch-modal.component.html',
  styleUrls: ['./batch-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BatchModalComponent implements OnInit, StrictModalComponent<BatchData, BatchData> {
  // Form group quản lý dữ liệu lô hàng
  batchForm!: FormGroup;

  // Dữ liệu đầu vào
  data: BatchData | null = null;

  private fb = inject(FormBuilder);

  constructor(
    @Inject(MAT_DIALOG_DATA) private dialogData: BatchData | null,
    @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: BatchData | null,
    private dialogRef?: MatDialogRef<BatchModalComponent>,
    private bottomSheetRef?: MatBottomSheetRef<BatchModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialog hoặc bottom sheet
    this.data = dialogData || bottomSheetData;
  }

  ngOnInit(): void {
    // Khởi tạo form với các validators
    this.initForm();

    // Nếu có dữ liệu được truyền vào, cập nhật form
    if (this.data) {
      this.batchForm.patchValue(this.data);
    }
  }

  /**
   * Khởi tạo form với các validators
   */
  private initForm(): void {
    this.batchForm = this.fb.group({
      batchNumber: ['', Validators.required],
      manufacturingDate: [new Date()], // Mặc định là ngày hôm nay
      expiryDate: [null, Validators.required],
      quantity: [null, [Validators.required, Validators.min(1)]]
    });
  }

  /**
   * Đóng modal và trả về dữ liệu lô hàng nếu form hợp lệ
   */
  save(): void {
    if (this.batchForm.valid) {
      const result: BatchData = this.batchForm.value;
      if (this.dialogRef) {
        this.dialogRef.close(result);
      } else if (this.bottomSheetRef) {
        this.bottomSheetRef.dismiss(result);
      }
    } else {
      // Đánh dấu tất cả các trường là đã touched để hiển thị lỗi nếu có
      this.batchForm.markAllAsTouched();
    }
  }

  /**
   * Đóng modal mà không lưu dữ liệu
   */
  cancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): BatchData {
    if (!this.batchForm.valid) {
      throw new Error('Batch form is not valid');
    }

    return this.batchForm.value;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.batchForm.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: BatchData): void {
    this.data = data;

    if (this.batchForm) {
      this.batchForm.patchValue(data);
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào field đầu tiên khi modal mở
    setTimeout(() => {
      const firstInput = document.querySelector('input[formControlName="batchNumber"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Kiểm tra xem có thay đổi nào chưa được lưu không
    if (this.batchForm.dirty && this.batchForm.valid) {
      // Có thể thêm logic confirm nếu cần
      return true;
    }
    return true; // Luôn cho phép đóng modal
  }
}
