<!-- Add Field Modal Content -->
<div class="add-field-modal-container">
  
  <!-- Modal Header -->
  <div class="modal-header d-flex align-items-center justify-content-between mb-3">
    <div class="header-content">
      <h4 class="modal-title mb-1">
        <mat-icon class="me-2 text-primary">add_circle</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.ADD_FIELD_MODAL.TITLE' | translate }}
      </h4>
      <p class="modal-description text-muted mb-0">
        {{ 'DYNAMIC_LAYOUT_BUILDER.ADD_FIELD_MODAL.DESCRIPTION' | translate }}
      </p>
    </div>
    
    <!-- Close Button -->
    <button 
      mat-icon-button 
      (click)="onCancel()"
      class="close-button"
      [attr.aria-label]="'COMMON.CLOSE' | translate">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Modal Content -->
  <div class="modal-content">
    
    <!-- Fields List -->
    @if (fieldsBySection().length > 0) {
      <div class="fields-sections">
        
        @for (section of fieldsBySection(); track trackBySectionTitle($index, section)) {
          <!-- Section Header -->
          <div class="section-header mb-2">
            <div class="d-flex align-items-center">
              <mat-icon class="text-primary me-2 small-icon">folder</mat-icon>
              <span class="fw-semibold">{{ section.title }}</span>
              <span class="badge bg-secondary ms-auto small">{{ section.fields.length }}</span>
            </div>
          </div>

          <!-- Fields in Section -->
          @if (section.fields.length > 0) {
            <div class="fields-grid mb-4">
              @for (field of section.fields; track trackByFieldId($index, field)) {
                <button
                  mat-stroked-button
                  (click)="onFieldSelect(field)"
                  class="field-item-button w-100 text-start">
                  
                  <!-- Field Icon -->
                  <mat-icon 
                    class="field-icon me-3"
                    [style.color]="getFieldIconColor(field.type)">
                    {{ getFieldIcon(field.type) }}
                  </mat-icon>
                  
                  <!-- Field Info -->
                  <div class="field-info flex-grow-1">
                    <div class="field-label fw-medium">{{ field.label }}</div>
                    <div class="field-type text-muted small">
                      {{ getFieldTypeLabel(field.type) }}
                    </div>
                  </div>
                  
                  <!-- Add Icon -->
                  <mat-icon class="add-icon text-success">add_circle_outline</mat-icon>
                </button>
              }
            </div>
          } @else {
            <div class="no-fields-message text-center text-muted py-3 mb-4">
              <mat-icon class="mb-2">folder_open</mat-icon>
              <div class="small">{{ 'DYNAMIC_LAYOUT_BUILDER.ADD_FIELD_MODAL.NO_FIELDS_IN_SECTION' | translate }}</div>
            </div>
          }
        }
      </div>
    } @else {
      <!-- No Fields Available -->
      <div class="no-fields-available text-center text-muted py-5">
        <mat-icon class="mb-3" style="font-size: 48px; width: 48px; height: 48px;">folder_open</mat-icon>
        <h5>{{ 'DYNAMIC_LAYOUT_BUILDER.ADD_FIELD_MODAL.NO_FIELDS_AVAILABLE' | translate }}</h5>
        <p class="mb-0">{{ 'DYNAMIC_LAYOUT_BUILDER.ADD_FIELD_MODAL.NO_FIELDS_DESCRIPTION' | translate }}</p>
      </div>
    }
  </div>

  <!-- Modal Footer -->
  <div class="modal-footer d-flex justify-content-end pt-3 border-top">
    <button 
      mat-button 
      (click)="onCancel()"
      class="cancel-button">
      <mat-icon class="me-1">close</mat-icon>
      {{ 'COMMON.CANCEL' | translate }}
    </button>
  </div>
</div>
