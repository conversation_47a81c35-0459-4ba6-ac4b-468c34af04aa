{"RESPONSIVE_MODAL_SERVICE": {"OVERLAY_MODAL_TEST": {"TITLE": "Overlay Modal Test", "MESSAGE": "This is a test modal with overlay positioning", "CONTENT": "On desktop: <PERSON><PERSON> will display near the clicked button position. On mobile: Will display bottom sheet.", "FEATURE": "Overlay positioning"}, "FORCE_MODE": {"DIALOG": {"TITLE": "Overlay <PERSON> (Force dialog)", "MESSAGE": "Test modal with force mode: dialog", "CONTENT": "This modal is forced to display as dialog regardless of viewport size."}, "BOTTOM_SHEET": {"TITLE": "Overlay Modal (Force bottom-sheet)", "MESSAGE": "Test modal with force mode: bottom-sheet", "CONTENT": "This modal is forced to display as bottom-sheet regardless of viewport size."}}, "NOTIFICATIONS": {"CANCELLED": "Overlay modal cancelled", "DIALOG_CANCELLED": "Overlay modal (dialog) cancelled", "BOTTOM_SHEET_CANCELLED": "Overlay modal (bottom-sheet) cancelled", "SUCCESS": "Overlay modal: {action}", "DIALOG_SUCCESS": "Overlay modal (dialog): {action}", "BOTTOM_SHEET_SUCCESS": "Overlay modal (bottom-sheet): {action}", "ERROR": "Error: {error}"}, "TEST_FEATURES": {"OVERLAY_POSITIONING": "Overlay positioning", "RESPONSIVE_BEHAVIOR": "Responsive behavior", "FORCE_MODE": "Force mode", "SMART_POSITIONING": "Smart positioning", "VIEWPORT_AWARENESS": "Viewport awareness", "TYPE_SAFETY": "Type safety"}}}