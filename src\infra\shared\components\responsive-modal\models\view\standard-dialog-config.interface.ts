import { TemplateRef, Type } from '@angular/core';
import { MatDialogConfig } from '@angular/material/dialog';
import { BaseStandardModalConfig } from '../../../../../models/view/base-standard-modal-config.interface';

/**
 * C<PERSON>u hình cho StandardDialogService
 * Extends MatDialogConfig và BaseStandardModalConfig với các enhanced features
 * Định nghĩa các tùy chọn để tùy chỉnh dialog behavior và appearance
 */
export interface StandardDialogConfig<D = any> extends
  Omit<MatDialogConfig<D>, 'data'>,
  BaseStandardModalConfig {

  /**
   * Dữ liệu được truyền vào component được load động
   * Thay thế cho MatDialogConfig.data để tránh conflict
   */
  data?: D;

  /**
   * Dữ liệu bổ sung cho component (alias cho data)
   * Đ<PERSON> maintain backward compatibility
   */
  componentData?: D;

  // Các properties width, height, disableClose, panelClass, position
  // đã được inherit từ MatDialogConfig
}
