<!-- Dialog Title -->
<h2 mat-dialog-title class="d-flex justify-content-between align-items-center">
  @if (isTitleString()) {
    <span>{{ $any(title() || '') | translate }}</span>
  } @else if (isTitleTemplate()) {
    <ng-container *ngTemplateOutlet="$any(title())"></ng-container>
  }

  @if (enableClose()) {
    <button
      mat-icon-button
      class="close-button ms-auto"
      (click)="onClose()"
      [attr.aria-label]="'DIALOG.CLOSE' | translate">
      <mat-icon>close</mat-icon>
    </button>
  }
</h2>

<!-- Dialog Content -->
<div mat-dialog-content class="p-3">
  <!-- Dynamic Component Container -->
  <div #dynamicComponentContainer></div>
</div>

<!-- Dialog Actions -->
<div mat-dialog-actions class="p-3 d-flex justify-content-end gap-2">
  @if (useDefault()) {
    <button
      mat-button
      (click)="onCancel()"
      class="me-2">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button
      mat-raised-button
      color="primary"
      [disabled]="!isComponentValid()"
      (click)="onConfirm()">
      {{ 'COMMON.SAVE' | translate }}
    </button>
  } @else if (customActionsTemplate()) {
    <ng-container *ngTemplateOutlet="
      $any(customActionsTemplate());
      context: {
        $implicit: this,
        onCustomAction: onCustomAction.bind(this),
        onConfirm: onConfirm.bind(this),
        onCancel: onCancel.bind(this),
        onClose: onClose.bind(this)
      }
    "></ng-container>
  }
</div>
