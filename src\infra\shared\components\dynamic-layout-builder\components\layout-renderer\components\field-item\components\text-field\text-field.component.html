<!-- Text Field Container -->
@if (shouldShowField()) {
  <div [class]="config.currentViewMode === 'form' ? 'field-row-form' : 'field-row-view'">

    <!-- Label Column -->
    <div class="field-label-column">
      <label class="field-label-text">
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>

      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnly()) {
        <mat-icon
          class="read-only-icon"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Value Column -->
    <div class="field-value-column">

      <!-- PREVIEW MODE: Display placeholder data, không có edit buttons -->
      @if (config.currentViewMode === 'preview') {
        <div class="field-view-value" [class.empty]="!placeholderValue()">
          @if (isUrl(placeholderValue())) {
            <a [href]="placeholderValue()" target="_blank" class="field-url-link">
              {{ placeholderValue() }}
            </a>
          } @else {
            <span>{{ placeholderValue() || ('DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate) }}</span>
          }
        </div>
      }

      <!-- VIEW MODE: Display actual values với inline edit functionality -->
      @else if (config.currentViewMode === 'view') {
        @if (!isFieldInEditMode()) {
          <!-- Normal view mode -->
          <div class="field-view-value" [class.empty]="!currentValue()">
            @if (currentValue()) {
              @if (isUrl(toStringValue(currentValue()))) {
                <a [href]="toStringValue(currentValue())" target="_blank" class="field-url-link">
                  {{ currentValue() }}
                </a>
              } @else {
                <span>{{ currentValue() }}</span>
              }
            } @else {
              <span class="empty">{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate }}</span>
            }

            <!-- Edit Actions - chỉ hiển thị khi có quyền write -->
            @if (canEditField()) {
              <div class="field-edit-actions">
                <button type="button" class="btn btn-edit" (click)="startEditMode()">
                  <mat-icon>edit</mat-icon>
                  {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.EDIT' | translate }}
                </button>
              </div>
            }
          </div>
        } @else {
          <!-- Inline edit mode -->
          <div class="field-inline-edit">
            <div class="field-input-container">
              <mat-form-field appearance="outline" class="w-100">
                <input
                  matInput
                  [type]="getInputType()"
                  [placeholder]="getPlaceholder() | translate"
                  [formControl]="formControl()"
                  [class.read-only-cursor]="isReadOnly()">
              </mat-form-field>
            </div>

            <!-- Edit Actions -->
            <div class="field-edit-actions editing">
              <button type="button" class="btn btn-save" (click)="saveEditMode()">
                <mat-icon>save</mat-icon>
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.SAVE' | translate }}
              </button>
              <button type="button" class="btn btn-cancel" (click)="cancelEditMode()">
                <mat-icon>close</mat-icon>
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.CANCEL' | translate }}
              </button>
            </div>
          </div>
        }
      }

      <!-- FORM MODE: Display input control -->
      @else if (config.currentViewMode === 'form') {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Input element -->
          <input
            matInput
            [type]="getInputType()"
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [readonly]="isReadOnly()"
            [class.read-only-cursor]="isReadOnly()">

        </mat-form-field>
      }

      <!-- Field-Level Validation Errors -->
      @if (shouldShowValidationErrors()) {
        <div class="field-validation-errors" aria-live="polite">
          @for (error of getValidationErrors(); track error) {
            <div class="validation-error-item">
              <mat-icon class="error-icon">error</mat-icon>
              <span class="error-message">{{ error | translate }}</span>
            </div>
          }
        </div>
      }

      <!-- Field Tooltip/Description -->
      @if (config.field.tooltip) {
        <div class="field-tooltip">
          <small class="text-muted">{{ config.field.tooltip }}</small>
        </div>
      }
    </div>
  </div>
}
