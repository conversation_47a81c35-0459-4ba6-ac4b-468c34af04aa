/**
 * Connected Overlay Modal Styles
 * 
 * Styles cho modal sử dụng cdkConnectedOverlay pattern
 * Modal hoạt động giống mat-menu với positioning relative to connected element
 */

.connected-overlay-modal-panel {
  // Base panel styles
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  // Positioning - sử dụng absolute để hoạt động giống mat-menu
  position: absolute !important;
  
  // Z-index để đảm bảo modal hiển thị trên các element khác
  z-index: 1000;
  
  // Animation cho smooth appearance
  transform-origin: top center;
  animation: connectedModalFadeIn 0.2s ease-out;
  
  // Responsive sizing
  min-width: 300px;
  max-width: 600px;
  max-height: 80vh;
  
  // Scrollable content
  overflow-y: auto;
  
  // Mobile responsive
  @media (max-width: 768px) {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    max-height: calc(100vh - 64px);
  }
}

// Animation keyframes
@keyframes connectedModalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-8px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Backdrop styles (transparent để không che khuất UI)
.cdk-overlay-transparent-backdrop {
  background: transparent;
  
  // Cho phép click-through nếu cần
  pointer-events: auto;
}

// Dark backdrop variant (nếu cần)
.connected-overlay-modal-backdrop {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
}

// Positioning variants
.connected-overlay-modal-panel {
  // Top positioning
  &.position-top {
    transform-origin: bottom center;
    animation: connectedModalSlideUp 0.2s ease-out;
  }
  
  // Bottom positioning (default)
  &.position-bottom {
    transform-origin: top center;
    animation: connectedModalSlideDown 0.2s ease-out;
  }
  
  // Left positioning
  &.position-left {
    transform-origin: right center;
    animation: connectedModalSlideRight 0.2s ease-out;
  }
  
  // Right positioning
  &.position-right {
    transform-origin: left center;
    animation: connectedModalSlideLeft 0.2s ease-out;
  }
}

// Animation variants
@keyframes connectedModalSlideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes connectedModalSlideUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes connectedModalSlideLeft {
  from {
    opacity: 0;
    transform: translateX(8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes connectedModalSlideRight {
  from {
    opacity: 0;
    transform: translateX(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Content styles
.connected-overlay-modal-content {
  padding: 24px;
  
  // Header
  .modal-header {
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    
    h2, h3 {
      margin: 0;
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1a1a;
    }
  }
  
  // Body
  .modal-body {
    margin-bottom: 16px;
    color: #4a4a4a;
    line-height: 1.5;
  }
  
  // Footer
  .modal-footer {
    padding-top: 16px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    
    .btn {
      min-width: 80px;
    }
  }
}

// Integration với Bootstrap classes
.connected-overlay-modal-panel {
  .card {
    border: none;
    box-shadow: none;
    margin: 0;
  }
  
  .card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    padding: 20px 24px 16px;
  }
  
  .card-body {
    padding: 16px 24px;
  }
  
  .card-footer {
    background: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
    padding: 16px 24px 20px;
  }
}

// Dark theme support
.dark-theme .connected-overlay-modal-panel {
  background: #2a2a2a;
  border-color: rgba(255, 255, 255, 0.12);
  color: #e0e0e0;
  
  .modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.12);
    
    h2, h3 {
      color: #ffffff;
    }
  }
  
  .modal-body {
    color: #b0b0b0;
  }
  
  .modal-footer {
    border-top-color: rgba(255, 255, 255, 0.12);
  }
  
  .card-header {
    border-bottom-color: rgba(255, 255, 255, 0.12);
  }
  
  .card-footer {
    border-top-color: rgba(255, 255, 255, 0.12);
  }
}
