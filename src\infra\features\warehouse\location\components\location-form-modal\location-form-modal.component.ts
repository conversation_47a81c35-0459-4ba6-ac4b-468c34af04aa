import { Component, OnInit, Inject, Optional, ChangeDetectionStrategy, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { LocationFormComponent } from '../location-form/location-form.component';
import { LocationFormData, Location } from '../../models/api/location.dto';
import { LocationService } from '../../services/location.service';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';
import { Subscription } from 'rxjs';

export interface LocationFormModalData {
  editMode: boolean;
  locationId?: string;
  parentId?: string | null;
  currentLevel?: number;
}

@Component({
  selector: 'app-location-form-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatBottomSheetModule,
    MatButtonModule,
    TranslateModule,
    LocationFormComponent
  ],
  templateUrl: './location-form-modal.component.html',
  styleUrls: ['./location-form-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LocationFormModalComponent implements OnInit, OnDestroy, StrictModalComponent<LocationFormModalData, Location | null> {
  dialogTitle = '';

  private dialogRef?: MatDialogRef<LocationFormModalComponent>;
  private bottomSheetRef?: MatBottomSheetRef<LocationFormModalComponent>;
  data: LocationFormModalData;

  // Memory management: Container để quản lý tất cả subscriptions
  private readonly subscriptions = new Subscription();

  constructor(
    private locationService: LocationService,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: LocationFormModalData | null,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: LocationFormModalData | null,
    @Optional() dialogRefInject?: MatDialogRef<LocationFormModalComponent>,
    @Optional() bottomSheetRefInject?: MatBottomSheetRef<LocationFormModalComponent>
  ) {
    this.dialogRef = dialogRefInject;
    this.bottomSheetRef = bottomSheetRefInject;
    this.data = this.dialogData || this.bottomSheetData || { editMode: false };
  }

  ngOnInit(): void {
    this.dialogTitle = this.data.editMode
      ? 'WAREHOUSE.LOCATION.EDIT'
      : 'WAREHOUSE.LOCATION.CREATE';
  }

  onFormSubmit(formData: LocationFormData): void {
    if (this.data.editMode && this.data.locationId) {
      // Memory management: Thêm subscription vào container để tự động cleanup
      this.subscriptions.add(
        this.locationService.updateLocation(this.data.locationId, formData).subscribe({
          next: (location) => {
            this.closeModal(location);
          },
          error: (error) => {
            console.error('Error updating location:', error);
            this.closeModal(null);
          }
        })
      );
    } else {
      // Memory management: Thêm subscription vào container để tự động cleanup
      this.subscriptions.add(
        this.locationService.createLocation(formData).subscribe({
          next: (location) => {
            this.closeModal(location);
          },
          error: (error) => {
            console.error('Error creating location:', error);
            this.closeModal(null);
          }
        })
      );
    }
  }

  onFormCancel(): void {
    this.closeModal(null);
  }

  private closeModal(result: Location | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): Location | null {
    // LocationFormModalComponent không có form riêng, nó delegate cho LocationFormComponent
    // Kết quả sẽ được trả về thông qua onFormSubmit callback
    return null;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // Validation được handle bởi LocationFormComponent
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: LocationFormModalData): void {
    this.data = data;
    this.dialogTitle = data.editMode
      ? 'WAREHOUSE.LOCATION.EDIT'
      : 'WAREHOUSE.LOCATION.CREATE';
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus sẽ được handle bởi LocationFormComponent
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }

  ngOnDestroy(): void {
    // Memory management: Unsubscribe tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }
}
