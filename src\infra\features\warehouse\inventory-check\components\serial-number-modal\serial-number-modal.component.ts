import { ChangeDetectionStrategy, Component, EventEmitter, Inject, OnInit, Optional, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { EmbeddedProductSerial, ProductListItem, SerialNumberModalData } from '@features/warehouse/inventory-check/models/api/inventory-check.dto';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Component modal quản lý số serial
 * Cho phép xem và cập nhật trạng thái của các số serial
 */
@Component({
  selector: 'app-serial-number-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatBottomSheetModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    TranslateModule
  ],
  templateUrl: './serial-number-modal.component.html',
  styleUrls: ['./serial-number-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SerialNumberModalComponent implements OnInit, StrictModalComponent<SerialNumberModalData, EmbeddedProductSerial[]> {
  /**
   * Thông tin sản phẩm
   */
  product: ProductListItem;

  /**
   * Danh sách serial
   */
  serials: EmbeddedProductSerial[] = [];

  /**
   * Danh sách trạng thái serial
   */
  serialStatuses = [
    { value: 'in_stock', label: 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS.IN_STOCK' },
    { value: 'assigned', label: 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS.ASSIGNED' },
    { value: 'sold', label: 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS.SOLD' },
    { value: 'missing', label: 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS.MISSING' },
    { value: 'damaged', label: 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS.DAMAGED' },
    { value: 'returned', label: 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS.RETURNED' },
    { value: 'in_transit', label: 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS.IN_TRANSIT' }
  ];

  /**
   * Output để emit danh sách serial đã cập nhật
   */
  @Output() serialsUpdated = new EventEmitter<EmbeddedProductSerial[]>();

  /**
   * Dữ liệu từ dialog hoặc bottom sheet
   */
  data: SerialNumberModalData | null | undefined;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: SerialNumberModalData | null | undefined,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: SerialNumberModalData | null | undefined,
    @Optional() private dialogRef?: MatDialogRef<SerialNumberModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<SerialNumberModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData;

    if (this.data) {
      this.product = this.data.product;
      // Tạo bản sao của danh sách serial để không ảnh hưởng đến dữ liệu gốc
      this.serials = JSON.parse(JSON.stringify(this.data.serials || []));
    } else {
      // Khởi tạo giá trị mặc định nếu không có dữ liệu
      this.product = {
        productId: '',
        name: '',
        sku: '',
        price: 0,
        warehouseIds: [],
        categoryIds: [],
        warehouseStock: {},
        brandIds: [],
        trackInventory: false
      } as any;
    }
  }

  ngOnInit(): void {
    // Nếu không có serial nào, tạo một số serial mẫu cho mục đích demo
    if (this.serials.length === 0) {
      this.createSampleSerials();
    }
  }

  /**
   * Tạo một số serial mẫu cho mục đích demo
   */
  private createSampleSerials(): void {
    // Tạo 5 serial mẫu
    for (let i = 1; i <= 5; i++) {
      this.serials.push({
        _id: `serial${i}`,
        serialNumber: `${this.product.sku}-SN00${i}`,
        status: 'in_stock'
      });
    }
  }

  /**
   * Xử lý khi nhấn nút Hủy
   * Đóng dialog mà không emit kết quả
   */
  onCancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }

  /**
   * Xử lý khi nhấn nút Xác nhận
   * Emit danh sách serial đã cập nhật và đóng dialog
   */
  onConfirm(): void {
    if (this.dialogRef) {
      this.dialogRef.close(this.serials);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(this.serials);
    }
  }

  /**
   * Tính số lượng thực tế dựa trên số serial có trạng thái in_stock hoặc assigned
   * @returns Số lượng thực tế
   */
  getActualQuantity(): number {
    return this.serials.filter(serial =>
      serial.status === 'in_stock' || serial.status === 'assigned'
    ).length;
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): EmbeddedProductSerial[] {
    return this.serials;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // Serial number modal luôn valid vì có thể có hoặc không có serial
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: SerialNumberModalData): void {
    this.data = data;
    this.product = data.product;
    // Tạo bản sao của danh sách serial để không ảnh hưởng đến dữ liệu gốc
    this.serials = JSON.parse(JSON.stringify(data.serials || []));

    // Nếu không có serial nào, tạo một số serial mẫu cho mục đích demo
    if (this.serials.length === 0) {
      this.createSampleSerials();
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào first select khi modal mở
    setTimeout(() => {
      const firstSelect = document.querySelector('mat-select') as HTMLElement;
      if (firstSelect) {
        firstSelect.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
