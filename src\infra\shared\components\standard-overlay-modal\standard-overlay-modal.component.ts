import {
  Component,
  Inject,
  TemplateRef,
  ChangeDetectionStrategy,
  signal,
  computed,
  ViewChild,
  ViewContainerRef,
  OnInit,
  ComponentRef,
  Type,
  ViewEncapsulation,
  OnDestroy,
  ElementRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FlashMessageService } from '@core/services/flash_message.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';

import { StandardOverlayModalConfig } from './models/standard-overlay-modal-config.interface';
import { StandardOverlayModalResult } from './models/standard-overlay-modal-result.interface';
import {
  ModalComponent,
  isModalComponent,
  isModalComponentWithData,
  ValidatableModalComponent,
  isValidatableModalComponent,
  AdvancedModalComponent,
  StrictModalComponent,
  isStrictModalComponent
} from '../standard-dialog/interfaces/modal-component.interface';

/**
 * StandardOverlayModalWrapperComponent - Wrapper component cho overlay modal với positioning
 *
 * Chức năng chính:
 * - Wrapper component theo pattern của StandardDialogWrapperComponent và StandardBottomSheetWrapperComponent
 * - Dynamic component loading với ViewContainerRef.createComponent()
 * - Overlay positioning với mouse coordinates hoặc element reference
 * - Tận dụng ModalComponent interface để lấy kết quả
 * - Component con implement StrictModalComponent interface
 * - Type-safe với TypeScript interfaces
 *
 * Tính năng:
 * - Mouse positioning: Modal căn giữa theo vị trí chuột
 * - Element positioning: Modal hiển thị gần element trigger
 * - Smart viewport awareness: Tự động điều chỉnh vị trí khi tràn viewport
 * - Backdrop click để đóng modal
 * - Keyboard navigation (ESC để đóng)
 */
@Component({
  selector: 'app-standard-overlay-modal-wrapper',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './standard-overlay-modal.component.html',
  styleUrls: ['./standard-overlay-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class StandardOverlayModalWrapperComponent<T extends StrictModalComponent = StrictModalComponent> 
  implements OnInit, OnDestroy {
  
  @ViewChild('dynamicComponentContainer', { read: ViewContainerRef, static: true })
  dynamicComponentContainer!: ViewContainerRef;

  // Signals cho reactive state management
  private readonly _config = signal<StandardOverlayModalConfig & { component?: Type<T> }>({});
  private componentRef: ComponentRef<T> | null = null;

  // Computed properties
  readonly title = computed(() => this._config().title);
  readonly enableClose = computed(() => this._config().enableClose ?? true);
  readonly useDefault = computed(() => this._config().actions?.useDefault ?? true);
  readonly customActionsTemplate = computed(() => this._config().actions?.customActionsTemplate);

  // Type checking helpers
  readonly isTitleTemplate = computed(() => this.title() instanceof TemplateRef);
  readonly isTitleString = computed(() => typeof this.title() === 'string');

  // Validation helper - với type constraint T extends StrictModalComponent
  readonly isComponentValid = computed(() => {
    if (!this.componentRef?.instance) return true;

    const instance = this.componentRef.instance;

    // Với type constraint T extends StrictModalComponent, instance luôn có isValid() method
    if (isStrictModalComponent(instance)) {
      return instance.isValid();
    }

    // Fallback cho ValidatableModalComponent (backward compatibility)
    if (isValidatableModalComponent(instance)) {
      return (instance as ValidatableModalComponent).isValid();
    }

    return true; // Mặc định là valid nếu không có validation
  });

  // Public properties để nhận data từ service
  public data!: StandardOverlayModalConfig & { component?: Type<T> };
  public overlayRef!: OverlayRef;

  constructor(
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {}

  ngOnInit(): void {
    // Set config từ data được inject
    if (this.data) {
      this._config.set(this.data);
    }

    // Load dynamic component nếu có
    if (this.data?.component) {
      this.loadDynamicComponent();
    }

    // Setup keyboard listeners
    this.setupKeyboardListeners();
  }

  ngOnDestroy(): void {
    // Cleanup component reference
    if (this.componentRef) {
      this.componentRef.destroy();
      this.componentRef = null;
    }
  }

  /**
   * Load dynamic component - đơn giản hóa logic theo pattern của StandardDialogWrapperComponent
   * Thêm validation cho StrictModalComponent interface
   */
  private loadDynamicComponent(): void {
    if (!this.data.component || !this.dynamicComponentContainer) {
      return;
    }

    this.dynamicComponentContainer.clear();
    this.componentRef = this.dynamicComponentContainer.createComponent(this.data.component);

    // Validate component implements StrictModalComponent interface (recommended)
    if (this.componentRef.instance && !isStrictModalComponent(this.componentRef.instance)) {
      // Thay thế console.warn bằng warning message có ý nghĩa cho developer
      this.flashMessageService.warning(
        this.translateService.instant('FLASH_MESSAGES.WARNING.GENERAL.INCOMPLETE_DATA'),
        {
          description: `Component ${this.data.component.name} chưa implement đầy đủ StrictModalComponent interface`
        }
      );
    }

    // Inject data vào component
    this.injectDataToComponent();

    // Inject OverlayRef để component có thể tự đóng overlay
    this.injectOverlayRefToComponent();

    // Gọi onModalOpen() nếu component implement AdvancedModalComponent
    this.callOnModalOpen();

    this.componentRef.changeDetectorRef.detectChanges();
  }

  /**
   * Inject data vào component con
   */
  private injectDataToComponent(): void {
    if (!this.componentRef?.instance) return;

    const componentData = this.data.data;
    if (!componentData) return;

    const instance = this.componentRef.instance as any;

    // Method 1: Gọi updateData() nếu có (ModalComponentWithData)
    if (isModalComponentWithData(instance)) {
      instance.updateData!(componentData);
      return;
    }

    // Method 2: Assign vào property data
    if ('data' in instance) {
      instance.data = componentData;
      return;
    }

    // Method 3: Assign từng property
    if (typeof componentData === 'object') {
      Object.assign(instance, componentData);
    }
  }

  /**
   * Inject OverlayRef để component có thể tự đóng overlay
   */
  private injectOverlayRefToComponent(): void {
    if (!this.componentRef?.instance) return;

    const instance = this.componentRef.instance as Record<string, unknown>;

    // Inject overlayRef nếu component có property này
    if ('overlayRef' in instance) {
      instance.overlayRef = this.overlayRef;
    }
  }

  /**
   * Gọi onModalOpen() nếu component implement AdvancedModalComponent
   */
  private callOnModalOpen(): void {
    if (!this.componentRef?.instance) return;

    const instance = this.componentRef.instance as Record<string, unknown>;

    // Gọi onModalOpen() nếu có
    if (typeof instance.onModalOpen === 'function') {
      instance.onModalOpen();
    }
  }

  /**
   * Gọi onModalClose() nếu component implement AdvancedModalComponent
   * @returns Promise<boolean> - true nếu có thể đóng, false nếu không
   */
  private async callOnModalClose(): Promise<boolean> {
    if (!this.componentRef?.instance) return true;

    const instance = this.componentRef.instance as any;

    // Gọi onModalClose() nếu có
    if (typeof instance.onModalClose === 'function') {
      const result = instance.onModalClose();

      // Xử lý cả sync và async result
      if (result instanceof Promise) {
        return await result;
      }

      return result !== false; // Mặc định là true nếu không return false
    }

    return true; // Mặc định cho phép đóng
  }

  /**
   * Lấy data từ component con để trả về
   */
  getComponentData(): any {
    if (!this.componentRef?.instance) {
      return this.data.data;
    }

    const instance = this.componentRef.instance;

    // Method 1: Sử dụng ModalComponent interface
    if (isModalComponent(instance)) {
      return instance.getModalResult();
    }

    // Method 2: Lấy từ property data
    if (instance && typeof instance === 'object' && 'data' in instance) {
      return (instance as any).data;
    }

    // Fallback: Trả về data ban đầu
    return this.data.data;
  }

  /**
   * Setup keyboard listeners cho accessibility
   */
  private setupKeyboardListeners(): void {
    if (!this.overlayRef) return;

    // Listen for ESC key to close modal
    this.overlayRef.keydownEvents().subscribe(event => {
      if (event.key === 'Escape' && this.enableClose()) {
        this.onClose();
      }
    });
  }

  /**
   * Xử lý sự kiện Confirm
   */
  async onConfirm(): Promise<void> {
    // Kiểm tra validation trước
    if (!this.isComponentValid()) {
      // Thay thế console.warn bằng FlashMessageService
      this.flashMessageService.warning(
        this.translateService.instant('FLASH_MESSAGES.WARNING.FORM.VALIDATION_FAILED')
      );
      return;
    }

    // Gọi onModalClose() nếu có để kiểm tra có thể đóng không
    const canClose = await this.callOnModalClose();
    if (!canClose) {
      // Xóa console.log debug không cần thiết - component tự xử lý thông báo
      return;
    }

    // Đóng overlay với kết quả
    this.overlayRef?.dispose();
  }

  /**
   * Xử lý sự kiện Cancel
   */
  onCancel(): void {
    this.overlayRef?.dispose();
  }

  /**
   * Xử lý sự kiện Close
   */
  onClose(): void {
    this.overlayRef?.dispose();
  }

  /**
   * Xử lý custom action
   */
  onCustomAction(actionName: string, payload?: any): void {
    this.overlayRef?.dispose();
  }

  /**
   * Getter để template có thể truy cập TemplateRef type
   */
  get TemplateRef() {
    return TemplateRef;
  }
}
