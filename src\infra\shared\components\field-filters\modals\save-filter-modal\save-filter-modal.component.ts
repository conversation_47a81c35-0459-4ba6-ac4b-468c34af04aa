import {
  Component,
  Inject,
  Optional,
  ChangeDetectionStrategy,
  signal,
  computed
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { SavedFilter } from '@shared/models/view/field-filters.model';
import { FilterValue } from '@shared/components';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu đầu vào của modal
 */
export interface SaveFilterModalData {
  filterName?: string; // Tên filter hiện tại (nếu có)
  filters: Array<{
    fieldId: string;
    filterValue?: FilterValue;
  }>; // Danh sách filter hiện tại
  savedFilters: SavedFilter[]; // Danh sách saved filters để kiểm tra trùng tên
}

/**
 * Kiểu dữ liệu trả về từ modal
 */
export interface SaveFilterModalResult {
  name: string;
  filters: Array<{
    fieldId: string;
    filterValue?: FilterValue;
  }>;
}

/**
 * Component modal để lưu filter với tên mới
 * Hỗ trợ validation tên không trống và không trùng
 */
@Component({
  selector: 'app-save-filter-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule
  ],
  templateUrl: './save-filter-modal.component.html',
  styleUrls: ['./save-filter-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SaveFilterModalComponent implements StrictModalComponent<SaveFilterModalData, SaveFilterModalResult> {
  /**
   * Tên filter được nhập bởi user
   */
  readonly filterName = signal<string>('');

  /**
   * Dữ liệu đầu vào cho modal
   */
  readonly data: SaveFilterModalData;

  /**
   * Computed signal để kiểm tra validation
   */
  readonly isNameEmpty = computed(() => !this.filterName().trim());

  readonly isNameDuplicated = computed(() => {
    const name = this.filterName().trim().toLowerCase();
    return this.data.savedFilters.some(filter =>
      filter.name.toLowerCase() === name
    );
  });

  readonly isValidSignal = computed(() =>
    !this.isNameEmpty() && !this.isNameDuplicated()
  );

  /**
   * Kiểm tra xem có phải edit mode không (có filterName ban đầu)
   */
  readonly isEditMode = computed(() => !!this.data.filterName);

  /**
   * Error message để hiển thị
   */
  readonly errorMessage = computed(() => {
    const prefix = this.isEditMode() ? 'FIELD_FILTERS.EDIT_MODAL' : 'FIELD_FILTERS.SAVE_MODAL';

    if (this.isNameEmpty()) {
      return `${prefix}.NAME_REQUIRED`;
    }
    if (this.isNameDuplicated()) {
      return `${prefix}.NAME_DUPLICATED`;
    }
    return '';
  });

  constructor(
    @Optional() private dialogRef?: MatDialogRef<SaveFilterModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<SaveFilterModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: SaveFilterModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: SaveFilterModalData
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      filters: [],
      savedFilters: []
    };

    // Khởi tạo tên filter nếu có
    if (this.data.filterName) {
      this.filterName.set(this.data.filterName);
    }
  }

  /**
   * Xử lý khi nhấn nút Hủy
   */
  onCancel(): void {
    this.close(undefined);
  }

  /**
   * Xử lý khi nhấn nút Lưu
   */
  onSave(): void {
    if (!this.isValidSignal()) {
      return;
    }

    const result: SaveFilterModalResult = {
      name: this.filterName().trim(),
      filters: this.data.filters
    };

    this.close(result);
  }

  /**
   * Xử lý khi nhấn Enter trong input
   */
  onEnterPressed(): void {
    if (this.isValidSignal()) {
      this.onSave();
    }
  }

  /**
   * Cập nhật tên filter
   */
  onNameChange(name: string): void {
    this.filterName.set(name);
  }

  /**
   * Đóng modal với kết quả
   */
  private close(result: SaveFilterModalResult | undefined): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): SaveFilterModalResult {
    if (!this.isValid()) {
      throw new Error('Save filter modal is not valid');
    }

    return {
      name: this.filterName().trim(),
      filters: this.data.filters
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return !this.isNameEmpty() && !this.isNameDuplicated();
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: SaveFilterModalData): void {
    // Cập nhật data
    (this.data as any) = data;

    // Khởi tạo tên filter nếu có
    if (data.filterName) {
      this.filterName.set(data.filterName);
    } else {
      this.filterName.set('');
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào input khi modal mở
    setTimeout(() => {
      const input = document.querySelector('input[matInput]') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
