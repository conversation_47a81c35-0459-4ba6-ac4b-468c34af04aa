import {
  Component,
  Inject,
  TemplateRef,
  ChangeDetectionStrategy,
  signal,
  computed,
  ViewChild,
  ViewContainerRef,
  OnInit,
  ComponentRef,
  Type,
  ViewEncapsulation
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  MatBottomSheetRef,
  MAT_BOTTOM_SHEET_DATA
} from '@angular/material/bottom-sheet';
import { FlashMessageService } from '@core/services/flash_message.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { StandardBottomSheetConfig } from './models/standard-bottom-sheet-config.interface';
import { StandardBottomSheetResult } from '../../models/view/standard-bottom-sheet-result.interface';
import {
  ModalComponent,
  isModalComponent,
  isModalComponentWithData,
  ValidatableModalComponent,
  isValidatableModalComponent,
  AdvancedModalComponent,
  StrictModalComponent,
  isStrictModalComponent
} from '../../models/view/modal-component.interface';

/**
 * StandardBottomSheetWrapperComponent - Refactored wrapper component cho StandardBottomSheetService
 *
 * Chức năng chính:
 * - Wrapper component đơn giản hóa theo pattern của StandardDialogWrapperComponent
 * - Dynamic component loading với ViewContainerRef.createComponent()
 * - Tận dụng ModalComponent interface để lấy kết quả
 * - Component con implement ModalComponent interface
 * - Type-safe với TypeScript interfaces
 *
 * Cải tiến:
 * - Component con có thể truy cập MatBottomSheetRef trực tiếp
 * - Sử dụng ModalComponent interface để lấy kết quả nhất quán
 * - Thống nhất với StandardDialogWrapperComponent pattern
 */
@Component({
  selector: 'app-standard-bottom-sheet-wrapper',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './standard-bottom-sheet.component.html',
  styleUrls: ['./standard-bottom-sheet.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class StandardBottomSheetWrapperComponent<T extends StrictModalComponent = StrictModalComponent> implements OnInit {
  @ViewChild('dynamicComponentContainer', { read: ViewContainerRef, static: true })
  dynamicComponentContainer!: ViewContainerRef;

  // Signals cho reactive state management
  private readonly _config = signal<StandardBottomSheetConfig & { component?: Type<T> }>({});
  private componentRef: ComponentRef<T> | null = null;

  // Computed properties
  readonly title = computed(() => this._config().title);
  readonly enableClose = computed(() => this._config().enableClose ?? true);
  readonly useDefault = computed(() => this._config().actions?.useDefault ?? true);
  readonly customActionsTemplate = computed(() => this._config().actions?.customActionsTemplate);

  // Type checking helpers
  readonly isTitleTemplate = computed(() => this.title() instanceof TemplateRef);
  readonly isTitleString = computed(() => typeof this.title() === 'string');

  // Validation helper - với type constraint T extends StrictModalComponent
  readonly isComponentValid = computed(() => {
    if (!this.componentRef?.instance) return true;

    const instance = this.componentRef.instance;

    // Với type constraint T extends StrictModalComponent, instance luôn có isValid() method
    if (isStrictModalComponent(instance)) {
      return instance.isValid();
    }

    // Fallback cho ValidatableModalComponent (backward compatibility)
    if (isValidatableModalComponent(instance)) {
      return (instance as ValidatableModalComponent).isValid();
    }

    return true; // Mặc định là valid nếu không có validation
  });

  constructor(
    private readonly bottomSheetRef: MatBottomSheetRef<StandardBottomSheetWrapperComponent<T>, T>,
    @Inject(MAT_BOTTOM_SHEET_DATA) public readonly data: StandardBottomSheetConfig & { component?: Type<T> },
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {
    this._config.set(data);
  }

  ngOnInit(): void {
    // Load dynamic component nếu có
    if (this.data.component) {
      this.loadDynamicComponent();
    }
  }

  /**
   * Load dynamic component - đơn giản hóa logic theo pattern của StandardDialogWrapperComponent
   * Thêm validation cho StrictModalComponent interface
   */
  private loadDynamicComponent(): void {
    if (!this.data.component || !this.dynamicComponentContainer) {
      return;
    }

    this.dynamicComponentContainer.clear();
    this.componentRef = this.dynamicComponentContainer.createComponent(this.data.component);

    // Validate component implements StrictModalComponent interface (recommended)
    if (this.componentRef.instance && !isStrictModalComponent(this.componentRef.instance)) {
      // Thay thế console.warn bằng warning message có ý nghĩa cho developer
      this.flashMessageService.warning(
        this.translateService.instant('FLASH_MESSAGES.WARNING.GENERAL.INCOMPLETE_DATA'),
        {
          description: `Component ${this.data.component.name} chưa implement đầy đủ StrictModalComponent interface`
        }
      );
    }

    // Inject data vào component
    this.injectDataToComponent();

    // Inject MatBottomSheetRef để component có thể tự đóng bottom sheet
    this.injectBottomSheetRefToComponent();

    // Gọi onModalOpen() nếu component implement AdvancedModalComponent
    this.callOnModalOpen();

    this.componentRef.changeDetectorRef.detectChanges();
  }

  /**
   * Inject data vào component con
   */
  private injectDataToComponent(): void {
    if (!this.componentRef?.instance) return;

    const componentData = this.data.data;
    if (!componentData) return;

    const instance = this.componentRef.instance as any;

    // Method 1: Gọi updateData() nếu có (ModalComponentWithData)
    if (isModalComponentWithData(instance)) {
      instance.updateData!(componentData);
      return;
    }

    // Method 2: Assign vào property data
    if ('data' in instance) {
      instance.data = componentData;
      return;
    }

    // Method 3: Assign từng property
    if (typeof componentData === 'object') {
      Object.assign(instance, componentData);
    }
  }

  /**
   * Inject MatBottomSheetRef để component có thể tự đóng bottom sheet
   */
  private injectBottomSheetRefToComponent(): void {
    if (!this.componentRef?.instance) return;

    const instance = this.componentRef.instance as Record<string, unknown>;

    // Inject bottomSheetRef nếu component có property này
    if ('bottomSheetRef' in instance) {
      instance.bottomSheetRef = this.bottomSheetRef;
    }
  }

  /**
   * Gọi onModalOpen() nếu component implement AdvancedModalComponent
   */
  private callOnModalOpen(): void {
    if (!this.componentRef?.instance) return;

    const instance = this.componentRef.instance as Record<string, unknown>;

    // Gọi onModalOpen() nếu có
    if (typeof instance.onModalOpen === 'function') {
      instance.onModalOpen();
    }
  }

  /**
   * Gọi onModalClose() nếu component implement AdvancedModalComponent
   * @returns Promise<boolean> - true nếu có thể đóng, false nếu không
   */
  private async callOnModalClose(): Promise<boolean> {
    if (!this.componentRef?.instance) return true;

    const instance = this.componentRef.instance as any;

    // Gọi onModalClose() nếu có
    if (typeof instance.onModalClose === 'function') {
      const result = instance.onModalClose();

      // Xử lý cả sync và async result
      if (result instanceof Promise) {
        return await result;
      }

      return result !== false; // Mặc định là true nếu không return false
    }

    return true; // Mặc định cho phép đóng
  }

  /**
   * Lấy data từ component con để trả về
   */
  getComponentData(): any {
    if (!this.componentRef?.instance) {
      return this.data.data;
    }

    const instance = this.componentRef.instance;

    // Method 1: Sử dụng ModalComponent interface
    if (isModalComponent(instance)) {
      return instance.getModalResult();
    }

    // Method 2: Lấy từ property data
    if (instance && typeof instance === 'object' && 'data' in instance) {
      return (instance as any).data;
    }

    // Fallback: Trả về data ban đầu
    return this.data.data;
  }

  /**
   * Xử lý sự kiện Confirm
   */
  async onConfirm(): Promise<void> {
    // Kiểm tra validation trước
    if (!this.isComponentValid()) {
      // Thay thế console.warn bằng FlashMessageService
      this.flashMessageService.warning(
        this.translateService.instant('FLASH_MESSAGES.WARNING.FORM.VALIDATION_FAILED')
      );
      return;
    }

    // Gọi onModalClose() nếu có để kiểm tra có thể đóng không
    const canClose = await this.callOnModalClose();
    if (!canClose) {
      // Xóa console.log debug không cần thiết - component tự xử lý thông báo
      return;
    }
a: this.getComponentData()
    // };
    this.bottomSheetRef.dismiss(this.getComponentData());
  }

  /**
   * Xử lý sự kiện Cancel
   */
  onCancel(): void {
    this.bottomSheetRef.dismiss();
  }

  /**
   * Xử lý sự kiện Close
   */
  onClose(): void {
    this.bottomSheetRef.dismiss();
  }

  /**
   * Xử lý custom action
   */
  onCustomAction(actionName: string, payload?: any): void {
    this.bottomSheetRef.dismiss();
  }

  /**
   * Getter để template có thể truy cập TemplateRef type
   */
  get TemplateRef() {
    return TemplateRef;
  }
}
