// ===== FIELD LAYOUT STYLES =====
// Shared styles cho Dynamic Layout Renderer field components
// Định nghĩa layout 2 cột: label và value với styling cố định

/**
 * Shared Field Styles for Dynamic Layout Builder Field Components
 * 
 * Chứa CSS chung cho tất cả field components để đảm bảo tính nhất quán
 * về styling, đặc biệt là icon trong label và validation error display
 */

// ===== FIELD LABEL ICON STYLING =====
.field-label {
  display: flex;
  align-items: center;
  
  // Icon trong label - được thêm sau refactor
  .field-type-icon {
    // Kích thước icon phù hợp với text
    font-size: 1rem !important;
    width: 1rem !important;
    height: 1rem !important;
    
    // Vertical alignment với text
    display: inline-flex;
    align-items: center;
    justify-content: center;
    
    // Margin để tạo khoảng cách với text
    margin-right: 0.5rem;
    
    // Đ<PERSON>m bảo icon không bị shrink
    flex-shrink: 0;
    
    // Line height để align với text
    line-height: 1;
    
    // Vertical align fallback cho các browser cũ
    vertical-align: middle;
  }
  
  // Required asterisk styling
  .required-asterisk {
    color: var(--bs-danger);
    margin-left: 0.25rem;
    font-weight: bold;
  }
}

// ===== FIELD VALIDATION ERROR STYLING =====
.field-validation-errors {
  margin-top: 0.25rem;
  
  .validation-error-item {
    display: flex;
    align-items: flex-start;
    color: var(--bs-danger);
    font-size: 0.75rem;
    line-height: 1.3;
    margin-bottom: 0.125rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    // Error icon
    .error-icon {
      font-size: 0.875rem !important;
      width: 0.875rem !important;
      height: 0.875rem !important;
      margin-right: 0.25rem;
      margin-top: 0.0625rem; // Slight offset để align với text
      flex-shrink: 0;
    }
    
    // Error message text
    .error-message {
      flex: 1;
    }
  }
}

// ===== RESPONSIVE ADJUSTMENTS =====
@media (max-width: 576px) {
  .field-label {
    .field-type-icon {
      font-size: 0.875rem !important;
      width: 0.875rem !important;
      height: 0.875rem !important;
      margin-right: 0.375rem;
    }
  }
  
  .field-validation-errors {
    .validation-error-item {
      font-size: 0.6875rem;
      
      .error-icon {
        font-size: 0.75rem !important;
        width: 0.75rem !important;
        height: 0.75rem !important;
      }
    }
  }
}

// ===== ACCESSIBILITY IMPROVEMENTS =====
.field-label {
  // Đảm bảo label có thể focus được khi cần
  &:focus-within {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
    border-radius: 0.25rem;
  }
}

.field-validation-errors {
  // ARIA live region để screen reader đọc errors
  &[aria-live] {
    .validation-error-item {
      // Đảm bảo error messages có contrast tốt
      &::before {
        content: "⚠️";
        margin-right: 0.25rem;
        speak: never; // Không đọc emoji
      }
    }
  }
}

// ===== DARK THEME SUPPORT =====
@media (prefers-color-scheme: dark) {
  .field-label {
    .field-type-icon {
      // Icon color sẽ được set bởi getFieldIconColor() method
      // Chỉ cần đảm bảo contrast tốt trong dark mode
      opacity: 0.9;
    }
  }
  
  .field-validation-errors {
    .validation-error-item {
      color: #ff6b6b; // Màu đỏ sáng hơn cho dark mode
      
      .error-icon {
        color: #ff6b6b;
      }
    }
  }
}

// ===== ANIMATION EFFECTS =====
.field-validation-errors {
  // Smooth animation khi validation errors xuất hiện/biến mất
  transition: all 0.2s ease-in-out;
  
  .validation-error-item {
    // Fade in animation
    animation: fadeInError 0.3s ease-in-out;
  }
}

@keyframes fadeInError {
  from {
    opacity: 0;
    transform: translateY(-0.25rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ===== PRINT STYLES =====
@media print {
  .field-label {
    .field-type-icon {
      // Ẩn icon khi in để tiết kiệm mực
      display: none;
    }
  }
  
  .field-validation-errors {
    // Hiển thị errors khi in để debug
    color: #000 !important;
    
    .validation-error-item {
      .error-icon {
        display: none;
      }
    }
  }
}


// ===== FIELD ROW LAYOUT =====
.field-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;

  // Responsive: stack trên mobile
  @media (max-width: 576px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

// ===== VIEW MODE LAYOUT (2 cột) =====
.field-row-view {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;

  // Responsive: stack trên mobile
  @media (max-width: 576px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

// ===== FORM MODE LAYOUT (vertical) =====
.field-row-form {
  display: flex;
  flex-direction: column;
  margin-bottom: 1.5rem;
  gap: 0.5rem;
}

// ===== LABEL COLUMN =====
.field-label-column {
  width: 170px;
  text-align: right;
  flex-shrink: 0;

  // Responsive: full width trên mobile
  @media (max-width: 576px) {
    max-width: none;
    min-width: auto;
    text-align: left;
    padding-top: 0;
  }

  .field-label-text {
    // font-weight: 500;
    // font-size: 0.9rem;
    // line-height: 1.4;
    // margin: 0;

    // Bootstrap text-muted color
    color: #6c757d;

    .required-asterisk {
      color: #dc3545;
      margin-left: 0.25rem;
    }
  }

  .read-only-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #dc3545; // Màu đỏ để nổi bật
    cursor: help;
    margin-left: 0.5rem;

    &:hover {
      color: #b02a37;
    }
  }
}

// ===== LABEL COLUMN FOR FORM MODE =====
.field-row-form .field-label-column {
  max-width: none;
  min-width: auto;
  text-align: left;
  padding-top: 0;
  margin-bottom: 0.25rem;

  .field-label-text {
    font-weight: 600;
    color: #495057;
  }
}

// ===== VALUE COLUMN =====
.field-value-column {
  flex: 1;
  min-width: 0; // Cho phép shrink

  .field-view-value {
    word-wrap: break-word;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 0.5rem;

    // Empty state
    &.empty {
      color: #6c757d;
      font-style: italic;
    }

    // Content wrapper để text không bị đẩy bởi edit actions
    > span:not(.field-edit-actions span),
    > div:not(.field-edit-actions) {
      flex: 1;
      min-width: 0;
    }

    // Special handling cho các field có structure phức tạp
    .field-toggle-display,
    .field-user-info,
    .field-list-display,
    .textarea-content {
      flex: 1;
      min-width: 0;
    }
  }

  // Form controls styling
  .mat-mdc-form-field {
    width: 100%;
    font-size: 0.9rem;

    .mat-mdc-input-element {
      font-size: 0.9rem;
    }
  }

  // Toggle/Switch styling
  .mat-mdc-slide-toggle {
    .mdc-switch {
      --mdc-switch-selected-track-color: #0d6efd;
      --mdc-switch-selected-handle-color: #ffffff;
      --mdc-switch-unselected-track-color: #dee2e6;
      --mdc-switch-unselected-handle-color: #ffffff;
    }
  }
}

// ===== EDIT ACTIONS =====
.field-edit-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  gap: 0.25rem;
  align-items: center;
  flex-shrink: 0; // Không cho phép shrink
  margin-left: auto; // Đẩy về bên phải

  .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    white-space: nowrap; // Không wrap text

    &.btn-edit {
      background-color: #0d6efd;
      color: white;

      &:hover {
        background-color: #0b5ed7;
      }
    }

    &.btn-save {
      background-color: #198754;
      color: white;

      &:hover {
        background-color: #157347;
      }
    }

    &.btn-cancel {
      background-color: #6c757d;
      color: white;

      &:hover {
        background-color: #5c636a;
      }
    }

    mat-icon {
      font-size: 14px;
      width: 14px;
      height: 14px;
    }
  }
}

// Show edit actions on hover (chỉ trong view mode)
.field-row-view:hover .field-edit-actions {
  opacity: 1;
}

// Luôn hiển thị actions khi field đang trong edit mode
.field-edit-actions.editing {
  opacity: 1;
}

// ===== INLINE EDIT MODE =====
.field-inline-edit {
  display: flex;
  flex-direction: column; // Layout dọc: label trên, input dưới
  gap: 0.5rem;
  width: 100%;

  // Container cho form field hoặc toggle - cũng sử dụng layout dọc
  .field-input-container {
    display: flex;
    flex-direction: column; // Layout dọc cho input container
    align-items: flex-start;
    gap: 0.5rem;
    width: 100%;
  }

  .mat-mdc-form-field {
    flex: 1;
    margin-bottom: 0;
    min-width: 0; // Cho phép shrink
  }

  .toggle-form-container {
    flex: 1;
    min-width: 0;
  }

  .field-edit-actions {
    opacity: 1;
    flex-shrink: 0;
    margin-top: 0; // Reset margin-top vì đã có gap
    margin-left: 0; // Reset margin-left auto
    align-self: flex-start; // Align buttons to left
  }
}

// ===== FIELD VALIDATION ERRORS =====
.field-validation-errors {
  margin-top: 0.5rem;
  
  .validation-error-item {
    display: flex;
    align-items: center;
    color: #dc3545;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;

    .error-icon {
      font-size: 16px;
      margin-right: 0.25rem;
    }
  }
}

// ===== FIELD TOOLTIP =====
.field-tooltip {
  margin-top: 0.25rem;
  
  small {
    font-size: 0.8rem;
    line-height: 1.3;
    color: #6c757d;
  }
}

// ===== SPECIAL FIELD TYPES =====

// List display (ol/li)
.field-list-display {
  ol {
    margin: 0;
    padding-left: 1.2rem;
    
    // li {
    //   font-size: 0.9rem;
    //   line-height: 1.4;
    //   margin-bottom: 0.25rem;
    //   color: #495057;
    // }
  }
}

// Clickable URL styling
.field-url-link {
  color: #0d6efd;
  text-decoration: none;
  
  &:hover {
    color: #0a58ca;
    text-decoration: underline;
  }
  
  &:visited {
    color: #6f42c1;
  }
}

// User info display
.field-user-info {
  .user-name {
    font-weight: 500;
    color: #495057;
  }
  
  .user-email {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.125rem;
  }
}

// Toggle display in view mode
.field-toggle-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  .toggle-icon {
    font-size: 20px;
    
    &.enabled {
      color: #198754;
    }
    
    &.disabled {
      color: #6c757d;
    }
  }
  
  .toggle-label {
    font-size: 0.9rem;
    color: #495057;
  }
}

// ===== DARK THEME SUPPORT =====
@media (prefers-color-scheme: dark) {
  .field-label-column .field-label-text {
    color: #adb5bd;
  }
  
  .field-value-column .field-view-value {
    color: #dee2e6;
    
    &.empty {
      color: #6c757d;
    }
  }
  
  .field-validation-errors .validation-error-item {
    color: #f5c2c7;
  }
  
  .field-tooltip small {
    color: #adb5bd;
  }
  
  .field-list-display ol li {
    color: #dee2e6;
  }
  
  .field-user-info {
    .user-name {
      color: #dee2e6;
    }
    
    .user-email {
      color: #adb5bd;
    }
  }
  
  .field-toggle-display .toggle-label {
    color: #dee2e6;
  }
}

// ===== RESPONSIVE ADJUSTMENTS =====
@media (max-width: 768px) {
  .field-row {
    margin-bottom: 1.5rem;
  }
  
  .field-label-column {
    margin-bottom: 0.5rem;
  }
}
