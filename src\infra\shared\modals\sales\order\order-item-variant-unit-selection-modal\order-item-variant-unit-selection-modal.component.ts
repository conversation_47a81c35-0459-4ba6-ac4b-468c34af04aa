import { Component, Inject, Optional, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ProductUnit } from 'salehub_shared_contracts';
import { ProductVariants } from 'salehub_shared_contracts/requests/shared/product';
import {
  OrderItemVariantUnitSelectionModalData,
  OrderItemVariantUnitSelectionResult
} from '@shared/models/view/order-item-variant-unit-selection.model';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

@Component({
  selector: 'app-order-item-variant-unit-selection-modal',
  templateUrl: './order-item-variant-unit-selection-modal.component.html',
  styleUrls: ['./order-item-variant-unit-selection-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatRadioModule,
    FormsModule,
    TranslateModule
  ]
})
export class OrderItemVariantUnitSelectionModalComponent implements StrictModalComponent<OrderItemVariantUnitSelectionModalData, OrderItemVariantUnitSelectionResult> {
  // Signals để lưu trữ state
  selectedAttributes = signal<{ [key: string]: string }>({});
  selectedUnit = signal<ProductUnit | null>(null);

  // Biến để lưu trữ các thuộc tính duy nhất và giá trị của chúng
  attributeGroups: { [key: string]: string[] } = {};

  // Computed signal để kiểm tra nút xác nhận có được enable không
  isConfirmEnabled = computed(() => {
    const hasRequiredVariant = this.data.variants.length > 0;
    const hasRequiredUnit = this.data.units.length > 0;

    if (!hasRequiredVariant && !hasRequiredUnit) {
      // Nếu không có cả variant và unit, luôn enable
      return true;
    }

    if (hasRequiredVariant && hasRequiredUnit) {
      // Nếu có cả variant và unit, phải chọn cả hai
      return this.findMatchingVariant() !== null && this.selectedUnit() !== null;
    }

    if (hasRequiredVariant) {
      // Nếu chỉ có variant, phải chọn variant
      return this.findMatchingVariant() !== null;
    }

    if (hasRequiredUnit) {
      // Nếu chỉ có unit, phải chọn unit
      return this.selectedUnit() !== null;
    }

    return false;
  });

  /**
   * Lấy số lượng nhóm thuộc tính
   * @returns Số lượng nhóm thuộc tính
   */
  getAttributeGroupsCount(): number {
    return Object.keys(this.attributeGroups).length;
  }

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: OrderItemVariantUnitSelectionModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: OrderItemVariantUnitSelectionModalData,
    @Optional() private dialogRef?: MatDialogRef<OrderItemVariantUnitSelectionModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<OrderItemVariantUnitSelectionModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {
      variants: [],
      currentValue: {
        variant: { variantId: '', attributes: [] } as ProductVariants[number],
        unit: {} as ProductUnit
      },
      units: []
    };

    this.data.variants = this.data.variants || [];
    this.data.units = this.data.units || [];
    this.initializeSelections();
    this.updateAvailableAttributes();
  }

  // Dữ liệu từ inject
  data: OrderItemVariantUnitSelectionModalData;

  // Khởi tạo các lựa chọn ban đầu từ currentValue
  private initializeSelections(): void {
    // Khởi tạo thuộc tính được chọn
    if (this.data.currentValue?.variant && this.data.currentValue.variant.attributes) {
      const initialAttributes: { [key: string]: string } = {};
      this.data.currentValue.variant.attributes.forEach((attr: { name: string; value: string }) => {
        initialAttributes[attr.name] = attr.value;
      });
      this.selectedAttributes.set(initialAttributes);
    }

    // Khởi tạo đơn vị được chọn
    if (this.data.currentValue?.unit) {
      this.selectedUnit.set(this.data.currentValue.unit);
    } else {
      // Nếu không có đơn vị được chọn, chọn đơn vị cơ bản
      const baseUnit = this.data.units.find(unit => unit.isBaseUnit);
      if (baseUnit) {
        this.selectedUnit.set(baseUnit);
      }
    }
  }

  // Cập nhật danh sách các thuộc tính có sẵn dựa trên lựa chọn hiện tại
  private updateAvailableAttributes(): void {
    const currentSelection = this.selectedAttributes();
    const newAttributeGroups: { [key: string]: string[] } = {};

    // Lấy tất cả các tên thuộc tính có thể có
    const allAttributeNames = Array.from(new Set(
      (this.data.variants || []).flatMap(v => (v.attributes || []).map((a: { name: string; value: string }) => a.name))
    ));

    // Với mỗi tên thuộc tính
    allAttributeNames.forEach(attrName => {
      // Lọc các variants phù hợp với lựa chọn hiện tại
      const compatibleVariants = (this.data.variants || []).filter(variant => Object.entries(currentSelection).every(([name, value]) => {
        // Bỏ qua thuộc tính đang xét
        if (name === attrName) return true;
        // Bỏ qua các thuộc tính không có giá trị (đã bỏ chọn)
        if (!value) return true;
        // Kiểm tra xem variant có khớp với các thuộc tính đã chọn không
        return (variant.attributes || []).some((attr: { name: string; value: string }) =>
          attr.name === name && attr.value === value
        );
      }));

      // Lấy các giá trị có thể có cho thuộc tính này
      newAttributeGroups[attrName] = Array.from(new Set(
        compatibleVariants
          .flatMap(v => v.attributes || [])
          .filter((attr: { name: string; value: string }) => attr.name === attrName)
          .map((attr: { name: string; value: string }) => attr.value)
      ));
    });

    this.attributeGroups = newAttributeGroups;
  }

  // Xử lý khi chọn một thuộc tính
  selectAttribute(name: string, value: string): void {
    const currentAttributes = this.selectedAttributes();

    // Nếu giá trị mới giống giá trị hiện tại, bỏ chọn nó
    if (currentAttributes[name] === value) {
      const newAttributes = { ...currentAttributes };
      delete newAttributes[name];
      this.selectedAttributes.set(newAttributes);
    } else {
      // Nếu không, chọn giá trị mới
      this.selectedAttributes.set({
        ...currentAttributes,
        [name]: value
      });
    }

    // Cập nhật lại danh sách các thuộc tính có sẵn
    this.updateAvailableAttributes();
  }

  // Xử lý khi chọn một đơn vị
  selectUnit(unit: ProductUnit): void {
    this.selectedUnit.set(unit);
  }

  // Kiểm tra xem một thuộc tính có được chọn không
  isAttributeSelected(name: string, value: string): boolean {
    return this.selectedAttributes()[name] === value;
  }

  // Tìm variant phù hợp với các thuộc tính đã chọn
  private findMatchingVariant(): ProductVariants[number] | null {
    const selectedAttrs = this.selectedAttributes();
    // Nếu không có thuộc tính nào được chọn, trả về null
    if (Object.keys(selectedAttrs).length === 0) return null;

    const matchedVariant = (this.data.variants || []).find(variant =>
      (variant.attributes || []).every((attr: { name: string; value: string }) =>
        selectedAttrs[attr.name] === attr.value
      )
    );

    if (!matchedVariant) return null;

    return matchedVariant;
  }

  // Xử lý khi nhấn nút hủy
  cancel(): void {
    this.close(null);
  }

  // Xử lý khi nhấn nút xác nhận
  confirm(): void {
    const matchingVariant = this.findMatchingVariant();
    const selectedUnit = this.selectedUnit();

    const result: OrderItemVariantUnitSelectionResult = {
      variant: matchingVariant,
      unit: selectedUnit
    };

    this.close(result);
  }

  // Đóng modal
  private close(result: OrderItemVariantUnitSelectionResult | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): OrderItemVariantUnitSelectionResult {
    const matchingVariant = this.findMatchingVariant();
    const selectedUnit = this.selectedUnit();

    return {
      variant: matchingVariant,
      unit: selectedUnit
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.isConfirmEnabled();
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: OrderItemVariantUnitSelectionModalData): void {
    this.data = data;

    // Khởi tạo lại các thuộc tính
    this.attributeGroups = {};
    if (data.variants && data.variants.length > 0) {
      data.variants.forEach(variant => {
        if (variant.attributes) {
          variant.attributes.forEach((attr: { name: string; value: string }) => {
            if (!this.attributeGroups[attr.name]) {
              this.attributeGroups[attr.name] = [];
            }
            if (!this.attributeGroups[attr.name].includes(attr.value)) {
              this.attributeGroups[attr.name].push(attr.value);
            }
          });
        }
      });
    }

    // Khởi tạo lại giá trị đã chọn từ currentValue
    if (data.currentValue) {
      if (data.currentValue.variant && data.currentValue.variant.attributes) {
        const initialAttributes: { [key: string]: string } = {};
        data.currentValue.variant.attributes.forEach((attr: { name: string; value: string }) => {
          initialAttributes[attr.name] = attr.value;
        });
        this.selectedAttributes.set(initialAttributes);
      }

      if (data.currentValue.unit) {
        this.selectedUnit.set(data.currentValue.unit);
      }
    }

    // Cập nhật lại danh sách các thuộc tính có sẵn
    this.updateAvailableAttributes();
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Kiểm tra dữ liệu bắt buộc
    if (!this.data.variants && !this.data.units) {
      console.warn('OrderItemVariantUnitSelectionModalComponent: No variants or units provided');
    }
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
