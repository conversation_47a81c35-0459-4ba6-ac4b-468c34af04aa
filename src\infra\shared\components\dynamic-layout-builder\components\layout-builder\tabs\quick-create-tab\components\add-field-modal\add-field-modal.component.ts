import { Component, inject, signal, computed, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';
import { MatCardModule } from '@angular/material/card';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { StrictModalComponent } from '@shared/components/standard-dialog/interfaces/modal-component.interface';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { Field, FieldType } from '@domain/entities/field.entity';
import { Section } from '@/shared/components/dynamic-layout-builder/models/dynamic-layout-config.dto';
import { getFieldIcon, getFieldIconColor, getFieldTypeLabel } from '@/shared/components/dynamic-layout-builder/utils/field.utils';

/**
 * Interface cho data truyền vào modal
 */
export interface AddFieldModalData {
  availableFields: Field[];
  sections?: Section[];
}

/**
 * Modal component để chọn field từ danh sách available fields
 * Implement StrictModalComponent để tương thích với ResponsiveModalService
 */
@Component({
  selector: 'app-add-field-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatListModule,
    MatDividerModule,
    MatCardModule,
    TranslateModule
  ],
  templateUrl: './add-field-modal.component.html',
  styleUrls: ['./add-field-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AddFieldModalComponent implements StrictModalComponent<AddFieldModalData, Field> {
  private translateService = inject(TranslateService);
  private responsiveModalService = inject(ResponsiveModalService);

  // Modal data
  private modalData = signal<AddFieldModalData>({ availableFields: [] });

  // Modal result
  private modalResult = signal<Field | undefined>(undefined);

  // Computed properties
  availableFields = computed(() => this.modalData().availableFields || []);
  sections = computed(() => this.modalData().sections || []);

  // Group fields by section
  fieldsBySection = computed(() => {
    const sections = this.sections();
    const fields = this.availableFields();
    
    if (sections.length === 0) {
      // Nếu không có sections, group tất cả fields vào một group
      return [{
        title: this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.ADD_FIELD_MODAL.ALL_FIELDS'),
        fields: fields
      }];
    }

    // Group fields theo section
    return sections.map(section => ({
      title: section.title,
      fields: section.fields || []
    })).filter(group => group.fields.length > 0);
  });

  /**
   * Implement StrictModalComponent interface
   */
  updateData(data: AddFieldModalData): void {
    console.log('📝 AddFieldModal: Updating data', data);
    this.modalData.set(data);
  }

  getModalResult(): Field {
    return this.modalResult() || {} as Field;
  }

  isValid(): boolean {
    // Modal luôn valid vì chỉ cần chọn field hoặc cancel
    return true;
  }

  onModalOpen(): void {
    console.log('🚀 AddFieldModal: Modal opened');
  }

  onModalClose(): boolean {
    console.log('🔒 AddFieldModal: Modal closed');
    return true; // Cho phép đóng modal
  }

  /**
   * Xử lý khi user chọn field
   */
  onFieldSelect(field: Field): void {
    console.log('✅ AddFieldModal: Field selected', field);
    // Set result và đóng modal
    this.modalResult.set(field);
    this.closeModal(field);
  }

  /**
   * Đóng modal với result
   */
  private closeModal(result?: Field): void {
    // Sử dụng ResponsiveModalService để đóng modal
    // Service sẽ tự động handle việc cleanup và return result
    this.responsiveModalService.closeModal(result);
  }

  /**
   * Đóng modal không có result (cancel)
   */
  onCancel(): void {
    console.log('❌ AddFieldModal: Cancelled');
    this.modalResult.set(undefined);
    this.closeModal(undefined);
  }

  /**
   * Utility methods cho template
   */
  getFieldIcon(fieldType: string): string {
    return getFieldIcon(fieldType as FieldType);
  }

  getFieldIconColor(fieldType: string): string {
    return getFieldIconColor(fieldType as FieldType);
  }

  getFieldTypeLabel(fieldType: string): string {
    return getFieldTypeLabel(fieldType as FieldType, this.translateService);
  }

  /**
   * Track by function cho ngFor
   */
  trackByFieldId(index: number, field: Field): string {
    return field._id || `${field.type}-${index}`;
  }

  trackBySectionTitle(index: number, section: any): string {
    return section.title || `section-${index}`;
  }
}
