import { Component, signal, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { StrictModalComponent } from '../../../models/view/modal-component.interface';

/**
 * Component test để demo validation với StrictModalComponent interface
 */
export interface ValidationTestData {
  name: string;
  email: string;
  agreeToTerms: boolean;
}

export interface ValidationTestResult {
  name: string;
  email: string;
  agreeToTerms: boolean;
  submittedAt: Date;
}

@Component({
  selector: 'app-validation-test',
  standalone: true,
  imports: [
    CommonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatButtonModule,
    FormsModule,
    TranslateModule
  ],
  template: `
    <div class="validation-test-container p-3">
      <h4 class="mb-3">🧪 Test Validation Modal Component</h4>

      <div class="form-section">
        <!-- Name Field -->
        <mat-form-field class="w-100 mb-3">
          <mat-label>Tên người dùng *</mat-label>
          <input
            matInput
            [(ngModel)]="formData().name"
            (ngModelChange)="updateName($event)"
            placeholder="Nhập tên của bạn"
            required>
          @if (!isNameValid()) {
            <mat-error>Tên không được để trống và phải có ít nhất 2 ký tự</mat-error>
          }
        </mat-form-field>

        <!-- Email Field -->
        <mat-form-field class="w-100 mb-3">
          <mat-label>Email *</mat-label>
          <input
            matInput
            type="email"
            [(ngModel)]="formData().email"
            (ngModelChange)="updateEmail($event)"
            placeholder="Nhập email của bạn"
            required>
          @if (!isEmailValid()) {
            <mat-error>Email không hợp lệ</mat-error>
          }
        </mat-form-field>

        <!-- Terms Checkbox -->
        <div class="mb-3">
          <mat-checkbox
            [(ngModel)]="formData().agreeToTerms"
            (ngModelChange)="updateAgreeToTerms($event)"
            color="primary">
            Tôi đồng ý với các điều khoản và điều kiện *
          </mat-checkbox>
          @if (!formData().agreeToTerms) {
            <div class="text-danger small mt-1">
              Bạn phải đồng ý với điều khoản để tiếp tục
            </div>
          }
        </div>

        <!-- Validation Status -->
        <div class="validation-status p-2 rounded mb-3"
             [class.bg-success-subtle]="isValid()"
             [class.bg-danger-subtle]="!isValid()">
          <strong>Trạng thái validation:</strong>
          @if (isValid()) {
            <span class="text-success">✅ Form hợp lệ - có thể submit</span>
          } @else {
            <span class="text-danger">❌ Form chưa hợp lệ - nút Xác nhận sẽ bị disable</span>
          }
        </div>

        <!-- Debug Info -->
        <div class="debug-info bg-light p-2 rounded small">
          <strong>Debug Info:</strong><br>
          Name valid: {{ isNameValid() }}<br>
          Email valid: {{ isEmailValid() }}<br>
          Terms agreed: {{ formData().agreeToTerms }}<br>
          Overall valid: {{ isValid() }}
        </div>
      </div>
    </div>
  `,
  styles: [`
    .validation-test-container {
      max-width: 500px;
    }

    .validation-status {
      border: 1px solid #dee2e6;
    }

    .bg-success-subtle {
      background-color: #d1e7dd !important;
      border-color: #badbcc !important;
    }

    .bg-danger-subtle {
      background-color: #f8d7da !important;
      border-color: #f5c2c7 !important;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ValidationTestComponent implements StrictModalComponent<ValidationTestData, ValidationTestResult> {

  // Form data signals
  formData = signal<ValidationTestData>({
    name: '',
    email: '',
    agreeToTerms: false
  });

  /**
   * Update data từ bên ngoài
   */
  updateData(data: ValidationTestData): void {
    console.log('ValidationTestComponent.updateData() called with:', data);
    this.formData.set({ ...data });
  }

  /**
   * Update individual fields
   */
  updateName(name: string): void {
    this.formData.update(data => ({ ...data, name }));
  }

  updateEmail(email: string): void {
    this.formData.update(data => ({ ...data, email }));
  }

  updateAgreeToTerms(agreeToTerms: boolean): void {
    this.formData.update(data => ({ ...data, agreeToTerms }));
  }

  /**
   * Lấy kết quả từ component
   */
  getModalResult(): ValidationTestResult {
    const currentData = this.formData();
    return {
      name: currentData.name,
      email: currentData.email,
      agreeToTerms: currentData.agreeToTerms,
      submittedAt: new Date()
    };
  }

  /**
   * Validate toàn bộ form
   */
  isValid(): boolean {
    return this.isNameValid() && this.isEmailValid() && this.formData().agreeToTerms;
  }

  /**
   * Validate tên
   */
  isNameValid(): boolean {
    const name = this.formData().name.trim();
    return name.length >= 2;
  }

  /**
   * Validate email
   */
  isEmailValid(): boolean {
    const email = this.formData().email.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return email.length > 0 && emailRegex.test(email);
  }

  /**
   * Method được gọi khi modal mở
   */
  onModalOpen?(): void {
    console.log('ValidationTestComponent: Modal đã được mở');
  }

  /**
   * Method được gọi trước khi modal đóng
   */
  onModalClose?(): boolean | Promise<boolean> {
    console.log('ValidationTestComponent: Chuẩn bị đóng modal');

    // Có thể thêm logic kiểm tra thay đổi chưa lưu ở đây
    if (!this.isValid()) {
      console.warn('ValidationTestComponent: Form chưa valid, nhưng vẫn cho phép đóng');
    }

    return true; // Cho phép đóng
  }
}
