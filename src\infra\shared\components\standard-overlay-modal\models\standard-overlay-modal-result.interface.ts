/**
 * Interface cho kết quả trả về từ StandardOverlayModal
 * Tương đồng với StandardDialogResult và StandardBottomSheetResult
 */
export interface StandardOverlayModalResult<TResult = any> {
  /**
   * D<PERSON> liệu kết quả từ modal component
   */
  data?: TResult;

  /**
   * Action được thực hiện để đóng modal
   */
  action?: 'confirm' | 'cancel' | 'close' | 'backdrop' | 'escape' | string;

  /**
   * Có phải modal bị đóng bởi user action không
   */
  dismissed?: boolean;

  /**
   * Timestamp khi modal được đóng
   */
  timestamp?: Date;

  /**
   * Metadata bổ sung
   */
  metadata?: {
    /**
     * Thời gian modal được mở (ms)
     */
    duration?: number;

    /**
     * Vị trí modal khi đóng
     */
    position?: {
      x: number;
      y: number;
    };

    /**
     * Validation state khi đóng
     */
    isValid?: boolean;

    /**
     * Custom metadata từ component
     */
    [key: string]: any;
  };
}

/**
 * Type helper cho kết quả thành công
 */
export interface StandardOverlayModalSuccessResult<TResult = any> extends StandardOverlayModalResult<TResult> {
  data: TResult;
  action: 'confirm';
  dismissed: false;
}

/**
 * Type helper cho kết quả bị hủy
 */
export interface StandardOverlayModalCancelledResult extends StandardOverlayModalResult {
  data: undefined;
  action: 'cancel' | 'close' | 'backdrop' | 'escape';
  dismissed: true;
}

/**
 * Union type cho tất cả các loại kết quả
 */
export type StandardOverlayModalAnyResult<TResult = any> = 
  | StandardOverlayModalSuccessResult<TResult>
  | StandardOverlayModalCancelledResult;

/**
 * Factory functions để tạo result objects
 */
export class StandardOverlayModalResultFactory {
  /**
   * Tạo kết quả thành công
   */
  static success<TResult>(data: TResult, metadata?: any): StandardOverlayModalSuccessResult<TResult> {
    return {
      data,
      action: 'confirm',
      dismissed: false,
      timestamp: new Date(),
      metadata
    };
  }

  /**
   * Tạo kết quả bị hủy
   */
  static cancelled(
    action: 'cancel' | 'close' | 'backdrop' | 'escape' = 'cancel',
    metadata?: any
  ): StandardOverlayModalCancelledResult {
    return {
      data: undefined,
      action,
      dismissed: true,
      timestamp: new Date(),
      metadata
    };
  }

  /**
   * Tạo kết quả custom
   */
  static custom<TResult>(
    data: TResult,
    action: string,
    dismissed: boolean = false,
    metadata?: any
  ): StandardOverlayModalResult<TResult> {
    return {
      data,
      action,
      dismissed,
      timestamp: new Date(),
      metadata
    };
  }
}
