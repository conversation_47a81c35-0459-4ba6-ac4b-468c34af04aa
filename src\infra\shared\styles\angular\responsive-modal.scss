.responsive-dialog {
  --mat-dialog-container-elevation-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
  --mdc-dialog-container-shape: 20px 20px 15px 15px;
  --mdc-dialog-subhead-size: 20px;
  --mdc-dialog-subhead-weight: 600;
  --mat-dialog-headline-padding: 10px 24px 10px 24px;
  --mat-dialog-container-min-width: 700px;
  --mat-dialog-container-max-width: 95vw;

  .mat-mdc-dialog-surface {
    border:1.5px solid #000;
    border-top: 6px solid #000;
  }
  .mat-mdc-dialog-title {
    position: relative;
    border-bottom: 1.5px solid #e7e7e7;
    margin-bottom: 1em;
  }
  .mat-mdc-dialog-actions {
    background: #efefef;
    border-top: 1.5px solid #cdcdcd;
  }
  .mat-dialog-close {
    position: absolute;
    top: 20px;
    right: 20px;
    cursor: pointer;

    .mat-icon {
      font-size: 28px;
    }
  }
}


// ResponsiveModalService Overlay Styles
// CSS cho overlay positioning và responsive behavior

// CDK Overlay Panel Styles
.responsive-overlay-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.24);
  padding: 24px;
  max-height: calc(100vh - 32px);
  overflow: auto;

  // Animation cho overlay
  animation: overlayFadeIn 0.2s ease-out;

  @media (max-width: 575px) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
}

// Transparent backdrop cho CDK Overlay
.cdk-overlay-transparent-backdrop {
  background-color: rgba(0, 0, 0, 0.1);

  &.cdk-overlay-backdrop-showing {
    opacity: 1;
  }
}


// Animations
@keyframes overlayFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bottomSheetSlideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// Positioning utilities cho overlay
.overlay-position-top-left {
  .mat-mdc-dialog-container {
    transform-origin: top left;
  }
}

.overlay-position-top-right {
  .mat-mdc-dialog-container {
    transform-origin: top right;
  }
}

.overlay-position-bottom-left {
  .mat-mdc-dialog-container {
    transform-origin: bottom left;
  }
}

.overlay-position-bottom-right {
  .mat-mdc-dialog-container {
    transform-origin: bottom right;
  }
}

.overlay-position-center {
  .mat-mdc-dialog-container {
    transform-origin: center;
  }
}

// Responsive behavior
@media (max-width: 767px) {
  // Trên mobile, overlay dialog sẽ có behavior giống modal thông thường
  .responsive-dialog-overlay {
    .mat-mdc-dialog-container {
      position: fixed !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      max-width: calc(100vw - 32px);
      max-height: calc(100vh - 64px);
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .responsive-overlay-panel {
    background: #2d2d2d;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.48);
  }

  .cdk-overlay-transparent-backdrop {
    background-color: rgba(0, 0, 0, 0.2);
  }

  .responsive-dialog-overlay {
    .mat-mdc-dialog-container {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.48);
    }

    .cdk-overlay-backdrop {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  .responsive-bottom-sheet-overlay {
    .mat-mdc-bottom-sheet-container {
      box-shadow: 0 -4px 32px rgba(0, 0, 0, 0.48);
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .responsive-overlay-panel {
    border: 2px solid;
  }

  .responsive-dialog-overlay {
    .mat-mdc-dialog-container {
      border: 2px solid;
    }
  }

  .responsive-bottom-sheet-overlay {
    .mat-mdc-bottom-sheet-container {
      border-top: 2px solid;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .responsive-overlay-panel,
  .responsive-dialog-overlay .mat-mdc-dialog-container,
  .responsive-bottom-sheet-overlay .mat-mdc-bottom-sheet-container {
    animation: none;
  }
  
  @keyframes overlayFadeIn {
    from, to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes bottomSheetSlideUp {
    from, to {
      transform: translateY(0);
    }
  }
}

// Animations for CDK Overlay
@keyframes overlayFadeIn {
  from {
    transform: scale(0.9) translateY(-10px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes overlaySlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

// ===================================================================
// THREADS-LIKE ANIMATION EFFECTS
// ===================================================================
// Hiệu ứng animation giống ứng dụng Threads
// 1. Scale effect cho container/backdrop (thu nhỏ background)
// 2. Slide-up effect cho mat-menu/bottom-sheet (trượt lên từ dưới)
// 3. App drawer scale effect cho StandardBottomSheetService

// Container/Backdrop scale effect - Thu nhỏ background khi mở mat-menu
.threads-menu-backdrop-scale {
  transition: transform 250ms cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  transform-origin: center center;

  // Khi mat-menu mở, thu nhỏ container/backdrop
  &.mat-menu-open {
    transform: scale(0.95);
  }

  // Khi mat-menu đóng, trở về kích thước bình thường
  &.mat-menu-closed {
    transform: scale(1);
  }
}

// Mat-menu slide-up animation - Menu trượt lên từ dưới
.mat-mdc-menu-panel.threads-menu-slide-up {
  // Initial state - Menu ở dưới và trong suốt
  transform: translateY(100%) scale(0.9);
  opacity: 0;
  transition: all 280ms cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: center bottom;

  // Animation khi menu xuất hiện
  &.ng-animating {
    transform: translateY(0) scale(1);
    opacity: 1;
  }

  // Final state - Menu hiển thị hoàn toàn
  &.mat-mdc-menu-panel-animating-done {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

// Override Angular Material default animations cho mat-menu
.mat-mdc-menu-panel {
  // Disable default mat-menu animations để sử dụng custom animation
  &.threads-menu-slide-up {
    animation: none !important;

    // Custom slide-up animation
    &.ng-trigger-slideInOut {
      animation: threadsMenuSlideUp 280ms cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
    }

    // Animation khi đóng menu
    &.ng-trigger-slideInOut.ng-animating {
      animation: threadsMenuSlideDown 200ms cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
    }
  }
}

// Keyframes cho slide-up animation
@keyframes threadsMenuSlideUp {
  0% {
    transform: translateY(100%) scale(0.9);
    opacity: 0;
  }
  60% {
    transform: translateY(-5%) scale(1.02);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

// Keyframes cho slide-down animation (khi đóng)
@keyframes threadsMenuSlideDown {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(100%) scale(0.9);
    opacity: 0;
  }
}

// Enhanced backdrop effect với blur
.threads-menu-backdrop-enhanced {
  position: relative;
  transition: all 250ms cubic-bezier(0.25, 0.8, 0.25, 1);

  // Khi menu mở
  &.mat-menu-open {
    transform: scale(0.95);
    filter: blur(2px);

    // Overlay effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 8px;
      z-index: -1;
      opacity: 1;
      transition: opacity 250ms ease-out;
    }
  }

  // Khi menu đóng
  &.mat-menu-closed {
    transform: scale(1);
    filter: none;

    &::before {
      opacity: 0;
    }
  }
}

// Responsive behavior cho mobile
@media (max-width: 767px) {
  .mat-mdc-menu-panel.threads-menu-slide-up {
    // Trên mobile, menu slide up từ bottom của screen
    transform: translateY(100vh) scale(0.95);

    &.ng-animating,
    &.mat-mdc-menu-panel-animating-done {
      transform: translateY(0) scale(1);
    }
  }

  // Mobile backdrop effect nhẹ hơn
  .threads-menu-backdrop-scale.mat-menu-open {
    transform: scale(0.98);
  }

  .threads-menu-backdrop-enhanced.mat-menu-open {
    transform: scale(0.98);
    filter: blur(1px);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .threads-menu-backdrop-enhanced.mat-menu-open::before {
    background: rgba(255, 255, 255, 0.05);
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .threads-menu-backdrop-scale,
  .threads-menu-backdrop-enhanced,
  .mat-mdc-menu-panel.threads-menu-slide-up {
    transition: none !important;
    animation: none !important;
    transform: none !important;
    filter: none !important;
  }

  // Override keyframes để disable animation
  @keyframes threadsMenuSlideUp {
    from, to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  @keyframes threadsMenuSlideDown {
    from, to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
}

// ===================================================================
// APP DRAWER ANIMATION FOR STANDARDBOTTOMSHEETSERVICE
// ===================================================================
// Threads-like animation cho .app-drawer element khi StandardBottomSheetService mở bottom sheet

// App drawer scale animation - Tự động áp dụng bởi StandardBottomSheetService
.app-drawer {
  // Default state - Sẵn sàng cho animation
  transition: transform 250ms cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: center center;

  // Khi có Threads animation active
  &.threads-animation-active {
    transform: scale(0.95);

    // Enhanced effect với subtle blur (optional)
    filter: blur(0.5px);

    // Overlay effect để tạo depth
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 8px;
      z-index: -1;
      opacity: 1;
      transition: opacity 250ms ease-out;
      pointer-events: none;
    }
  }

  // Khi đang closing - parallel animation với bottom sheet slide-down
  &.threads-animation-closing {
    // Smooth scale-up animation trở về kích thước bình thường
    transform: scale(1);
    filter: blur(0);

    // Fade out overlay effect
    &::before {
      opacity: 0;
      transition: opacity 280ms ease-out;
    }

    // Ensure smooth transition
    transition: transform 280ms cubic-bezier(0.25, 0.8, 0.25, 1),
                filter 280ms cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  // Responsive behavior
  @media (max-width: 767px) {
    &.threads-animation-active {
      // Mobile: Scale effect nhẹ hơn
      transform: scale3d(0.95, 0.99, 0.99);
      filter: blur(0.25px);
      background: #eee;
      border-radius: 10px;

    }
  }
}

// Bottom sheet panel animation - Tự động áp dụng bởi StandardBottomSheetService
.standard-bottom-sheet-panel {
  // Khi có Threads animation classes
  &.threads-menu-slide-up {
    // Initial state - Bottom sheet ở dưới và trong suốt
    transform: translateY(100%) scale(0.95);
    opacity: 0;
    transition: all 280ms cubic-bezier(0.25, 0.8, 0.25, 1);
    transform-origin: center bottom;

    // Animation khi bottom sheet xuất hiện
    &.ng-animating {
      transform: translateY(0) scale(1);
      opacity: 1;
    }

    // Final state - Bottom sheet hiển thị hoàn toàn
    &.mat-mdc-bottom-sheet-container {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
}

// Mat Bottom Sheet Container - Override để có parallel animation
.mat-mdc-bottom-sheet-container {
  // Parallel slide-down animation khi đóng
  &.threads-parallel-closing {
    transform: translateY(100%) scale(0.95) !important;
    opacity: 0 !important;
    transition: all 280ms cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  }

  // Ensure smooth opening animation
  &.threads-menu-slide-up {
    animation: standardBottomSheetSlideUp 280ms cubic-bezier(0.25, 0.8, 0.25, 1) forwards;
  }
}

// Enhanced keyframes cho StandardBottomSheetService
@keyframes standardBottomSheetSlideUp {
  0% {
    transform: translateY(100%) scale(0.95);
    opacity: 0;
  }
  50% {
    transform: translateY(10%) scale(0.98);
    opacity: 0.7;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes standardBottomSheetSlideDown {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  100% {
    transform: translateY(100%) scale(0.95);
    opacity: 0;
  }
}

// Dark mode support cho app drawer
@media (prefers-color-scheme: dark) {
  .app-drawer.threads-animation-active::before {
    background: rgba(255, 255, 255, 0.03);
  }
}

// Reduced motion support cho app drawer
@media (prefers-reduced-motion: reduce) {
  .app-drawer {
    transition: none !important;
    transform: none !important;
    filter: none !important;

    &.threads-animation-active {
      transform: none !important;
      filter: none !important;

      &::before {
        opacity: 0 !important;
      }
    }
  }

  .standard-bottom-sheet-panel.threads-menu-slide-up {
    transition: none !important;
    animation: none !important;
    transform: none !important;
    opacity: 1 !important;
  }

  @keyframes standardBottomSheetSlideUp {
    from, to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  @keyframes standardBottomSheetSlideDown {
    from, to {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }
}

// ===================================================================
// UTILITY CLASSES FOR EASY IMPLEMENTATION
// ===================================================================
// Các class tiện ích để dễ dàng áp dụng hiệu ứng Threads-like

// Class chính để áp dụng toàn bộ hiệu ứng Threads
.threads-menu-effect {
  // Áp dụng backdrop scale effect
  &.threads-menu-container {
    @extend .threads-menu-backdrop-scale;
  }

  // Áp dụng enhanced backdrop với blur
  &.threads-menu-container-enhanced {
    @extend .threads-menu-backdrop-enhanced;
  }

  // Áp dụng slide-up effect cho mat-menu
  .mat-mdc-menu-panel {
    @extend .threads-menu-slide-up;
  }
}

// Quick apply classes
.apply-threads-menu-basic {
  @extend .threads-menu-backdrop-scale;

  .mat-mdc-menu-panel {
    @extend .threads-menu-slide-up;
  }
}

.apply-threads-menu-enhanced {
  @extend .threads-menu-backdrop-enhanced;

  .mat-mdc-menu-panel {
    @extend .threads-menu-slide-up;
  }
}

// ===================================================================
// INTEGRATION EXAMPLES (COMMENTED OUT)
// ===================================================================
/*
// Cách sử dụng trong component:

// 1. Basic Threads effect:
<div class="apply-threads-menu-basic">
  <button [matMenuTriggerFor]="menu">Open Menu</button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item>Item 1</button>
    <button mat-menu-item>Item 2</button>
  </mat-menu>
</div>

// 2. Enhanced Threads effect với blur:
<div class="apply-threads-menu-enhanced">
  <button [matMenuTriggerFor]="menu">Open Menu</button>
  <mat-menu #menu="matMenu">
    <button mat-menu-item>Item 1</button>
    <button mat-menu-item>Item 2</button>
  </mat-menu>
</div>

// 3. Manual control:
<div class="threads-menu-backdrop-scale"
     [class.mat-menu-open]="menuOpen"
     [class.mat-menu-closed]="!menuOpen">
  <button [matMenuTriggerFor]="menu"
          (menuOpened)="menuOpen = true"
          (menuClosed)="menuOpen = false">
    Open Menu
  </button>
  <mat-menu #menu="matMenu" class="threads-menu-slide-up">
    <button mat-menu-item>Item 1</button>
    <button mat-menu-item>Item 2</button>
  </mat-menu>
</div>

// 4. Trong TypeScript component:
export class MyComponent {
  menuOpen = false;

  onMenuOpened() {
    this.menuOpen = true;
  }

  onMenuClosed() {
    this.menuOpen = false;
  }
}
*/
