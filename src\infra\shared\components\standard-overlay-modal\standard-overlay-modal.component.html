<!-- Standard Overlay Modal Wrapper Template -->
<div class="standard-overlay-modal-container">
  <!-- Header Section -->
  <div class="standard-overlay-modal-header" *ngIf="title()">
    <!-- Title Display -->
    <div class="standard-overlay-modal-title">
      <!-- Template Title -->
      <ng-container *ngIf="isTitleTemplate()">
        <ng-container *ngTemplateOutlet="$any(title())"></ng-container>
      </ng-container>

      <!-- String Title -->
      <h4 *ngIf="isTitleString()" class="mb-0">
        {{ $any(title()) | translate }}
      </h4>
    </div>

    <!-- Close Button -->
    <button
      *ngIf="enableClose()"
      type="button"
      class="btn btn-sm btn-icon btn-light-primary"
      (click)="onClose()"
      [attr.aria-label]="'COMMON.ACTIONS.CLOSE' | translate"
    >
      <i class="ki-duotone ki-cross fs-2">
        <span class="path1"></span>
        <span class="path2"></span>
      </i>
    </button>
  </div>

  <!-- Content Section -->
  <div class="standard-overlay-modal-content">
    <!-- Dynamic Component Container -->
    <div #dynamicComponentContainer></div>
  </div>

  <!-- Footer Section -->
  <div class="standard-overlay-modal-footer" *ngIf="useDefault() || customActionsTemplate()">
    <!-- Custom Actions Template -->
    <ng-container *ngIf="customActionsTemplate()">
      <ng-container *ngTemplateOutlet="customActionsTemplate()"></ng-container>
    </ng-container>

    <!-- Default Actions -->
    <div class="standard-overlay-modal-actions" *ngIf="useDefault()">
      <!-- Cancel Button -->
      <button
        type="button"
        class="btn btn-light me-3"
        (click)="onCancel()"
      >
        <i class="ki-duotone ki-cross fs-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        {{ 'COMMON.ACTIONS.CANCEL' | translate }}
      </button>

      <!-- Confirm Button -->
      <button
        type="button"
        class="btn btn-primary"
        (click)="onConfirm()"
        [disabled]="!isComponentValid()"
      >
        <i class="ki-duotone ki-check fs-2">
          <span class="path1"></span>
          <span class="path2"></span>
        </i>
        {{ 'COMMON.ACTIONS.CONFIRM' | translate }}
      </button>
    </div>
  </div>
</div>
