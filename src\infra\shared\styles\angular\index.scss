@use '../variable' as *;
@use '@angular/material' as mat;

@forward './bottom-sheet.scss';
@forward './responsive-modal.scss';
@forward './dialog.scss';
@forward './modal.scss';
@forward './expansion-panel.scss';
@forward './form.scss';
@forward './mat-drawer.scss';


html {
  --mat-expansion-container-background-color: #{$bodyBackgroundColor};
  --mat-sidenav-content-background-color: #{$bodyBackgroundColor};
  --mat-sidenav-container-background-color: #{$bodyBackgroundColor};
  --mat-bottom-sheet-container-background-color: #{$bodyBackgroundColor};
  --mat-expansion-container-box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, .03);
  --mdc-protected-button-container-shape:  0.5em;
  --mdc-text-button-container-shape: 0.5em;
  --mdc-outlined-text-field-input-text-color: #183153;
  --mat-expansion-header-text-color: #252f4a;
  --mdc-outlined-text-field-label-text-color: #183153;
  --mat-sys-on-surface-variant: #465975;
  --mat-expansion-header-hover-state-layer-color: #fbda5f;
  --mdc-outlined-text-field-outline-color: #DBDFE9;
  --mdc-outlined-text-field-hover-outline-color: #ccc;
  --mat-sidenav-container-shape: 0;
  --mat-menu-container-color: var(--tw-dropdown-background-color);
  --mat-menu-container-elevation-shadow: var(--tw-dropdown-box-shadow);
  --mat-bottom-sheet-container-text-color: var(--tw-gray-700);
  --mat-sidenav-content-text-color: $bodyTextColor;
  --mat-paginator-container-background-color: #fff;
  --mdc-elevated-card-container-color: #fff;

  // dialog
  --mdc-dialog-container-shape: 0;
  --mdc-dialog-container-color: #fff;

  //card
  --mdc-elevated-card-container-elevation: none;
  --mdc-elevated-card-container-shape: 0;
}

html {
  color-scheme: light dark;
  @include mat.theme((
    color: mat.$azure-palette,
    typography: $font,
    density: 0
  ));
}






.cdk-overlay-container {
  z-index: 99999;
}
