import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { EmbeddedWarehouseLocation, ProductListItem, ProductFilterModalData } from '@features/warehouse/inventory-check/models/api/inventory-check.dto';
import { ProductFilterResult } from '@features/warehouse/inventory-check/models/view/inventory-check.view-model';
import { mockCategoryList, mockWarehouseLocations } from '@mock/shared/list.mock';
import { mockProductList } from '@mock/product/product.mock';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Component modal lọc sản phẩm
 * Cho phép lọc sản phẩm theo nhóm hàng, vị trí kho và các tùy chọn khác
 */
@Component({
  selector: 'app-product-filter-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatBottomSheetModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    TranslateModule
  ],
  templateUrl: './product-filter-modal.component.html',
  styleUrls: ['./product-filter-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductFilterModalComponent implements OnInit, StrictModalComponent<ProductFilterModalData, ProductFilterResult> {
  /**
   * Danh sách nhóm hàng từ mock data
   */
  categories = mockCategoryList;

  /**
   * Danh sách vị trí kho đã lọc theo warehouseId
   */
  filteredLocations: any[] = [];

  /**
   * Danh sách nhóm hàng đã chọn
   */
  selectedCategories: string[] = [];

  /**
   * Vị trí kho đã chọn
   */
  selectedWarehouseLocation: EmbeddedWarehouseLocation | null = null;

  /**
   * Chỉ kiểm hàng còn tồn kho
   */
  onlyInStock: boolean = false;

  /**
   * Chỉ kiểm hàng đang kinh doanh
   */
  onlyActive: boolean = false;

  /**
   * ID kho đã chọn
   */
  warehouseId: string = '';

  /**
   * Dữ liệu từ dialog hoặc bottom sheet
   */
  data: ProductFilterModalData | null | undefined;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: ProductFilterModalData | null | undefined,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: ProductFilterModalData | null | undefined,
    @Optional() private dialogRef?: MatDialogRef<ProductFilterModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<ProductFilterModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData;

    // Khởi tạo giá trị từ dữ liệu đầu vào
    if (this.data) {
      this.warehouseId = this.data.warehouseId;
      this.selectedCategories = this.data.current.category || [];
      this.selectedWarehouseLocation = this.data.current.warehouseLocation;
    }
  }

  ngOnInit(): void {
    // Lọc danh sách vị trí kho theo warehouseId
    this.filteredLocations = mockWarehouseLocations.filter(
      location => location.warehouse._id === this.warehouseId
    );

    // Đảm bảo rằng selectedCategories không bị undefined
    if (!this.selectedCategories) {
      this.selectedCategories = [];
    }
  }

  /**
   * Xử lý khi nhấn nút Hủy
   * Đóng dialog mà không trả về kết quả
   */
  onCancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }

  /**
   * Xử lý khi nhấn nút Xác nhận
   * Lọc sản phẩm theo các tiêu chí và trả về kết quả
   */
  onConfirm(): void {
    const filteredProducts = this.filterProducts();
    const result: ProductFilterResult = {
      products: filteredProducts,
      filters: {
        categories: this.selectedCategories,
        warehouseLocation: this.selectedWarehouseLocation,
        onlyInStock: this.onlyInStock,
        onlyActive: this.onlyActive
      }
    };

    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  /**
   * Lọc sản phẩm theo các tiêu chí đã chọn
   * @returns Danh sách sản phẩm đã lọc
   */
  private filterProducts(): ProductListItem[] {
    // Nếu không có tiêu chí lọc nào được chọn, trả về tất cả sản phẩm
    if (this.selectedCategories.length === 0 && !this.selectedWarehouseLocation && !this.onlyInStock && !this.onlyActive) {
      // Giới hạn số lượng sản phẩm trả về để tránh quá tải
      return mockProductList.slice(0, 10);
    }

    const filteredProducts = mockProductList.filter(product => {
      // Lọc theo nhóm hàng
      if (this.selectedCategories.length > 0) {
        const hasCategory = product.categoryIds.some(categoryId =>
          this.selectedCategories.includes(categoryId)
        );
        if (!hasCategory) return false;
      }

      // Lọc theo vị trí kho
      if (this.selectedWarehouseLocation) {
        // Giả định sản phẩm có vị trí kho nếu warehouseIds chứa warehouseId của vị trí kho
        const hasLocation = product.warehouseIds.includes(this.warehouseId);
        if (!hasLocation) return false;
      }

      // Lọc theo tồn kho
      if (this.onlyInStock) {
        const inStock = product.warehouseStock && product.warehouseStock[this.warehouseId] > 0;
        if (!inStock) return false;
      }

      // Lọc theo trạng thái kinh doanh (giả định tất cả sản phẩm đều active)
      if (this.onlyActive) {
        // Trong mock data không có trường status, nên giả định tất cả đều active
        // Nếu có trường status, sẽ kiểm tra: product.status === 'active'
        // return product.status === 'active';
      }

      return true;
    });

    // Nếu không có sản phẩm nào thỏa mãn tiêu chí lọc, trả về một số sản phẩm mặc định
    if (filteredProducts.length === 0) {
      console.log('Không có sản phẩm nào thỏa mãn tiêu chí lọc, trả về sản phẩm mặc định');
      return mockProductList.slice(0, 5);
    }

    return filteredProducts;
  }

  /**
   * Kiểm tra xem một nhóm hàng có được chọn hay không
   * @param categoryId ID của nhóm hàng cần kiểm tra
   * @returns true nếu nhóm hàng đã được chọn, ngược lại false
   */
  isCategorySelected(categoryId: string): boolean {
    return this.selectedCategories.includes(categoryId);
  }

  /**
   * Xử lý khi chọn/bỏ chọn một nhóm hàng
   * @param categoryId ID của nhóm hàng
   * @param isChecked Trạng thái checkbox
   */
  onCategoryChange(categoryId: string, isChecked: boolean): void {
    if (isChecked) {
      // Thêm vào danh sách đã chọn nếu chưa có
      if (!this.selectedCategories.includes(categoryId)) {
        this.selectedCategories.push(categoryId);
      }
    } else {
      // Xóa khỏi danh sách đã chọn
      this.selectedCategories = this.selectedCategories.filter(id => id !== categoryId);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): ProductFilterResult {
    const filteredProducts = this.filterProducts();
    return {
      products: filteredProducts,
      filters: {
        categories: this.selectedCategories,
        warehouseLocation: this.selectedWarehouseLocation,
        onlyInStock: this.onlyInStock,
        onlyActive: this.onlyActive
      }
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // Product filter modal luôn valid vì có thể không chọn filter nào
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: ProductFilterModalData): void {
    this.data = data;
    this.warehouseId = data.warehouseId;
    this.selectedCategories = data.current.category || [];
    this.selectedWarehouseLocation = data.current.warehouseLocation;

    // Cập nhật filtered locations
    this.filteredLocations = mockWarehouseLocations.filter(loc =>
      loc.warehouse._id === this.warehouseId
    );
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào first checkbox khi modal mở
    setTimeout(() => {
      const firstCheckbox = document.querySelector('mat-checkbox input') as HTMLInputElement;
      if (firstCheckbox) {
        firstCheckbox.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
