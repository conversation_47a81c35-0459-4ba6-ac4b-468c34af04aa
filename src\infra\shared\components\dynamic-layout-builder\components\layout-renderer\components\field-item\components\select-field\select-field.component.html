<!-- Select Field Container -->
@if (shouldShowField()) {
  <div [class]="config.currentViewMode === 'form' ? 'field-row-form' : 'field-row-view'">

    <!-- Label Column -->
    <div class="field-label-column">
      <label class="field-label-text">
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>

      <!-- Read-only icon for form mode with read permission -->
      @if (isReadOnly()) {
        <mat-icon
          class="read-only-icon"
          [matTooltip]="getReadOnlyTooltip() | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Value Column -->
    <div class="field-value-column">

      <!-- PREVIEW MODE: Display placeholder data -->
      @if (config.currentViewMode === 'preview') {
        <div class="field-view-value" [class.empty]="!placeholderValue() && !placeholderValues()?.length">
          <!-- Single select display -->
          @if (!isMultiSelect()) {
            <span>{{ (placeholderValue() | translate) || ('DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate) }}</span>
          }

          <!-- Multi-select display with ordered list -->
          @else {
            @if (placeholderValues()?.length) {
              <div class="field-list-display">
                <ol>
                  @for (value of placeholderValues(); track value) {
                    <li>{{ value | translate }}</li>
                  }
                </ol>
              </div>
            } @else {
              <span class="empty">{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate }}</span>
            }
          }
        </div>
      }

      <!-- VIEW MODE: Display actual values hoặc inline edit -->
      @else if (config.currentViewMode === 'view') {
        @if (!isFieldInEditMode()) {
          <!-- Normal view mode -->
          <div class="field-view-value" [class.empty]="!hasCurrentValue()">
            <!-- Single select display -->
            @if (!isMultiSelect()) {
              @if (currentValue()) {
                <span>{{ formatDisplayValue(currentValue()) }}</span>
              } @else {
                <span class="empty">{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate }}</span>
              }
            }

            <!-- Multi-select display with ordered list -->
            @else {
              @if (getCurrentValueArray()?.length) {
                <div class="field-list-display">
                  <ol>
                    @for (value of getCurrentValueArray(); track value) {
                      <li>{{ formatDisplayValue(value) }}</li>
                    }
                  </ol>
                </div>
              } @else {
                <span class="empty">{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate }}</span>
              }
            }

            <!-- Edit Actions - chỉ hiển thị khi có quyền write -->
            @if (canEditField()) {
              <div class="field-edit-actions">
                <button type="button" class="btn btn-edit" (click)="startEditMode()">
                  <mat-icon>edit</mat-icon>
                  {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.EDIT' | translate }}
                </button>
              </div>
            }
          </div>
        } @else {
          <!-- Inline edit mode -->
          <div class="field-inline-edit">
            <div class="field-input-container">
              <mat-form-field appearance="outline" class="w-100">
                <mat-select
                  [placeholder]="(config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.SELECT') | translate"
                  [formControl]="formControl()"
                  [multiple]="isMultiSelect()">
                  @for (option of getSelectOptions(); track option.value) {
                    <mat-option [value]="option.value">
                      {{ option.label | translate }}
                    </mat-option>
                  }
                </mat-select>
              </mat-form-field>
            </div>

            <!-- Edit Actions -->
            <div class="field-edit-actions editing">
              <button type="button" class="btn btn-save" (click)="saveEditMode()">
                <mat-icon>save</mat-icon>
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.SAVE' | translate }}
              </button>
              <button type="button" class="btn btn-cancel" (click)="cancelEditMode()">
                <mat-icon>close</mat-icon>
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.CANCEL' | translate }}
              </button>
            </div>
          </div>
        }
      }

      <!-- FORM MODE: Display select control -->
      @else {
        <mat-form-field appearance="outline" class="w-100">
          <!-- Select element -->
          <mat-select
            [placeholder]="getPlaceholder() | translate"
            [formControl]="formControl()"
            [multiple]="isMultiSelect()"
            [disabled]="isReadOnly()"
            (selectionChange)="onSelectionChange($event)">

            <!-- Options -->
            @for (option of fieldOptions(); track option.value) {
              <mat-option [value]="option.value">
                {{ option.label | translate }}
              </mat-option>
            }
          </mat-select>

        </mat-form-field>

        <!-- Selected chips display for multi-select in form mode -->
        @if (isMultiSelect() && formControl().value && formControl().value.length > 0) {
          <div class="selected-chips-container">
            @for (selectedValue of formControl().value; track selectedValue) {
              <mat-chip
                class="selected-chip"
                [removable]="!isReadOnly()"
                (removed)="removeChip(selectedValue)">
                {{ getOptionLabel(selectedValue) | translate }}
                @if (!isReadOnly()) {
                  <mat-icon matChipRemove>cancel</mat-icon>
                }
              </mat-chip>
            }
          </div>
        }
      }

      <!-- Field-Level Validation Errors -->
      @if (shouldShowValidationErrors()) {
        <div class="field-validation-errors" aria-live="polite">
          @for (error of getValidationErrors(); track error) {
            <div class="validation-error-item">
              <mat-icon class="error-icon">error</mat-icon>
              <span class="error-message">{{ error | translate }}</span>
            </div>
          }
        </div>
      }

      <!-- Field Tooltip/Description -->
      @if (config.field.tooltip) {
        <div class="field-tooltip">
          <small class="text-muted">{{ config.field.tooltip }}</small>
        </div>
      }

      <!-- Field Hints -->
      @if (config.currentViewMode === 'form' && isMultiSelect()) {
        <div class="field-hints">
          <small class="text-muted">
            {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_HINTS.MULTI_SELECT' | translate }}
          </small>
        </div>
      }
    </div>
  </div>
}
