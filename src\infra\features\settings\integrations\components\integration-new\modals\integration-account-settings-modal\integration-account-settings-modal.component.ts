import { Component, signal, computed, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';

import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';
import { SettingsListComponent, SettingChangeEvent, SettingsSaveEvent } from '@shared/components/settings/settings-list.component';
import { FlashMessageService } from '@core/services/flash_message.service';
import { 
  IntegrationAccountSettingsModalData, 
  IntegrationAccountSettingsModalResult 
} from './integration-account-settings-modal.interfaces';

/**
 * Modal component để quản lý cài đặt tài khoản tích hợp
 * Chỉ chứa phần Settings từ modal gốc
 */
@Component({
  selector: 'app-integration-account-settings-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatDividerModule,
    TranslateModule,
    SettingsListComponent
  ],
  templateUrl: './integration-account-settings-modal.component.html',
  styleUrls: ['./integration-account-settings-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IntegrationAccountSettingsModalComponent implements StrictModalComponent<IntegrationAccountSettingsModalData, IntegrationAccountSettingsModalResult> {
  
  // Dữ liệu đầu vào từ modal service
  data = signal<IntegrationAccountSettingsModalData | null>(null);
  
  // Trạng thái thay đổi settings
  settingsChanged = signal<boolean>(false);
  changedSettings = signal<Record<string, any>>({});
  
  // Computed properties
  platformName = computed(() => this.data()?.platformName || '');
  accountName = computed(() => this.data()?.accountName || '');
  config = computed(() => this.data()?.config);
  
  // Computed cho title modal
  modalTitle = computed(() => {
    const platform = this.platformName();
    const account = this.accountName();
    if (account) {
      return `${platform} - ${account}`;
    }
    return platform;
  });
  
  constructor(
    private flashMessageService: FlashMessageService
  ) {}
  
  /**
   * Xử lý thay đổi setting (khi autoSave = true)
   * @param event Sự kiện thay đổi setting
   */
  onSettingChange(event: SettingChangeEvent): void {
    // Cập nhật trạng thái thay đổi
    this.settingsChanged.set(true);
    
    // Cập nhật changed settings
    const currentChanged = this.changedSettings();
    this.changedSettings.set({
      ...currentChanged,
      [event.settingId]: event.value
    });
    
    console.log('Setting changed:', event);
  }
  
  /**
   * Xử lý lưu settings (khi autoSave = false)
   * @param event Sự kiện lưu settings
   */
  onSettingsSave(event: SettingsSaveEvent): void {
    // Cập nhật trạng thái thay đổi
    this.settingsChanged.set(true);
    this.changedSettings.set(event.changedSettings);
    
    // Hiển thị thông báo thành công
    this.flashMessageService.success('SETTINGS_SAVED_SUCCESSFULLY');
    
    console.log('Settings saved:', event);
  }
  
  /**
   * Xử lý hủy thay đổi settings
   */
  onSettingsCancel(): void {
    // Reset trạng thái thay đổi
    this.settingsChanged.set(false);
    this.changedSettings.set({});
    
    console.log('Settings changes cancelled');
  }
  
  // ===== StrictModalComponent Interface Implementation =====
  
  /**
   * Cập nhật dữ liệu cho modal
   */
  updateData(data: IntegrationAccountSettingsModalData): void {
    this.data.set(data);
  }
  
  /**
   * Trả về kết quả từ modal component
   */
  getModalResult(): IntegrationAccountSettingsModalResult {
    return {
      settingsChanged: this.settingsChanged(),
      changedSettings: this.settingsChanged() ? this.changedSettings() : undefined,
      action: this.settingsChanged() ? 'save' : 'close'
    };
  }
  
  /**
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return true; // Modal này luôn valid
  }
  
  /**
   * Method được gọi khi modal được mở
   */
  onModalOpen?(): void {
    console.log('Integration Account Settings Modal opened');
  }
  
  /**
   * Method được gọi trước khi modal đóng
   */
  onModalClose?(): boolean | Promise<boolean> {
    console.log('Integration Account Settings Modal closing');
    return true;
  }
}
