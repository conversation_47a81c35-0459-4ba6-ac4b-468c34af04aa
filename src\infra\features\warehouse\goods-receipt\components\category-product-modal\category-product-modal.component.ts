import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule, MatSelectionListChange } from '@angular/material/list';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { mockCategoryList } from '@mock/shared/list.mock';
import { mockProductList } from '@mock/product/product.mock';
import { CategoryList } from 'salehub_shared_contracts/requests/shared/list';
import { ProductListItem } from '../../models/api/goods-receipt.dto'
import { CategoryProductModalData } from '../../models/view/goods-receipt.view-model';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Modal cho phép người dùng chọn các nhóm hàng và lấy ra các sản phẩm thuộc nhóm đó
 */
@Component({
  selector: 'app-category-product-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatListModule,
    TranslateModule
  ],
  templateUrl: './category-product-modal.component.html',
  styleUrls: ['./category-product-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CategoryProductModalComponent implements StrictModalComponent<CategoryProductModalData, ProductListItem[]> {
  // Danh sách nhóm hàng từ mock data
  categoryList: CategoryList = mockCategoryList;

  // Lưu trữ ID của các nhóm hàng được chọn sử dụng signal
  selectedCategoryIds = signal<string[]>([]);

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: CategoryProductModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: CategoryProductModalData,
    @Optional() private dialogRef?: MatDialogRef<CategoryProductModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<CategoryProductModalComponent>
  ) {
    // Sử dụng dữ liệu từ dialogData hoặc bottomSheetData nếu có
    const inputData = this.dialogData || this.bottomSheetData;

    // Khởi tạo selectedCategoryIds nếu có dữ liệu sẵn
    if (inputData?.categoryIds && inputData.categoryIds.length > 0) {
      this.selectedCategoryIds.set(inputData.categoryIds);
    }
  }

  /**
   * Xử lý sự kiện khi người dùng thay đổi lựa chọn trong danh sách
   * @param event Sự kiện thay đổi từ mat-selection-list
   */
  onSelectionChange(event: MatSelectionListChange): void {
    // Lấy tất cả các tùy chọn đã chọn
    const selectedOptions = event.source.selectedOptions.selected;

    // Lấy các giá trị (category ID) từ các tùy chọn đã chọn
    const selectedIds = selectedOptions.map(option => option.value);

    // Cập nhật signal với các ID đã chọn
    this.selectedCategoryIds.set(selectedIds);
  }

  /**
   * Đóng modal và trả về kết quả null
   */
  onCancel(): void {
    this.close(null);
  }

  /**
   * Lọc sản phẩm theo nhóm hàng đã chọn và đóng modal
   */
  onAdd(): void {
    const selectedIds = this.selectedCategoryIds();

    if (selectedIds.length === 0) {
      // Không có nhóm hàng nào được chọn
      this.close(null);
      return;
    }

    // Lấy danh sách productIds để loại trừ (nếu có)
    const excludeProductIds = this.dialogData?.excludeProductIds ||
                             this.bottomSheetData?.excludeProductIds ||
                             [];

    // Lọc sản phẩm từ mockProductList dựa trên categoryIds
    const selectedProducts = mockProductList.filter(product => {
      // Loại bỏ sản phẩm nếu nó có trong danh sách loại trừ
      if (excludeProductIds.includes(product.productId)) {
        return false;
      }

      // Kiểm tra xem sản phẩm có thuộc ít nhất một nhóm hàng đã chọn không
      if (!product.categoryIds || product.categoryIds.length === 0) {
        return false;
      }

      // Trả về true nếu có ít nhất một categoryId của sản phẩm nằm trong danh sách selectedCategoryIds
      return product.categoryIds.some(categoryId => selectedIds.includes(categoryId));
    });

    // Đóng modal và trả về danh sách sản phẩm đã lọc
    this.close(selectedProducts);
  }

  /**
   * Phương thức đóng modal, hỗ trợ cả dialog và bottom sheet
   */
  private close(result: ProductListItem[] | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): ProductListItem[] {
    const selectedIds = this.selectedCategoryIds();

    if (selectedIds.length === 0) {
      return [];
    }

    // Lấy danh sách productIds để loại trừ (nếu có)
    const excludeProductIds = this.dialogData?.excludeProductIds ||
                             this.bottomSheetData?.excludeProductIds ||
                             [];

    // Lọc sản phẩm từ mockProductList dựa trên categoryIds
    const selectedProducts = mockProductList.filter(product => {
      // Loại bỏ sản phẩm nếu nó có trong danh sách loại trừ
      if (excludeProductIds.includes(product.productId)) {
        return false;
      }

      // Kiểm tra xem sản phẩm có thuộc ít nhất một nhóm hàng đã chọn không
      if (!product.categoryIds || product.categoryIds.length === 0) {
        return false;
      }

      // Trả về true nếu có ít nhất một categoryId của sản phẩm nằm trong danh sách selectedCategoryIds
      return product.categoryIds.some(categoryId => selectedIds.includes(categoryId));
    });

    return selectedProducts;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // Category product modal luôn valid vì có thể không chọn category nào
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: CategoryProductModalData): void {
    // Khởi tạo selectedCategoryIds nếu có dữ liệu sẵn
    if (data?.categoryIds && data.categoryIds.length > 0) {
      this.selectedCategoryIds.set(data.categoryIds);
    } else {
      this.selectedCategoryIds.set([]);
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào first list item khi modal mở
    setTimeout(() => {
      const firstListItem = document.querySelector('mat-list-option') as HTMLElement;
      if (firstListItem) {
        firstListItem.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
