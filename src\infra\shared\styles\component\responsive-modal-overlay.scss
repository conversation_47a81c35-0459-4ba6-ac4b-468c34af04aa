// ResponsiveModalService Overlay Styles
// CSS cho overlay positioning và responsive behavior

// CDK Overlay Panel Styles
.responsive-overlay-panel {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.24);
  padding: 24px;
  max-height: calc(100vh - 32px);
  overflow: auto;

  // Animation cho overlay
  animation: overlayFadeIn 0.2s ease-out;

  @media (max-width: 575px) {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }
}

// Transparent backdrop cho CDK Overlay
.cdk-overlay-transparent-backdrop {
  background-color: rgba(0, 0, 0, 0.1);

  &.cdk-overlay-backdrop-showing {
    opacity: 1;
  }
}

// Legacy Overlay Dialog Styles (for backward compatibility)
.responsive-dialog-overlay {
  // Đ<PERSON>m bảo dialog không có transform mặc định để positioning chính xác
  .mat-mdc-dialog-container {
    transform: none !important;
    
    // Responsive width cho overlay
    min-width: 300px;
    max-width: 400px;
    width: auto;
    
    // Shadow và border radius cho overlay
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.24);
    border-radius: 12px;
    
    // Animation cho overlay
    animation: overlayFadeIn 0.2s ease-out;
    
    @media (max-width: 575px) {
      min-width: 280px;
      max-width: calc(100vw - 32px);
      margin: 16px;
    }
  }
  
  // Backdrop cho overlay (nhẹ hơn modal thông thường)
  .cdk-overlay-backdrop {
    background-color: rgba(0, 0, 0, 0.1);
    
    &.cdk-overlay-backdrop-showing {
      opacity: 1;
    }
  }
}

// Bottom Sheet Overlay Styles (cho mobile)
.responsive-bottom-sheet-overlay {
  // Giữ nguyên styles của bottom sheet
  .mat-mdc-bottom-sheet-container {
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 32px rgba(0, 0, 0, 0.24);
    
    // Animation cho bottom sheet
    animation: bottomSheetSlideUp 0.3s ease-out;
  }
}

// Animations
@keyframes overlayFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bottomSheetSlideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// Positioning utilities cho overlay
.overlay-position-top-left {
  .mat-mdc-dialog-container {
    transform-origin: top left;
  }
}

.overlay-position-top-right {
  .mat-mdc-dialog-container {
    transform-origin: top right;
  }
}

.overlay-position-bottom-left {
  .mat-mdc-dialog-container {
    transform-origin: bottom left;
  }
}

.overlay-position-bottom-right {
  .mat-mdc-dialog-container {
    transform-origin: bottom right;
  }
}

.overlay-position-center {
  .mat-mdc-dialog-container {
    transform-origin: center;
  }
}

// Responsive behavior
@media (max-width: 767px) {
  // Trên mobile, overlay dialog sẽ có behavior giống modal thông thường
  .responsive-dialog-overlay {
    .mat-mdc-dialog-container {
      position: fixed !important;
      top: 50% !important;
      left: 50% !important;
      transform: translate(-50%, -50%) !important;
      max-width: calc(100vw - 32px);
      max-height: calc(100vh - 64px);
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .responsive-overlay-panel {
    background: #2d2d2d;
    color: white;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.48);
  }

  .cdk-overlay-transparent-backdrop {
    background-color: rgba(0, 0, 0, 0.2);
  }

  .responsive-dialog-overlay {
    .mat-mdc-dialog-container {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.48);
    }

    .cdk-overlay-backdrop {
      background-color: rgba(0, 0, 0, 0.2);
    }
  }

  .responsive-bottom-sheet-overlay {
    .mat-mdc-bottom-sheet-container {
      box-shadow: 0 -4px 32px rgba(0, 0, 0, 0.48);
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .responsive-overlay-panel {
    border: 2px solid;
  }

  .responsive-dialog-overlay {
    .mat-mdc-dialog-container {
      border: 2px solid;
    }
  }

  .responsive-bottom-sheet-overlay {
    .mat-mdc-bottom-sheet-container {
      border-top: 2px solid;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .responsive-overlay-panel,
  .responsive-dialog-overlay .mat-mdc-dialog-container,
  .responsive-bottom-sheet-overlay .mat-mdc-bottom-sheet-container {
    animation: none;
  }
  
  @keyframes overlayFadeIn {
    from, to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes bottomSheetSlideUp {
    from, to {
      transform: translateY(0);
    }
  }
}

// Animations for CDK Overlay
@keyframes overlayFadeIn {
  from {
    transform: scale(0.9) translateY(-10px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes overlaySlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
