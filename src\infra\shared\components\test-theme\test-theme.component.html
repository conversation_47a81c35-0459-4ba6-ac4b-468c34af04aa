<div class="container mt-5">

  <!-- Test Dynamic Layout Renderer -->
  <div class="row mb-4" *ngIf="testFeatures['dynamicLayoutRenderer']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🎨 Test Dynamic Layout Renderer</h3>
          <p class="text-muted mb-0">Test component render giao diện dựa trên cấu hình layout với 2 chế độ view/form và permission profiles</p>
        </div>
        <div class="card-body">
          <app-dynamic-layout-renderer
            [config]="testLayoutRendererConfig">
          </app-dynamic-layout-renderer>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Test Settings List Component -->
  <div class="row mb-4" *ngIf="testFeatures['settingsListComponent']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>⚙️ Test Settings List Component</h3>
          <p class="text-muted mb-0">Test component hiển thị danh sách cài đặt với toggle, select, và radio</p>
        </div>
        <div class="card-body">
          <app-settings-list
            [config]="settingsConfig"
            (settingChange)="onSettingChange($event)"
            (save)="onSettingsSave($event)"
            (cancel)="onSettingsCancel()"
            (autoSaveToggle)="onAutoSaveToggle($event)">
          </app-settings-list>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Dynamic Layout Builder với Field Usage Tracking -->
  <div class="row mb-4" *ngIf="testFeatures['dynamicLayoutBuilder']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🧪 Test Dynamic Layout Builder - Field Usage Tracking</h3>
          <p class="text-muted mb-0">Test field usage tracking và drag-drop prevention logic</p>
        </div>
        <div class="card-body">
          <app-dynamic-layout-builder
            [layoutBuilderConfig]="testLayoutBuilderConfig">
          </app-dynamic-layout-builder>
        </div>
      </div>
    </div>
  </div>


  <div class="row" *ngIf="testFeatures['productModifiersBottomSheet'] || testFeatures['batchDialog']">
    <div class="col-md-6" *ngIf="testFeatures['productModifiersBottomSheet']">
      <div class="card">
        <div class="card-header">
          <h4>Test Product Modifiers Bottom Sheet</h4>
        </div>
        <div class="card-body">
          <button class="btn btn-primary" (click)="openProductModifiersSheet()">
            Mở bottom sheet chọn thêm sản phẩm
          </button>
        </div>
      </div>
    </div>
    <div class="col-md-6" *ngIf="testFeatures['batchDialog']">
      <div class="card">
        <div class="card-header">
          <h4>Test Batch Dialog</h4>
        </div>
        <div class="card-body">
          <button class="btn btn-success" (click)="openBatchDialog()">
            Mở dialog lô hàng
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-3" *ngIf="testFeatures['inputPlace']">
    <div class="col-md-6">
      <input-place></input-place>
    </div>
  </div>

  <!-- <app-product-selection
    [list]="productSelectorConfig.list"
    [data]="productSelectorConfig.data"
    [warehouseList]="productSelectorConfig.warehouseList"
    [categoryList]="productSelectorConfig.categoryList"
    [brandList]="productSelectorConfig.brandList"
    (openProductForm)="openProductForm()"
  ></app-product-selection> -->

  <!-- Thêm button để test dialog vào vị trí thích hợp -->
  <div class="content-section" *ngIf="testFeatures['testDialogs']">
    <h3>Test Dialogs</h3>
    <div class="button-row">
      <button mat-raised-button color="primary" (click)="openCategoryProductDialog()">
        Test Category Product Dialog
      </button>

      <!-- Các button test khác đã có sẵn -->
    </div>
  </div>

  <div *ngIf="testFeatures['additionalCostDialog']">
    <h2>Test AdditionalCostDialog</h2>
    <div class="button-row">
      <button mat-raised-button color="primary" (click)="openAdditionalCostDialog(false)">
        Thêm chi phí mới
      </button>
      <button mat-raised-button color="accent" (click)="openAdditionalCostDialog(true)">
        Sửa chi phí
      </button>
    </div>
  </div>

  <div *ngIf="testFeatures['taxDialog']">
    <h2>Test TaxDialog</h2>
    <div class="button-row">
      <button mat-raised-button color="primary" (click)="openTaxDialog(false)">
        Thêm thuế mới
      </button>
      <button mat-raised-button color="accent" (click)="openTaxDialog(true)">
        Sửa thuế
      </button>
    </div>
  </div>

  <!-- Test SelectAdditionalCostsDialog -->
  <div class="col-md-6 mb-10" *ngIf="testFeatures['selectAdditionalCostsDialog']">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Select Additional Costs Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog chọn và tùy chỉnh chi phí phát sinh khi nhập kho</p>
        <button class="btn btn-primary" (click)="openSelectAdditionalCostsDialog()">
          Mở Dialog Chọn Chi Phí
        </button>
      </div>
    </div>
  </div>

  <!-- DISABLE ALL SECTIONS BELOW - Only show ResponsiveModalService.openWithOverlay -->
  <div *ngIf="false">

  <!-- Test QualityCheckRejectDialog -->
  <div class="col-md-6 mb-10" *ngIf="testFeatures['qualityCheckRejectDialog']">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Quality Check Reject Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog tạo hoặc chỉnh sửa sản phẩm bị từ chối trong kiểm tra chất lượng</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openQualityCheckRejectDialog(false)">
            Thêm sản phẩm bị từ chối
          </button>
          <button class="btn btn-warning" (click)="openQualityCheckRejectDialog(true)">
            Sửa sản phẩm bị từ chối
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test SimpleNoteDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Simple Note Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog thêm hoặc chỉnh sửa ghi chú đơn giản</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openSimpleNoteDialog()">
            Thêm ghi chú mới
          </button>
          <button class="btn btn-warning" (click)="openSimpleNoteDialog('Ghi chú hiện tại cần chỉnh sửa')">
            Sửa ghi chú
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test NoteModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Note Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thêm hoặc chỉnh sửa ghi chú với loại ghi chú</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openNoteModal()">
            Mở modal ghi chú
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test PromotionModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Promotion Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thêm hoặc chỉnh sửa khuyến mãi</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openPromotionModal()">
            Mở modal khuyến mãi
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test VariantFormModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Variant Form Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal tạo hoặc chỉnh sửa thuộc tính sản phẩm</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openVariantFormModal()">
            Mở modal thuộc tính sản phẩm
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test ProductFilterDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Product Filter Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog lọc sản phẩm theo nhóm hàng và vị trí kho</p>
        <button class="btn btn-primary" (click)="openProductFilterDialog()">
          Mở dialog lọc sản phẩm
        </button>
      </div>
    </div>
  </div>

  <!-- Test SerialNumberDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Serial Number Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog quản lý số serial của sản phẩm</p>
        <button class="btn btn-primary" (click)="openSerialNumberDialog()">
          Mở dialog quản lý serial
        </button>
      </div>
    </div>
  </div>

  <!-- Test ConfirmModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Confirm Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal xác nhận hành động</p>
        <button class="btn btn-primary" (click)="openConfirmDialog()">
          Mở modal xác nhận
        </button>
      </div>
    </div>
  </div>

  <!-- Test FieldPermissionModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Field Permission Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thiết lập quyền truy cập field với table responsive và radio buttons</p>
        <button class="btn btn-success" (click)="openFieldPermissionModal()">
          Mở modal thiết lập quyền field
        </button>
      </div>
    </div>
  </div>

  <!-- Test FieldPropertiesModal -->
  <div class="col-md-12 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Field Properties Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal chỉnh sửa thuộc tính field với các loại field khác nhau</p>
        <div class="row">
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('text')">
              Text Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('picklist')">
              Picklist Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('multi-picklist')">
              Multi-Picklist Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('textarea')">
              Textarea Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('number')">
              Number Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('currency')">
              Currency Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('decimal')">
              Decimal Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('search')">
              Search Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('user')">
              User Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('upload-file')">
              Upload File Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('upload-image')">
              Upload Image Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('checkbox')">
              Checkbox Field
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test StandardDialog -->
  <div class="col-md-12 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Standard Dialog Component</h3>
      </div>
      <div class="card-body">
        <p>Test StandardDialogComponent với các cấu hình khác nhau</p>
        <div class="row">
          <div class="col-md-4 mb-2">
            <button class="btn btn-primary w-100" (click)="openBasicStandardDialog()">
              Basic Dialog
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-success w-100" (click)="openConfirmStandardDialog()">
              Confirm Dialog
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-info w-100" (click)="openAlertStandardDialog()">
              Alert Dialog
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-warning w-100" (click)="openCustomTitleDialog()">
              Custom Title
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-secondary w-100" (click)="openCustomActionsDialog()">
              Custom Actions
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-danger w-100" (click)="openNoCloseDialog()">
              No Close Dialog
            </button>
          </div>
          <div class="col-md-12 mb-2">
            <button class="btn btn-outline-primary w-100" (click)="openResponsiveDialog()">
              Responsive Dialog (800px width)
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test StandardBottomSheet -->
  <div class="col-md-12 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Standard Bottom Sheet Service</h3>
      </div>
      <div class="card-body">
        <p>Test StandardBottomSheetService với các cấu hình khác nhau (Drop-in replacement cho MatBottomSheet)</p>
        <div class="row">
          <div class="col-md-4 mb-2">
            <button class="btn btn-primary w-100" (click)="openBasicStandardBottomSheet()">
              Basic Bottom Sheet
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-warning w-100" (click)="openCustomTitleBottomSheet()">
              Custom Title
            </button>
          </div>
          <div class="col-md-4 mb-2">
            <button class="btn btn-secondary w-100" (click)="openCustomActionsBottomSheet()">
              Custom Actions
            </button>
          </div>
          <div class="col-md-6 mb-2">
            <button class="btn btn-danger w-100" (click)="openNoCloseBottomSheet()">
              No Close Bottom Sheet
            </button>
          </div>
          <div class="col-md-6 mb-2">
            <button class="btn btn-outline-info w-100" (click)="openNoBackdropBottomSheet()">
              No Backdrop Bottom Sheet
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- CreateLayoutModal Tests -->
  <div class="card mb-4">
    <div class="card-header">
      <h3>CreateLayoutModal</h3>
    </div>
    <div class="card-body">
      <p class="text-muted">
        Test CreateLayoutModal component với ResponsiveModalService. Modal để tạo layout mới trong Dynamic Layout Builder.
      </p>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="primary"
          (click)="openCreateLayoutModal()"
          class="btn btn-primary">
          <mat-icon>add_circle</mat-icon>
          Tạo Layout (Có Validation)
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openCreateLayoutModalSimple()"
          class="btn btn-secondary">
          <mat-icon>add</mat-icon>
          Tạo Layout (Đơn giản)
        </button>
      </div>
    </div>
  </div>

  <!-- ResponsiveModalService Tests -->
  <div class="card mb-4">
    <div class="card-header">
      <h3>ResponsiveModalService</h3>
    </div>
    <div class="card-body">
      <p class="text-muted">
        Test ResponsiveModalService với auto-selection giữa StandardDialog (desktop) và StandardBottomSheet (mobile).
        Service này sử dụng StandardDialogService và StandardBottomSheetService với enhanced features.
      </p>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="primary"
          (click)="openResponsiveModal()"
          class="btn btn-primary">
          Auto-Selection Modal
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openForceDialogModal()"
          class="btn btn-success">
          Force Dialog Mode
        </button>
        <button
          mat-raised-button
          color="warn"
          (click)="openForceBottomSheetModal()"
          class="btn btn-warning">
          Force Bottom Sheet Mode
        </button>
        <button
          mat-raised-button
          color="warn"
          (click)="openListColumnSelectorBottomSheet()"
          class="btn btn-danger">
          🐛 Test ListColumnSelector Bottom Sheet
        </button>
      </div>

      <!-- Validation Test Section -->
      <h5 class="mt-4 mb-3">🧪 Validation Test (AdvancedModalComponent Interface)</h5>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="accent"
          (click)="openValidationTestModal()"
          class="btn btn-info">
          🧪 Test Validation Modal
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openValidationTestBottomSheet()"
          class="btn btn-info">
          🧪 Test Validation Bottom Sheet
        </button>
      </div>

      <!-- Type Safety Test Section -->
      <h5 class="mt-4 mb-3">🔒 Type Safety Test (StrictModalComponent Interface)</h5>
      <div class="d-flex gap-2 flex-wrap">
        <button
          mat-raised-button
          color="primary"
          (click)="openStrictTestModal()"
          class="btn btn-success">
          🔒 Test Strict Type-Safe Modal
        </button>
        <button
          mat-raised-button
          color="accent"
          (click)="openIntegrationAccountViewModal()"
          class="btn btn-info">
          🔗 Test Integration Account View Modal
        </button>
        <button
          mat-raised-button
          color="primary"
          (click)="openIntegrationAccountSettingsModal()"
          class="btn btn-primary">
          ⚙️ Test Integration Account Settings Modal
        </button>
        <button
          mat-raised-button
          color="warn"
          (click)="openIntegrationAccountLogsModal()"
          class="btn btn-warning">
          📋 Test Integration Account Logs Modal
        </button>
      </div>
      <p class="text-muted mt-2">
        <small>
          <strong>StrictTestModalComponent</strong> implement đầy đủ StrictModalComponent interface với type safety đầy đủ.
          Bao gồm: getModalResult(), isValid(), updateData(), onModalOpen(), onModalClose()
        </small>
      </p>
    </div>
  </div>

  <!-- DynamicLayoutRendererModal Tests -->
  <div class="card mb-4">
    <div class="card-header">
      <h3>🎨 DynamicLayoutRendererModal</h3>
    </div>
    <div class="card-body">
      <p class="text-muted">
        Test DynamicLayoutRendererModal component - modal wrapper cho DynamicLayoutRenderer với đầy đủ chức năng View và Form Edit Mode.
      </p>

      <!-- View Mode Test -->
      <h5 class="mt-4 mb-3">👁️ View Mode Test (Read-only)</h5>
      <div class="d-flex gap-2 flex-wrap mb-3">
        <button
          mat-raised-button
          color="primary"
          (click)="openDynamicLayoutRendererViewModal()"
          class="btn btn-primary">
          <mat-icon>visibility</mat-icon>
          Mở View Mode Modal
        </button>
      </div>
      <p class="text-muted small">
        Modal chỉ hiển thị dữ liệu, không cho phép chỉnh sửa. Chỉ có nút Close.
      </p>

      <!-- Edit Mode Test -->
      <h5 class="mt-4 mb-3">✏️ Form Edit Mode Test</h5>
      <div class="d-flex gap-2 flex-wrap mb-3">
        <button
          mat-raised-button
          color="accent"
          (click)="openDynamicLayoutRendererEditModal()"
          class="btn btn-success">
          <mat-icon>edit</mat-icon>
          Mở Edit Mode Modal
        </button>
      </div>
      <p class="text-muted small">
        Modal cho phép chỉnh sửa form với validation, save callback, và tracking thay đổi. Có nút Save và Cancel.
      </p>

      <!-- Custom Configuration Test -->
      <h5 class="mt-4 mb-3">⚙️ Custom Configuration Test</h5>
      <div class="d-flex gap-2 flex-wrap mb-3">
        <button
          mat-raised-button
          color="warn"
          (click)="openDynamicLayoutRendererCustomModal()"
          class="btn btn-warning">
          <mat-icon>settings</mat-icon>
          Mở Custom Modal
        </button>
      </div>
      <p class="text-muted small">
        Modal với cấu hình tùy chỉnh: quyền editor, force dialog mode, custom size, và validation error simulation.
      </p>

      <!-- Features List -->
      <div class="mt-4">
        <h6>✨ Tính năng được test:</h6>
        <ul class="text-muted small">
          <li><strong>View Mode:</strong> Hiển thị read-only với permission profiles</li>
          <li><strong>Edit Mode:</strong> Form editing với validation và save callback</li>
          <li><strong>Responsive:</strong> Auto-selection giữa dialog và bottom sheet</li>
          <li><strong>Type Safety:</strong> StrictModalComponent interface với type-safe data/result</li>
          <li><strong>i18n:</strong> Đa ngôn ngữ với translation keys</li>
          <li><strong>State Management:</strong> Tracking form changes và loading states</li>
          <li><strong>Error Handling:</strong> Xử lý lỗi save và validation</li>
          <li><strong>Accessibility:</strong> ARIA labels và keyboard navigation</li>
        </ul>
      </div>
    </div>
  </div>

  </div> <!-- END DISABLE ALL SECTIONS -->

  <!-- Test ResponsiveModalService.openWithOverlay -->
  <div class="row mb-4" *ngIf="testFeatures['multiplePositionTest'] || testFeatures['positioningOptionsTest']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🎯 Test ResponsiveModalService.openWithOverlay</h3>
          <p class="text-muted mb-0">Test modal với overlay positioning trên desktop và bottom sheet trên mobile</p>
        </div>
        <div class="card-body">

          <!-- Auto Mode Test - DISABLED -->
          <div *ngIf="false">
            <h5 class="mt-3 mb-3">🔄 Auto Mode Test (Responsive)</h5>
            <div class="d-flex gap-2 flex-wrap mb-3">
              <button
                mat-raised-button
                color="primary"
                (click)="testOpenWithOverlay($event)"
                class="btn btn-primary">
                <mat-icon>open_in_new</mat-icon>
                Test Overlay Modal (Auto)
              </button>
            </div>
            <p class="text-muted small">
              Trên desktop: Modal hiển thị gần vị trí button được click. Trên mobile: Hiển thị bottom sheet.
            </p>
          </div>

          <!-- Force Dialog Mode Test - DISABLED -->
          <div *ngIf="false">
            <h5 class="mt-4 mb-3">💻 Force Dialog Mode Test</h5>
            <div class="d-flex gap-2 flex-wrap mb-3">
              <button
                mat-raised-button
                color="accent"
                (click)="testOpenWithOverlayForceMode($event, 'dialog')"
                class="btn btn-success">
                <mat-icon>desktop_windows</mat-icon>
                Force Dialog Mode
              </button>
            </div>
            <p class="text-muted small">
              Force hiển thị dialog với overlay positioning bất kể viewport size.
            </p>
          </div>

          <!-- Force Bottom Sheet Mode Test - DISABLED -->
          <div *ngIf="false">
            <h5 class="mt-4 mb-3">📱 Force Bottom Sheet Mode Test</h5>
            <div class="d-flex gap-2 flex-wrap mb-3">
              <button
                mat-raised-button
                color="warn"
                (click)="testOpenWithOverlayForceMode($event, 'bottom-sheet')"
                class="btn btn-warning">
                <mat-icon>phone_android</mat-icon>
                Force Bottom Sheet Mode
              </button>
            </div>
            <p class="text-muted small">
              Force hiển thị bottom sheet bất kể viewport size.
            </p>
          </div>

          <!-- Multiple Trigger Test -->
          <div *ngIf="testFeatures['multiplePositionTest']">
            <h5 class="mt-4 mb-3">🎯 Multiple Trigger Position Test</h5>
            <div class="row">
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOpenWithOverlay($event)"
                  class="w-100">
                  Top Left
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOpenWithOverlay($event)"
                  class="w-100">
                  Top Right
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOpenWithOverlay($event)"
                  class="w-100">
                  Bottom Left
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOpenWithOverlay($event)"
                  class="w-100">
                  Bottom Right
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test overlay positioning từ các vị trí khác nhau trên màn hình để kiểm tra logic tính toán vị trí.
            </p>
          </div>

          <!-- Positioning Options Test -->
          <div *ngIf="testFeatures['positioningOptionsTest']">
            <h5 class="mt-4 mb-3">📍 Positioning Options Test (9 Alignment Options)</h5>
            <div class="row">
              <!-- Top Row -->
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="accent"
                  (click)="testOverlayPositioning($event, 'top-left')"
                  class="w-100">
                  🔺 Top Left
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="accent"
                  (click)="testOverlayPositioning($event, 'top-center')"
                  class="w-100">
                  🔺 Top Center
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="accent"
                  (click)="testOverlayPositioning($event, 'top-right')"
                  class="w-100">
                  🔺 Top Right
                </button>
              </div>

              <!-- Middle Row -->
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="warn"
                  (click)="testOverlayPositioning($event, 'left')"
                  class="w-100">
                  ◀️ Left (Legacy)
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="warn"
                  (click)="testOverlayPositioning($event, 'center')"
                  class="w-100">
                  🎯 Center (Legacy)
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="warn"
                  (click)="testOverlayPositioning($event, 'right')"
                  class="w-100">
                  ▶️ Right (Legacy)
                </button>
              </div>

              <!-- Bottom Row -->
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOverlayPositioning($event, 'bottom-left')"
                  class="w-100">
                  🔻 Bottom Left
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOverlayPositioning($event, 'bottom-center')"
                  class="w-100">
                  🔻 Bottom Center (Default)
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOverlayPositioning($event, 'bottom-right')"
                  class="w-100">
                  🔻 Bottom Right
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test tất cả 9 positioning options: 6 options mới (top-*, bottom-*) và 3 legacy options (left, center, right).
              Default là <strong>bottom-center</strong>.
            </p>
          </div>

          <!-- Features List -->
          <div class="mt-4">
            <h6>✨ Tính năng được test:</h6>
            <ul class="text-muted small">
              <li><strong>Overlay Positioning:</strong> Tính toán vị trí tối ưu dựa trên trigger element</li>
              <li><strong>Responsive Behavior:</strong> Auto-selection giữa overlay dialog và bottom sheet</li>
              <li><strong>Force Mode:</strong> Có thể force sử dụng dialog hoặc bottom sheet</li>
              <li><strong>Smart Positioning:</strong> Tự động điều chỉnh vị trí khi không đủ không gian</li>
              <li><strong>Viewport Awareness:</strong> Tính toán dựa trên kích thước viewport</li>
              <li><strong>Type Safety:</strong> Tương thích với StrictModalComponent interface</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test ResponsiveModalService.openWithPopover -->
  <div class="row mb-4" *ngIf="testFeatures['popoverModalTest']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🎯 Test ResponsiveModalService.openWithPopover</h3>
          <p class="text-muted mb-0">Test method mới openWithPopover với PrimeNG Popover positioning trên desktop và bottom-sheet trên mobile</p>
        </div>
        <div class="card-body">
          <!-- Basic Popover Test -->
          <div>
            <h5 class="mb-3">🔧 Basic Popover Modal Test</h5>
            <div class="row">
              <div class="col-md-6 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOpenWithPopover($event)"
                  class="w-100">
                  🎯 Test Popover Modal
                </button>
              </div>
              <div class="col-md-6 mb-2">
                <button
                  mat-raised-button
                  color="accent"
                  (click)="testPopoverEdgeCases($event)"
                  class="w-100">
                  ⚠️ Test Edge Cases
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test cơ bản openWithPopover method và các edge cases (fallback, force mobile mode).
            </p>
          </div>

          <!-- Popover Positioning Test -->
          <div *ngIf="testFeatures['popoverPositioningTest']">
            <h5 class="mt-4 mb-3">📍 Popover Positioning Test</h5>
            <div class="row">
              <!-- Corner positions -->
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testPopoverPositioning($event, 'Top Left Corner')"
                  class="w-100"
                  style="position: relative; top: 0; left: 0;">
                  📍 Top Left
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testPopoverPositioning($event, 'Top Right Corner')"
                  class="w-100"
                  style="position: relative; top: 0; right: 0;">
                  📍 Top Right
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="accent"
                  (click)="testPopoverPositioning($event, 'Center Position')"
                  class="w-100">
                  📍 Center
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="warn"
                  (click)="testPopoverPositioning($event, 'Near Viewport Edge')"
                  class="w-100"
                  style="margin-left: auto; display: block;">
                  📍 Edge Case
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test PrimeNG Popover positioning logic và viewport collision detection với các vị trí trigger khác nhau.
            </p>
          </div>

          <!-- Responsive Test -->
          <div *ngIf="testFeatures['popoverResponsiveTest']">
            <h5 class="mt-4 mb-3">📱 Responsive Behavior Test</h5>
            <div class="row">
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="success"
                  (click)="testPopoverPositioning($event, 'Desktop Mode')"
                  class="w-100">
                  🖥️ Desktop Test
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="success"
                  (click)="testPopoverPositioning($event, 'Mobile Mode')"
                  class="w-100">
                  📱 Mobile Test
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="success"
                  (click)="testPopoverPositioning($event, 'Breakpoint Test')"
                  class="w-100">
                  🔄 Breakpoint Test
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test responsive behavior: Desktop sử dụng PrimeNG Popover-like positioning, Mobile sử dụng bottom sheet.
              Thay đổi kích thước browser để test breakpoint transitions.
            </p>
          </div>

          <!-- Features List -->
          <div class="mt-4">
            <h6>✨ Tính năng được test:</h6>
            <ul class="text-muted small">
              <li><strong>PrimeNG Popover Positioning:</strong> Modal positioned giống PrimeNG Popover component</li>
              <li><strong>Responsive Design:</strong> Auto-selection giữa popover (desktop) và bottom-sheet (mobile)</li>
              <li><strong>ConnectedOverlayModalService:</strong> Sử dụng connected element positioning</li>
              <li><strong>Fallback Mechanism:</strong> Fallback về StandardOverlayModalService khi không có trigger element</li>
              <li><strong>Force Mode:</strong> Support force dialog hoặc bottom-sheet mode</li>
              <li><strong>Type Safety:</strong> Async/await pattern với Promise return type</li>
              <li><strong>PrimeNG Styling:</strong> Sử dụng p-popover và p-component classes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test ResponsiveModalService.openWithMatMenu -->
  <div class="row mb-4" *ngIf="testFeatures['matMenuModalTest']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🎯 Test ResponsiveModalService.openWithMatMenu</h3>
          <p class="text-muted mb-0">Test method mới openWithMatMenu với mat-menu positioning trên desktop và bottom-sheet trên mobile</p>
        </div>
        <div class="card-body">
          <!-- Basic Mat Menu Test -->
          <div>
            <h5 class="mb-3">🔧 Basic Mat Menu Modal Test</h5>
            <div class="row">
              <div class="col-md-6 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testOpenWithMatMenu($event)"
                  class="w-100">
                  🎯 Test Mat Menu Modal
                </button>
              </div>
              <div class="col-md-6 mb-2">
                <button
                  mat-raised-button
                  color="accent"
                  (click)="testMatMenuEdgeCases()"
                  class="w-100">
                  ⚠️ Test Edge Cases
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test cơ bản openWithMatMenu method và các edge cases (null trigger, invalid element).
            </p>
          </div>

          <!-- Mat Menu Positioning Test -->
          <div *ngIf="testFeatures['matMenuPositioningTest']">
            <h5 class="mt-4 mb-3">📍 Mat Menu Positioning Test</h5>
            <div class="row">
              <!-- Corner positions -->
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testMatMenuPositioning($event, 'Top Left Corner')"
                  class="w-100"
                  style="position: relative; top: 0; left: 0;">
                  📍 Top Left
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="primary"
                  (click)="testMatMenuPositioning($event, 'Top Right Corner')"
                  class="w-100"
                  style="position: relative; top: 0; right: 0;">
                  📍 Top Right
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="accent"
                  (click)="testMatMenuPositioning($event, 'Center Position')"
                  class="w-100">
                  📍 Center
                </button>
              </div>
              <div class="col-md-3 mb-2">
                <button
                  mat-raised-button
                  color="warn"
                  (click)="testMatMenuPositioning($event, 'Near Viewport Edge')"
                  class="w-100"
                  style="margin-left: auto; display: block;">
                  📍 Edge Case
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test positioning logic và viewport collision detection với các vị trí trigger khác nhau.
            </p>
          </div>

          <!-- Responsive Test -->
          <div *ngIf="testFeatures['matMenuResponsiveTest']">
            <h5 class="mt-4 mb-3">📱 Responsive Behavior Test</h5>
            <div class="row">
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="success"
                  (click)="testMatMenuPositioning($event, 'Desktop Mode')"
                  class="w-100">
                  🖥️ Desktop Test
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="success"
                  (click)="testMatMenuPositioning($event, 'Mobile Mode')"
                  class="w-100">
                  📱 Mobile Test
                </button>
              </div>
              <div class="col-md-4 mb-2">
                <button
                  mat-raised-button
                  color="success"
                  (click)="testMatMenuPositioning($event, 'Breakpoint Test')"
                  class="w-100">
                  🔄 Breakpoint Test
                </button>
              </div>
            </div>
            <p class="text-muted small">
              Test responsive behavior: Desktop sử dụng positioned dialog, Mobile sử dụng bottom sheet.
              Thay đổi kích thước browser để test breakpoint transitions.
            </p>
          </div>

          <!-- Features List -->
          <div class="mt-4">
            <h6>✨ Tính năng được test:</h6>
            <ul class="text-muted small">
              <li><strong>Mat Menu Positioning:</strong> Dialog positioned giống Angular Material mat-menu</li>
              <li><strong>Responsive Design:</strong> Auto-selection giữa dialog (desktop) và bottom-sheet (mobile)</li>
              <li><strong>Viewport Collision Detection:</strong> Tự động điều chỉnh position khi overflow viewport</li>
              <li><strong>Type Safety:</strong> Compatible với MatDialogRef và MatBottomSheetRef interfaces</li>
              <li><strong>Error Handling:</strong> Validate trigger element và handle edge cases</li>
              <li><strong>Flexible Configuration:</strong> Support MatDialogConfig options</li>
              <li><strong>Backdrop & Styling:</strong> Mat-menu-like backdrop và panel classes</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test Threads-like Mat-Menu Animation -->
  <div class="row mb-4" *ngIf="testFeatures['threadsMenuAnimation']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🎭 Test Threads-like Mat-Menu Animation</h3>
          <p class="text-muted mb-0">Test hiệu ứng animation giống ứng dụng Threads: scale background + slide-up menu</p>
        </div>
        <div class="card-body">
          <!-- Basic Threads Animation -->
          <div class="mb-4">
            <h5 class="mb-3">🎯 Basic Threads Animation</h5>
            <div class="apply-threads-menu-basic p-4 border rounded" style="background: #f8f9fa;">
              <div class="d-flex gap-3 align-items-center">
                <button mat-raised-button
                        color="primary"
                        [matMenuTriggerFor]="basicMenu"
                        (menuOpened)="onThreadsMenuOpened('basic')"
                        (menuClosed)="onThreadsMenuClosed('basic')">
                  🎯 Basic Threads Menu
                </button>
                <mat-menu #basicMenu="matMenu">
                  <button mat-menu-item>
                    <mat-icon>home</mat-icon>
                    <span>Trang chủ</span>
                  </button>
                  <button mat-menu-item>
                    <mat-icon>person</mat-icon>
                    <span>Hồ sơ</span>
                  </button>
                  <button mat-menu-item>
                    <mat-icon>settings</mat-icon>
                    <span>Cài đặt</span>
                  </button>
                  <mat-divider></mat-divider>
                  <button mat-menu-item>
                    <mat-icon>logout</mat-icon>
                    <span>Đăng xuất</span>
                  </button>
                </mat-menu>
                <span class="text-muted">Scale background + slide-up menu</span>
              </div>
            </div>
          </div>

          <!-- Enhanced Threads Animation với Blur -->
          <div class="mb-4">
            <h5 class="mb-3">✨ Enhanced Threads Animation (với Blur)</h5>
            <div class="apply-threads-menu-enhanced p-4 border rounded" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
              <div class="d-flex gap-3 align-items-center">
                <button mat-raised-button
                        color="accent"
                        [matMenuTriggerFor]="enhancedMenu"
                        (menuOpened)="onThreadsMenuOpened('enhanced')"
                        (menuClosed)="onThreadsMenuClosed('enhanced')">
                  ✨ Enhanced Threads Menu
                </button>
                <mat-menu #enhancedMenu="matMenu">
                  <button mat-menu-item>
                    <mat-icon>dashboard</mat-icon>
                    <span>Dashboard</span>
                  </button>
                  <button mat-menu-item>
                    <mat-icon>analytics</mat-icon>
                    <span>Thống kê</span>
                  </button>
                  <button mat-menu-item>
                    <mat-icon>inventory</mat-icon>
                    <span>Kho hàng</span>
                  </button>
                  <button mat-menu-item>
                    <mat-icon>shopping_cart</mat-icon>
                    <span>Đơn hàng</span>
                  </button>
                  <mat-divider></mat-divider>
                  <button mat-menu-item>
                    <mat-icon>help</mat-icon>
                    <span>Trợ giúp</span>
                  </button>
                </mat-menu>
                <span>Scale + blur background + slide-up menu</span>
              </div>
            </div>
          </div>

          <!-- Multiple Menus Test -->
          <div class="mb-4">
            <h5 class="mb-3">🔄 Multiple Menus Test</h5>
            <div class="row">
              <div class="col-md-4 mb-3">
                <div class="apply-threads-menu-basic p-3 border rounded text-center" style="background: #e3f2fd;">
                  <button mat-raised-button
                          color="primary"
                          [matMenuTriggerFor]="menu1"
                          class="w-100">
                    📱 Menu 1
                  </button>
                  <mat-menu #menu1="matMenu">
                    <button mat-menu-item>Action 1</button>
                    <button mat-menu-item>Action 2</button>
                    <button mat-menu-item>Action 3</button>
                  </mat-menu>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="apply-threads-menu-enhanced p-3 border rounded text-center" style="background: #f3e5f5;">
                  <button mat-raised-button
                          color="accent"
                          [matMenuTriggerFor]="menu2"
                          class="w-100">
                    🎨 Menu 2
                  </button>
                  <mat-menu #menu2="matMenu">
                    <button mat-menu-item>Design 1</button>
                    <button mat-menu-item>Design 2</button>
                    <button mat-menu-item>Design 3</button>
                  </mat-menu>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="apply-threads-menu-basic p-3 border rounded text-center" style="background: #e8f5e8;">
                  <button mat-raised-button
                          color="primary"
                          [matMenuTriggerFor]="menu3"
                          class="w-100">
                    ⚙️ Menu 3
                  </button>
                  <mat-menu #menu3="matMenu">
                    <button mat-menu-item>Setting 1</button>
                    <button mat-menu-item>Setting 2</button>
                    <button mat-menu-item>Setting 3</button>
                  </mat-menu>
                </div>
              </div>
            </div>
            <p class="text-muted small">
              Test multiple menus với Threads animation để đảm bảo không có conflicts.
            </p>
          </div>

          <!-- Animation Status -->
          <div class="alert alert-info">
            <h6 class="mb-2">📊 Animation Status:</h6>
            <ul class="mb-0">
              <li><strong>Basic Menu:</strong> {{ threadsMenuStatus.basic || 'Closed' }}</li>
              <li><strong>Enhanced Menu:</strong> {{ threadsMenuStatus.enhanced || 'Closed' }}</li>
              <li><strong>Animation Duration:</strong> 280ms slide-up, 250ms scale</li>
              <li><strong>Easing:</strong> cubic-bezier(0.25, 0.8, 0.25, 1)</li>
            </ul>
          </div>

          <!-- Features List -->
          <div class="mt-4">
            <h6 class="mb-3">✨ Tính năng được test:</h6>
            <div class="row">
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li><strong>🎯 Scale Effect:</strong> Thu nhỏ background container</li>
                  <li><strong>📱 Slide-up Effect:</strong> Menu trượt lên từ dưới</li>
                  <li><strong>✨ Blur Effect:</strong> Làm mờ background (enhanced)</li>
                  <li><strong>🎭 Smooth Animation:</strong> 280ms với easing curves</li>
                </ul>
              </div>
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li><strong>📱 Responsive:</strong> Tối ưu cho mobile</li>
                  <li><strong>🌙 Dark Mode:</strong> Hỗ trợ dark theme</li>
                  <li><strong>♿ Accessibility:</strong> Reduced motion support</li>
                  <li><strong>🔄 Multiple Menus:</strong> Không conflicts</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test ResponsiveModalService với Threads Animation -->
  <div class="row mb-4" *ngIf="testFeatures['responsiveModalThreadsAnimation']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🚀 Test ResponsiveModalService với Threads Animation</h3>
          <p class="text-muted mb-0">Test tích hợp Threads-like animation vào ResponsiveModalService (auto-apply)</p>
        </div>
        <div class="card-body">
          <!-- Basic Integration Test -->
          <div class="mb-4">
            <h5 class="mb-3">🎯 Basic Integration (Auto-apply)</h5>
            <div class="p-4 border rounded" style="background: #f8f9fa;">
              <div class="d-flex gap-3 align-items-center flex-wrap">
                <button class="btn btn-primary"
                        (click)="testResponsiveModalWithThreadsBasic($event)">
                  🎯 Basic Threads Modal
                </button>
                <button class="btn btn-success"
                        (click)="testResponsiveModalWithThreadsEnhanced($event)">
                  ✨ Enhanced Threads Modal
                </button>
                <button class="btn btn-info"
                        (click)="testResponsiveModalWithThreadsDisabled($event)">
                  🚫 Disabled Animation
                </button>
                <span class="text-muted">Auto-apply animation từ ResponsiveModalService</span>
              </div>
            </div>
          </div>

          <!-- Mat-Menu Integration Test -->
          <div class="mb-4">
            <h5 class="mb-3">📱 Mat-Menu Integration (openWithMatMenu)</h5>
            <div class="p-4 border rounded" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
              <div class="d-flex gap-3 align-items-center flex-wrap">
                <button class="btn btn-light"
                        (click)="testMatMenuWithThreadsBasic($event)">
                  🎯 Mat-Menu Basic
                </button>
                <button class="btn btn-warning"
                        (click)="testMatMenuWithThreadsEnhanced($event)">
                  ✨ Mat-Menu Enhanced
                </button>
                <button class="btn btn-secondary"
                        (click)="testMatMenuWithThreadsCustom($event)">
                  🎨 Custom Classes
                </button>
                <span>openWithMatMenu() với Threads animation</span>
              </div>
            </div>
          </div>

          <!-- Popover Integration Test -->
          <div class="mb-4">
            <h5 class="mb-3">🎪 Popover Integration (openWithPopover)</h5>
            <div class="p-4 border rounded" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white;">
              <div class="d-flex gap-3 align-items-center flex-wrap">
                <button class="btn btn-light"
                        (click)="testPopoverWithThreadsBasic($event)">
                  🎯 Popover Basic
                </button>
                <button class="btn btn-dark"
                        (click)="testPopoverWithThreadsEnhanced($event)">
                  ✨ Popover Enhanced
                </button>
                <button class="btn btn-outline-light"
                        (click)="testPopoverWithThreadsMobile($event)">
                  📱 Force Mobile Mode
                </button>
                <span>openWithPopover() với Threads animation</span>
              </div>
            </div>
          </div>

          <!-- Responsive Test -->
          <div class="mb-4">
            <h5 class="mb-3">📱 Responsive Behavior Test</h5>
            <div class="row">
              <div class="col-md-4 mb-3">
                <div class="p-3 border rounded text-center" style="background: #e3f2fd;">
                  <button class="btn btn-primary w-100 mb-2"
                          (click)="testResponsiveDesktop($event)">
                    🖥️ Force Desktop
                  </button>
                  <small class="text-muted">Mat-menu với animation</small>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="p-3 border rounded text-center" style="background: #f3e5f5;">
                  <button class="btn btn-secondary w-100 mb-2"
                          (click)="testResponsiveMobile($event)">
                    📱 Force Mobile
                  </button>
                  <small class="text-muted">Bottom sheet với animation</small>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="p-3 border rounded text-center" style="background: #e8f5e8;">
                  <button class="btn btn-success w-100 mb-2"
                          (click)="testResponsiveAuto($event)">
                    🔄 Auto Responsive
                  </button>
                  <small class="text-muted">Tự động theo viewport</small>
                </div>
              </div>
            </div>
          </div>

          <!-- Animation Status -->
          <div class="alert alert-info">
            <h6 class="mb-2">📊 ResponsiveModalService Animation Status:</h6>
            <ul class="mb-0">
              <li><strong>Integration Status:</strong> {{ responsiveModalThreadsStatus.integration || 'Ready' }}</li>
              <li><strong>Last Test:</strong> {{ responsiveModalThreadsStatus.lastTest || 'None' }}</li>
              <li><strong>Animation Type:</strong> {{ responsiveModalThreadsStatus.animationType || 'Basic' }}</li>
              <li><strong>Mode:</strong> {{ responsiveModalThreadsStatus.mode || 'Auto' }}</li>
            </ul>
          </div>

          <!-- Integration Features -->
          <div class="mt-4">
            <h6 class="mb-3">🚀 Tính năng tích hợp:</h6>
            <div class="row">
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li><strong>🔄 Auto-apply:</strong> Tự động áp dụng animation</li>
                  <li><strong>⚙️ Configuration:</strong> threadsAnimation config option</li>
                  <li><strong>🎯 Backward Compatible:</strong> Không ảnh hưởng code cũ</li>
                  <li><strong>📱 Responsive:</strong> Desktop mat-menu, mobile bottom-sheet</li>
                </ul>
              </div>
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li><strong>🎨 Customizable:</strong> Custom CSS classes support</li>
                  <li><strong>🧹 Auto Cleanup:</strong> Tự động cleanup animation</li>
                  <li><strong>🔧 Type Safe:</strong> TypeScript interface support</li>
                  <li><strong>🎪 Multiple Modes:</strong> Dialog, Popover, Mat-menu</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Test StandardBottomSheetService với Threads Animation -->
  <div class="row mb-4" *ngIf="testFeatures['standardBottomSheetThreadsAnimation']">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3>🎭 Test StandardBottomSheetService với Threads Animation</h3>
          <p class="text-muted mb-0">Test tích hợp Threads-like animation vào StandardBottomSheetService (auto-apply .app-drawer scale)</p>
        </div>
        <div class="card-body">
          <!-- App Drawer Detection -->
          <div class="mb-4">
            <div class="alert alert-info">
              <h6 class="mb-2">📱 App Drawer Detection:</h6>
              <p class="mb-2">
                <strong>Target Element:</strong> <code>.app-drawer</code>
                <span class="badge badge-{{ standardBottomSheetThreadsStatus.appDrawerFound === 'Found' ? 'success' : 'warning' }} ms-2">
                  {{ standardBottomSheetThreadsStatus.appDrawerFound }}
                </span>
              </p>
              <p class="mb-0">
                <strong>Animation Status:</strong> {{ standardBottomSheetThreadsStatus.animationActive }}
              </p>
            </div>
          </div>

          <!-- Basic StandardBottomSheetService Tests -->
          <div class="mb-4">
            <h5 class="mb-3">🎯 Basic StandardBottomSheetService Tests</h5>
            <div class="p-4 border rounded" style="background: #f8f9fa;">
              <div class="d-flex gap-3 align-items-center flex-wrap">
                <button class="btn btn-primary"
                        (click)="testStandardBottomSheetBasic()">
                  🎯 Basic Bottom Sheet
                </button>
                <button class="btn btn-success"
                        (click)="testStandardBottomSheetWithData()">
                  📊 With Data
                </button>
                <button class="btn btn-info"
                        (click)="testStandardBottomSheetAsync()">
                  ⚡ Async Method
                </button>
                <span class="text-muted">Auto-apply Threads animation (.app-drawer scale)</span>
              </div>
            </div>
          </div>

          <!-- Advanced Tests -->
          <div class="mb-4">
            <h5 class="mb-3">🚀 Advanced Animation Tests</h5>
            <div class="p-4 border rounded" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
              <div class="d-flex gap-3 align-items-center flex-wrap">
                <button class="btn btn-light"
                        (click)="testStandardBottomSheetMultiple()">
                  🔄 Multiple Sheets
                </button>
                <button class="btn btn-warning"
                        (click)="testStandardBottomSheetQuickOpen()">
                  ⚡ Quick Open/Close
                </button>
                <button class="btn btn-danger"
                        (click)="testStandardBottomSheetStress()">
                  💪 Stress Test
                </button>
                <span>Test animation performance và cleanup</span>
              </div>
            </div>
          </div>

          <!-- Animation Timing Test -->
          <div class="mb-4">
            <h5 class="mb-3">⏱️ Animation Timing Test</h5>
            <div class="row">
              <div class="col-md-4 mb-3">
                <div class="p-3 border rounded text-center" style="background: #e3f2fd;">
                  <button class="btn btn-primary w-100 mb-2"
                          (click)="testAnimationTiming('fast')">
                    ⚡ Fast Animation
                  </button>
                  <small class="text-muted">Scale 50% → Slide up</small>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="p-3 border rounded text-center" style="background: #f3e5f5;">
                  <button class="btn btn-secondary w-100 mb-2"
                          (click)="testAnimationTiming('normal')">
                    🎭 Normal Timing
                  </button>
                  <small class="text-muted">280ms slide-up, 250ms scale</small>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="p-3 border rounded text-center" style="background: #e8f5e8;">
                  <button class="btn btn-success w-100 mb-2"
                          (click)="testAnimationTiming('slow')">
                    🐌 Slow Animation
                  </button>
                  <small class="text-muted">Debug timing</small>
                </div>
              </div>
            </div>
          </div>

          <!-- Animation Status -->
          <div class="alert alert-info">
            <h6 class="mb-2">📊 StandardBottomSheetService Animation Status:</h6>
            <ul class="mb-0">
              <li><strong>Integration Status:</strong> {{ standardBottomSheetThreadsStatus.integration || 'Ready' }}</li>
              <li><strong>Last Test:</strong> {{ standardBottomSheetThreadsStatus.lastTest || 'None' }}</li>
              <li><strong>App Drawer Found:</strong> {{ standardBottomSheetThreadsStatus.appDrawerFound || 'Unknown' }}</li>
              <li><strong>Animation Active:</strong> {{ standardBottomSheetThreadsStatus.animationActive || 'No' }}</li>
            </ul>
          </div>

          <!-- Integration Features -->
          <div class="mt-4">
            <h6 class="mb-3">🎭 Tính năng tích hợp:</h6>
            <div class="row">
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li><strong>🎯 Auto-apply:</strong> Tự động áp dụng animation</li>
                  <li><strong>📱 App Drawer Target:</strong> Scale down .app-drawer element</li>
                  <li><strong>⚡ Auto Cleanup:</strong> Tự động cleanup khi đóng</li>
                  <li><strong>🎭 Timing Control:</strong> 280ms slide-up, 250ms scale</li>
                </ul>
              </div>
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li><strong>🔄 Backward Compatible:</strong> Không ảnh hưởng code cũ</li>
                  <li><strong>📱 Responsive:</strong> Mobile-optimized animation</li>
                  <li><strong>♿ Accessibility:</strong> Reduced motion support</li>
                  <li><strong>🧹 Memory Safe:</strong> Proper timeout cleanup</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

<!-- Templates cho StandardDialog -->
<ng-template #customTitleTemplate let-data>
  <div class="d-flex align-items-center">
    <mat-icon class="me-2 text-warning">warning</mat-icon>
    <div>
      <h3 class="mb-0">Cảnh báo quan trọng</h3>
      <small class="text-muted">Bạn đang thực hiện {{ data.action }} cho {{ data.userName }}</small>
    </div>
  </div>
</ng-template>

<ng-template #customActionsTemplate let-data let-onCustomAction="onCustomAction" let-onClose="onClose">
  <div class="d-flex gap-2 flex-wrap">
    <button mat-button (click)="onCustomSave(onCustomAction)" class="btn btn-outline-primary">
      <mat-icon class="me-1">save</mat-icon>
      Lưu
    </button>
    <button mat-raised-button color="primary" (click)="onCustomSaveAndContinue(onCustomAction)">
      <mat-icon class="me-1">save</mat-icon>
      Lưu và Tiếp tục
    </button>
    <button mat-button (click)="onCustomCancel(onClose)" class="btn btn-outline-secondary">
      <mat-icon class="me-1">close</mat-icon>
      Hủy
    </button>
  </div>
</ng-template>
