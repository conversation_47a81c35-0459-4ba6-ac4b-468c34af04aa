import { OnInit, On<PERSON>hanges, OnDestroy, SimpleChanges, ChangeDetectorRef, inject, signal, Directive, EventEmitter, Output } from '@angular/core';
import { FormControl, Validators, ValidatorFn } from '@angular/forms';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

import { BaseFieldComponent, FormFieldComponent, FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldConstraints, FieldValue } from '@domain/entities/field.entity';
import { getFieldIcon, getFieldIconColor } from '@/shared/components/dynamic-layout-builder/utils/field.utils';
import { FormDataManagementServiceInterface } from '@/shared/components/dynamic-layout-builder/services/form-data-management.service';
import { FieldValidationService } from '@/shared/components/dynamic-layout-builder/services/field-validation.service';

/**
 * Abstract base class cho tất cả field components trong Dynamic Layout Builder
 * 
 * Cung cấp:
 * - Lifecycle management (ngOnInit, ngOnChanges, ngOnDestroy)
 * - Subscription management để tránh memory leaks
 * - Common logic cho việc khởi tạo FormControl từ field.value
 * - Template methods cho các field components kế thừa
 * - Consistent implementation của BaseFieldComponent và FormFieldComponent interfaces
 * 
 * Các field components con chỉ cần:
 * - Implement abstract methods: validateFieldType(), generateMockValue(), getValidators(), getFieldIcon()
 * - Override methods nếu cần custom behavior
 */
@Directive()
export abstract class AbstractFieldComponent implements OnInit, OnChanges, OnDestroy, BaseFieldComponent, FormFieldComponent {

  // ===== ABSTRACT PROPERTIES =====
  
  /**
   * Configuration object - phải được set bởi component con
   */
  abstract config: FieldItemConfig;

  // ===== INJECTED SERVICES =====

  protected translateService = inject(TranslateService);
  protected cdr = inject(ChangeDetectorRef);

  // ===== STATE MANAGEMENT =====
  
  // Subscription để quản lý form control changes
  protected subscriptions = new Subscription();

  // Signals để quản lý state
  protected placeholderValue = signal<string>('');
  public formControl = signal<FormControl>(new FormControl());
  protected isVisible = signal<boolean>(true);
  protected isReadOnlyState = signal<boolean>(false);

  // Form mode state
  protected currentValue = signal<FieldValue>('');
  protected validationErrors = signal<string[]>([]);

  // Edit mode state - cho inline editing
  protected isInEditMode = signal<boolean>(false);
  protected originalValue = signal<FieldValue>(''); // Backup value để cancel

  // FormDataManagementService instance (từ config)
  protected formDataService: FormDataManagementServiceInterface | null = null;

  // Inject FieldValidationService
  protected fieldValidationService = inject(FieldValidationService);

  /**
   * Event emitters để gửi dữ liệu lên parent component
   */
  @Output() valueChange = new EventEmitter<{ fieldId: string; value: FieldValue }>();
  


  // ===== LIFECYCLE HOOKS =====

  ngOnInit(): void {
    // Lấy FormDataManagementService từ config nếu có
    this.formDataService = this.config.formDataService || null;

    this.initializeField();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Khi config thay đổi, cần xử lý cẩn thận để không mất dữ liệu người dùng đã nhập
    if (changes['config'] && this.config) {
      // Cập nhật FormDataManagementService reference
      this.formDataService = this.config.formDataService || null;

      this.validateFieldType();

      const configChange = changes['config'];
      const isFirstTime = !configChange.previousValue;
      const fieldIdChanged = configChange.previousValue?.field?._id !== this.config.field._id;
      const fieldValueChanged = configChange.previousValue?.field?.value !== this.config.field.value;

      if (isFirstTime || fieldIdChanged) {
        // Khởi tạo hoàn toàn mới cho field mới hoặc lần đầu
        this.initializeFormControl();
        this.initializeField();
      } else if (fieldValueChanged && !this.formControl().dirty) {
        // Chỉ cập nhật giá trị nếu form control chưa bị user thay đổi (dirty = false)
        this.formControl().setValue(this.config.field.value || null, { emitEvent: false });
        this.initializeField();
      } else {
        // Chỉ cập nhật field-specific logic, không động đến FormControl value
        this.initializeField();
      }
    }
  }

  ngOnDestroy(): void {
    // Hủy tất cả subscriptions để tránh memory leaks
    this.subscriptions.unsubscribe();
  }

  // ===== ABSTRACT METHODS - PHẢI ĐƯỢC IMPLEMENT BỞI COMPONENT CON =====

  /**
   * Validate rằng field type được hỗ trợ bởi component
   */
  protected abstract validateFieldType(): void;

  /**
   * Generate placeholder value dựa trên field type cho preview mode
   */
  protected abstract generatePlaceholderValue(): void;

  /**
   * Load actual value từ form data cho view mode
   * Template method - có thể được override nếu cần custom logic
   */
  protected loadActualValue(): void {
    // Mặc định: sử dụng field.value hoặc form data nếu có
    let actualValue = this.getInitialValue();

    // Nếu có form data service và field đã được edit, lấy giá trị từ form data
    if (this.config.formDataService && this.config.field._id) {
      const formValue = this.config.formDataService.getFieldValue(this.config.field._id);
      if (formValue !== undefined && formValue !== null) {
        actualValue = formValue;
      }
    }

    this.currentValue.set(actualValue);
  }

  /**
   * Lấy validators phù hợp cho field type
   */
  protected abstract getValidators(): ValidatorFn[];

  

  // ===== TEMPLATE METHODS - CÓ THỂ ĐƯỢC OVERRIDE BỞI COMPONENT CON =====

  /**
   * Khởi tạo field dựa trên config
   * Template method - có thể được override nếu cần custom logic
   */
  protected initializeField(): void {
    // Validate field type
    this.validateFieldType();

    // Kiểm tra visibility dựa trên permission
    this.isVisible.set(this.config.currentPermission !== 'none');

    // Kiểm tra read-only state
    // Field sẽ read-only khi:
    // 1. Permission là 'read' trong form mode, HOẶC
    // 2. enableEditMode = false trong form mode (bất kể permission)
    this.isReadOnlyState.set(
      this.config.currentViewMode === 'form' && (
        this.config.currentPermission === 'read' ||
        this.config.enableEditMode === false
      )
    );

    // Cập nhật FormControl disabled state nếu đã tồn tại
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      if (control) {
        if (this.isReadOnly()) {
          control.disable({ emitEvent: false });
        } else {
          control.enable({ emitEvent: false });
        }
      }
    }

    // Xử lý theo view mode
    if (this.config.currentViewMode === 'preview') {
      // Preview mode: hiển thị placeholder/mock data
      this.generatePlaceholderValue();
    } else if (this.config.currentViewMode === 'view') {
      // View mode: hiển thị actual values từ form data
      this.loadActualValue();
    } else if (this.config.currentViewMode === 'form') {
      // Form mode: setup form controls
      // QUAN TRỌNG: Không gọi initializeFormControl() ở đây để tránh ghi đè user input
      // initializeFormControl() chỉ được gọi từ ngOnChanges khi thực sự cần thiết

      // Hủy subscription cũ trước khi tạo subscription mới
      this.subscriptions.unsubscribe();
      this.subscriptions = new Subscription();

      // Chỉ setup subscriptions, KHÔNG khởi tạo lại FormControl
      this.setupFormValueSubscription();
    }
  }

  /**
   * Xử lý khi field.value thay đổi từ bên ngoài
   * Template method - có thể được override nếu cần custom logic
   * CHỈ cập nhật nếu form control chưa bị user thay đổi (dirty = false)
   */
  handleFieldValueChange(): void {
    if (this.config.currentViewMode === 'preview') {
      // Preview mode: regenerate placeholder data
      this.generatePlaceholderValue();
    } else if (this.config.currentViewMode === 'view') {
      // View mode: reload actual value
      this.loadActualValue();
    } else if (this.config.currentViewMode === 'form') {
      // Form mode: cập nhật form control nếu chưa dirty
      const control = this.formControl();
      if (control && !control.dirty) {
        // Chỉ cập nhật nếu form control chưa bị user thay đổi
        const newValue = this.getInitialValue();
        control.setValue(newValue, { emitEvent: false });
        this.currentValue.set(newValue);
      }
    }
  }

  /**
   * Lấy initial value từ field.value
   * Template method - có thể được override nếu cần custom logic
   */
  protected getInitialValue(): FieldValue {
    return this.config.field.value;
  }

  // ===== BASEFIELCOMPONENT INTERFACE IMPLEMENTATION =====

  /**
   * Kiểm tra xem có nên hiển thị field hay không
   */
  shouldShowField(): boolean {
    return this.isVisible();
  }

  /**
   * Lấy placeholder text cho field
   */
  getPlaceholder(): string {
    return this.config.field.placeholder || 
           `DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.${this.config.field.type.toUpperCase()}`;
  }

  /**
   * Lấy tooltip text cho read-only fields
   */
  getReadOnlyTooltip(): string {
    return 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP';
  }

  // ===== FORMFIELDCOMPONENT INTERFACE IMPLEMENTATION =====

  /**
   * Kiểm tra xem field có ở chế độ read-only không
   */
  isReadOnly(): boolean {
    return this.isReadOnlyState();
  }

  /**
   * Khởi tạo form control cho form mode
   */
  initializeFormControl(): void {
    // Lấy giá trị từ FormDataManagementService trước, nếu không có thì dùng field.value
    let initialValue = this.getInitialValue();

    if (this.formDataService && this.config.field._id) {
      const serviceValue = this.formDataService.getFieldValue(this.config.field._id);
      if (serviceValue !== null && serviceValue !== undefined) {
        initialValue = serviceValue;
      }
    }

    // Cập nhật currentValue signal để đồng bộ
    this.currentValue.set(initialValue);

    const control = new FormControl({
      value: initialValue,
      disabled: this.isReadOnly()
    });

    // Thêm validators dựa trên field type và requirements
    const validators = this.getValidators();
    if (validators.length > 0) {
      control.addValidators(validators);
    }

    this.formControl.set(control);
  }

  /**
   * Bind giá trị từ form data vào field
   */
  bindFormValue(value: FieldValue): void {
    this.currentValue.set(value);

    // Nếu đang ở form mode, cập nhật form control
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      control.setValue(value, { emitEvent: false });
    }
  }

  /**
   * Handle khi user thay đổi giá trị field
   */
  onValueChange(value: FieldValue): void {
    this.currentValue.set(value);

    // Emit change event để Form Management Service track
    const fieldId = this.config.field._id || '';
    this.valueChange.emit({ fieldId, value });

    // Trigger change detection để cập nhật UI
    this.cdr.markForCheck();
  }

  // ===== UTILITY METHODS =====

  /**
   * Lấy giá trị hiện tại của field
   */
  getCurrentValue(): FieldValue {
    if (this.config.currentViewMode === 'form') {
      return this.formControl().value;
    }
    return this.currentValue();
  }

  getFieldValue(): FieldValue {
    return this.getCurrentValue();
  }

  // ===== EDIT MODE MANAGEMENT =====

  /**
   * Kiểm tra xem field có đang trong edit mode không
   */
  isFieldInEditMode(): boolean {
    return this.isInEditMode();
  }

  /**
   * Kiểm tra xem có thể edit field này không (permission check + enableEditMode check)
   */
  canEditField(): boolean {
    return this.config.currentPermission === 'read_write' &&
           this.config.currentViewMode === 'view' && // Chỉ cho phép edit trong view mode (không phải preview hay form)
           this.config.enableEditMode !== false; // Kiểm tra enableEditMode từ config
  }

  /**
   * Bắt đầu edit mode cho field
   */
  startEditMode(): void {
    if (!this.canEditField()) {
      return;
    }

    // Backup giá trị hiện tại để có thể cancel
    this.originalValue.set(this.getCurrentValue());

    // Chuyển sang edit mode
    this.isInEditMode.set(true);

    // Khởi tạo form control nếu chưa có
    if (!this.formControl().value) {
      this.initializeFormControl();
      this.formControl().setValue(this.getCurrentValue());
    }

    this.cdr.markForCheck();
  }

  /**
   * Lưu thay đổi và thoát edit mode
   */
  saveEditMode(): void {
    if (!this.isInEditMode()) {
      return;
    }

    // Lấy giá trị mới từ form control
    const newValue = this.formControl().value;

    // Cập nhật current value để hiển thị ngay lập tức
    this.currentValue.set(newValue);

    // Cập nhật form data service nếu có
    if (this.config.formDataService && this.config.field._id) {
      this.config.formDataService.updateFieldValue(this.config.field._id, newValue, this.config.field);
    }

    // Emit change event
    this.onValueChange(newValue);

    // Thoát edit mode
    this.isInEditMode.set(false);

    this.cdr.markForCheck();
  }

  /**
   * Hủy thay đổi và thoát edit mode
   */
  cancelEditMode(): void {
    if (!this.isInEditMode()) {
      return;
    }

    // Khôi phục giá trị ban đầu
    const originalVal = this.originalValue();
    this.currentValue.set(originalVal);
    this.formControl().setValue(originalVal);

    // Thoát edit mode
    this.isInEditMode.set(false);

    this.cdr.markForCheck();
  }

  /**
   * Kiểm tra xem field có validation errors không
   * FIXED: Sử dụng cùng logic với shouldShowValidationErrors() để consistency
   */
  hasValidationErrors(): boolean {
    // Delegate to shouldShowValidationErrors() để đảm bảo logic consistent
    return this.shouldShowValidationErrors();
  }

  /**
   * Kiểm tra xem có nên hiển thị validation errors không
   * FIXED: Chỉ hiển thị validation errors khi thực sự cần thiết để tránh hiển thị errors không đúng khi init
   */
  shouldShowValidationErrors(): boolean {
    if (this.config.currentViewMode === 'form') {
      const control = this.formControl();
      const hasServiceErrors = this.getValidationErrors().length > 0;

      // Luôn hiển thị service errors (từ server validation)
      if (hasServiceErrors) {
        return true;
      }

      // Chỉ hiển thị FormControl errors khi:
      // 1. Field đã được user tương tác (touched hoặc dirty)
      // 2. HOẶC field trống và required (để hiển thị ngay khi cần thiết)
      if (control.invalid) {
        const isTouchedOrDirty = control.touched || control.dirty;
        const isEmptyAndRequired = this.isFieldEmptyAndRequired(control);

        return isTouchedOrDirty || isEmptyAndRequired;
      }

      return false;
    }

    // Trong view mode, chỉ hiển thị nếu có validation errors từ service
    return this.validationErrors().length > 0;
  }

  /**
   * Kiểm tra xem field có trống và required không
   * Helper method để xác định khi nào nên hiển thị required error ngay khi init
   */
  private isFieldEmptyAndRequired(control: any): boolean {
    // Chỉ check required error cho fields thực sự required
    if (!this.isFieldRequired()) {
      return false;
    }

    // Kiểm tra value có trống không
    const value = control.value;
    const isEmpty = value === null ||
                   value === undefined ||
                   value === '' ||
                   (Array.isArray(value) && value.length === 0);

    // Chỉ hiển thị required error nếu field thực sự trống
    // Điều này tránh hiển thị error cho fields có initial value nhưng chưa được validate đúng
    return isEmpty && control.hasError('required');
  }

  /**
   * Lấy danh sách validation errors
   * UPDATED: Tích hợp với FormDataManagementService để lấy validation results
   */
  getValidationErrors(): string[] {
    if (this.config.currentViewMode === 'form') {
      const errors: string[] = [];

      // 1. Lấy errors từ FormControl (Angular reactive forms)
      const control = this.formControl();
      if (control.errors) {
        if (control.errors['required']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED');
        }
        if (control.errors['email']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_EMAIL');
        }
        if (control.errors['pattern']) {
          // Pattern errors dựa trên field type
          switch (this.config.field.type) {
            case 'url':
              errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_URL');
              break;
            case 'phone':
              errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_PHONE');
              break;
            default:
              errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_FORMAT');
              break;
          }
        }
        if (control.errors['minlength']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MIN_LENGTH');
        }
        if (control.errors['maxlength']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_LENGTH');
        }
        if (control.errors['min']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MIN_VALUE');
        }
        if (control.errors['max']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_VALUE');
        }
        if (control.errors['matDatepickerParse']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_DATE');
        }
        if (control.errors['matDatepickerMin']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.DATE_TOO_EARLY');
        }
        if (control.errors['matDatepickerMax']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.DATE_TOO_LATE');
        }
        if (control.errors['invalidSelection']) {
          errors.push('DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_SELECTION');
        }
      }

      // 2. Lấy errors từ FormDataManagementService (business logic validation)
      if (this.config.formDataService) {
        const fieldId = this.config.field._id || '';
        const serviceErrors = this.config.formDataService.getFieldErrors(fieldId);

        // Convert service errors từ FORM_VALIDATION.* sang DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.*
        serviceErrors.forEach((error: string) => {
          const convertedError = this.convertServiceErrorToFieldError(error);
          if (convertedError && !errors.includes(convertedError)) {
            errors.push(convertedError);
          }
        });
      }

      return errors;
    }
    return this.validationErrors();
  }

  /**
   * Convert validation error từ FormDataManagementService format sang field error format
   * @param serviceError - Error từ service (FORM_VALIDATION.*)
   * @returns Converted error (DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.*)
   */
  private convertServiceErrorToFieldError(serviceError: string): string | null {
    const errorMap: Record<string, string> = {
      'FORM_VALIDATION.FIELD_REQUIRED': 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.REQUIRED',
      'FORM_VALIDATION.INVALID_EMAIL': 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_EMAIL',
      'FORM_VALIDATION.INVALID_URL': 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_URL',
      'FORM_VALIDATION.PHONE_TOO_LONG': 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_PHONE',
      'FORM_VALIDATION.TEXT_TOO_LONG': 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_LENGTH',
      'FORM_VALIDATION.NUMBER_TOO_LONG': 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.MAX_VALUE',
      'FORM_VALIDATION.INVALID_PICKLIST_VALUE': 'DYNAMIC_LAYOUT_RENDERER.FIELD_ERRORS.INVALID_FORMAT'
    };

    return errorMap[serviceError] || null;
  }

  /**
   * Lấy icon phù hợp cho field type
   */
  getFieldIcon(): string {
    return getFieldIcon(this.config.field.type);
  }

  /**
   * Lấy màu sắc icon cho field type
   */
  getFieldIconColor(): string {
    return getFieldIconColor(this.config.field.type);
  }
  

  /**
   * Setup subscription để theo dõi form control value changes
   */
  protected setupFormValueSubscription(): void {
    const control = this.formControl();
    if (!control) return;

    // Subscribe to value changes
    const valueChangeSub = control.valueChanges.subscribe((value: FieldValue) => {
      // Cập nhật FormDataManagementService nếu có
      if (this.formDataService && this.config.field._id) {
        this.formDataService.updateFieldValue(this.config.field._id, value, this.config.field);
      }

      // Gọi onValueChange callback nếu có
      if (this.onValueChange) {
        this.onValueChange(value);
      }
    });

    this.subscriptions.add(valueChangeSub);
  }

  /**
   * Kiểm tra xem field có required không từ constraints
   * Helper method để sử dụng trong templates
   */
  protected isFieldRequired(): boolean {
    const fieldConfig = this.config.field;
    return fieldConfig.constraints && 'isRequired' in fieldConfig.constraints
      ? !!(fieldConfig.constraints as FieldConstraints).isRequired
      : false;
  }

  /**
   * Kiểm tra xem field có public không từ constraints
   * Helper method để sử dụng trong templates
   */
  protected isFieldPublic(): boolean {
    const fieldConfig = this.config.field;
    return fieldConfig.constraints && 'isPublic' in fieldConfig.constraints
      ? !!(fieldConfig.constraints as FieldConstraints).isPublic
      : false;
  }

  /**
   * Lấy common validators (required)
   * UPDATED: Sử dụng FieldValidationService tập trung
   */
  protected getCommonValidators(): ValidatorFn[] {
    return this.fieldValidationService.getCommonValidators(this.config.field.constraints as FieldConstraints);
  }

  /**
   * Lấy tất cả validators cho field (common + field-specific)
   * UPDATED: Sử dụng FieldValidationService tập trung
   */
  protected getAllValidators(): ValidatorFn[] {
    return this.fieldValidationService.getValidatorsForField(this.config.field);
  }
}
