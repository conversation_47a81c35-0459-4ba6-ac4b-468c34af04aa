// Standard Overlay Modal - Main exports

// Component
export { StandardOverlayModalWrapperComponent } from './standard-overlay-modal.component';

// Service
export { StandardOverlayModalService } from './standard-overlay-modal.service';

// Interfaces
export type {
  StandardOverlayModalConfig,
  StandardOverlayModalConfigWithComponent,
  StandardOverlayModalActions,
  StandardOverlayModalPositioning
} from './models/standard-overlay-modal-config.interface';

export { DEFAULT_OVERLAY_MODAL_CONFIG } from './models/standard-overlay-modal-config.interface';

export type {
  StandardOverlayModalResult,
  StandardOverlayModalSuccessResult,
  StandardOverlayModalCancelledResult,
  StandardOverlayModalAnyResult
} from '../../models/view/standard-overlay-modal-result.interface';

export { StandardOverlayModalResultFactory } from '../../models/view/standard-overlay-modal-result.interface';
