import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FlashMessageService } from '@core/services/flash_message.service';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu đầu vào của CreateLayoutModal
 */
export interface CreateLayoutModalData {
  existingLayouts?: string[]; // Danh sách tên layout đã tồn tại để validate
  defaultName?: string; // Tên mặc định
  defaultDescription?: string; // Mô tả mặc định
}

/**
 * Interface cho kết quả trả về từ CreateLayoutModal
 */
export interface CreateLayoutModalResult {
  name: string;
  description: string;
  createdAt: string;
  isDefault: boolean;
}

/**
 * Modal component để tạo layout mới trong Dynamic Layout Builder
 *
 * Features:
 * - Input field cho tên layout
 * - Validation cho tên layout (required, unique)
 * - Description field (optional)
 * - Create/Cancel buttons
 * - i18n support
 * - Implement StrictModalComponent interface
 */
@Component({
  selector: 'app-create-layout-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './create-layout-modal.component.html',
  styleUrls: ['./create-layout-modal.component.scss']
})
export class CreateLayoutModalComponent implements StrictModalComponent<CreateLayoutModalData, CreateLayoutModalResult> {
  // Services
  private modalData = inject(MAT_DIALOG_DATA) as CreateLayoutModalData | undefined;
  private translateService = inject(TranslateService);
  private flashMessageService = inject(FlashMessageService);

  // Form data signals
  layoutName = signal('');
  layoutDescription = signal('');
  isCreating = signal(false);

  // Computed properties
  existingLayouts = signal<string[]>([]);

  constructor() {
    // Khởi tạo dữ liệu từ modalData nếu có
    if (this.modalData) {
      this.updateData(this.modalData);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): CreateLayoutModalResult {
    const name = this.layoutName().trim();
    const description = this.layoutDescription().trim();

    return {
      name,
      description: description || this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.DEFAULT_DESCRIPTION'),
      createdAt: new Date().toISOString(),
      isDefault: false
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    const name = this.layoutName().trim();
    const isNameValid = name.length >= 3 && name.length <= 50;
    const isNameUnique = !this.existingLayouts().includes(name);

    return isNameValid && isNameUnique;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: CreateLayoutModalData): void {
    console.log('CreateLayoutModalComponent.updateData() called with:', data);

    if (data.defaultName) {
      this.layoutName.set(data.defaultName);
    }

    if (data.defaultDescription) {
      this.layoutDescription.set(data.defaultDescription);
    }

    if (data.existingLayouts) {
      this.existingLayouts.set(data.existingLayouts);
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    console.log('CreateLayoutModalComponent modal opened');
    // Focus vào input đầu tiên
    setTimeout(() => {
      const firstInput = document.querySelector('input[name="layoutName"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng - xử lý validation và thông báo
   */
  onModalClose(): boolean {
    console.log('CreateLayoutModalComponent modal closing');

    // Nếu modal đóng với kết quả (nút Xác nhận), kiểm tra validation
    if (this.isValid()) {
      const name = this.layoutName().trim();

      // Kiểm tra tên layout không trùng
      if (this.existingLayouts().includes(name)) {
        this.flashMessageService.error(
          this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.LAYOUT_SELECTOR.ERRORS.NAME_EXISTS', { name })
        );
        return false; // Không cho phép đóng modal
      }

      // Hiển thị thông báo thành công
      this.flashMessageService.success(
        this.translateService.instant('DYNAMIC_LAYOUT_BUILDER.CREATE_LAYOUT_MODAL.SUCCESS_MESSAGE', { name })
      );
    }

    return true; // Cho phép đóng modal
  }

  // ===== Component Methods =====

  // ===== Legacy Methods (để tương thích với template cũ nếu cần) =====

  /**
   * Kiểm tra form có valid không (legacy method)
   * Với StrictModalComponent, sử dụng isValid() method
   */
  isFormValid(): boolean {
    return this.isValid();
  }
}
