import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu đầu vào của SimpleBottomSheetTestComponent
 */
export interface SimpleBottomSheetTestData {
  message?: string;
  testData?: any;
}

/**
 * Interface cho kết quả trả về từ SimpleBottomSheetTestComponent
 */
export interface SimpleBottomSheetTestResult {
  action: 'confirm' | 'cancel';
  data?: any;
}

/**
 * SimpleBottomSheetTestComponent - Component đơn giản để test StandardBottomSheetService
 *
 * Chức năng:
 * - Hiển thị message được truyền vào
 * - Test dynamic component loading
 * - Verify data passing functionality
 * - Implements StrictModalComponent interface để có type safety đầy đủ
 */
@Component({
  selector: 'app-simple-bottom-sheet-test',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="simple-bottom-sheet-test-content p-3">
      <h4>Simple Bottom Sheet Test Component</h4>
      <p *ngIf="message">{{ message }}</p>
      <p *ngIf="data">Data: {{ data | json }}</p>
      <div class="mt-3">
        <p class="text-muted">
          Đây là component được load động bởi StandardBottomSheetService.
          Component này nhận data từ config và hiển thị trong bottom sheet.
        </p>
        <div class="feature-list mt-2">
          <small class="d-block">✅ Dynamic component loading</small>
          <small class="d-block">✅ Data passing từ service</small>
          <small class="d-block">✅ Responsive design</small>
          <small class="d-block">✅ Enhanced features (title, close, actions)</small>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .simple-bottom-sheet-test-content {
      min-height: 150px;
    }

    h4 {
      color: #333;
      margin-bottom: 1rem;
      font-size: 1.1rem;
    }

    p {
      margin-bottom: 0.5rem;
    }

    .text-muted {
      font-size: 0.9rem;
      color: #6c757d;
    }

    .feature-list {
      background-color: #f8f9fa;
      padding: 0.75rem;
      border-radius: 0.375rem;
      border-left: 3px solid #28a745;
    }

    .feature-list small {
      color: #28a745;
      font-weight: 500;
    }
  `]
})
export class SimpleBottomSheetTestComponent implements StrictModalComponent<SimpleBottomSheetTestData, SimpleBottomSheetTestResult> {
  @Input() message?: string;
  @Input() data?: any;

  // Internal data từ modal service
  private modalData?: SimpleBottomSheetTestData;

  constructor() {
    console.log('SimpleBottomSheetTestComponent created');
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): SimpleBottomSheetTestResult {
    return {
      action: 'confirm',
      data: this.modalData || { message: this.message, data: this.data }
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return true; // Test component luôn valid
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: SimpleBottomSheetTestData): void {
    this.modalData = data;
    this.message = data.message;
    this.data = data.testData;
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    console.log('SimpleBottomSheetTestComponent modal opened');
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    console.log('SimpleBottomSheetTestComponent modal closing');
    return true; // Luôn cho phép đóng modal
  }
}
