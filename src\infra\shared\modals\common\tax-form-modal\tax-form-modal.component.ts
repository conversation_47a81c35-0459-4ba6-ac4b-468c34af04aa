import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { TaxInfo } from 'salehub_shared_contracts/entities/ims/inventory/goods_receipt';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho dữ liệu truyền vào modal
 */
export interface TaxFormModalData {
  tax?: TaxInfo; // Dữ liệu thuế nếu là chỉnh sửa
  subTotal?: number; // Tổng tiền hàng để tính thuế tự động
}

/**
 * Interface cho kết quả trả về từ modal
 */
export type TaxFormModalResult = TaxInfo | undefined;

/**
 * Modal form thêm/sửa thuế
 * Implements StrictModalComponent interface để có type safety đầy đủ
 */
@Component({
  selector: 'app-tax-form-modal',
  templateUrl: './tax-form-modal.component.html',
  styleUrls: ['./tax-form-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatTooltipModule,
    TranslateModule
  ]
})
export class TaxFormModalComponent implements OnInit, StrictModalComponent<TaxFormModalData, TaxFormModalResult> {
  /**
   * Form group để quản lý form
   */
  form: FormGroup;

  /**
   * Biến để hiển thị tiêu đề modal
   */
  dialogTitle: string;

  /**
   * Biến để theo dõi nếu user đã nhập số tiền thuế thủ công
   */
  manualTaxAmount = false;

  /**
   * Dữ liệu đầu vào cho modal
   */
  data: TaxFormModalData;

  constructor(
    private fb: FormBuilder,
    @Optional() private dialogRef?: MatDialogRef<TaxFormModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<TaxFormModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: TaxFormModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: TaxFormModalData
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || { tax: undefined, subTotal: 0 };

    // Xác định tiêu đề modal dựa vào dữ liệu đầu vào
    this.dialogTitle = this.data.tax ? 'TAX.EDIT_TITLE' : 'TAX.ADD_TITLE';

    // Khởi tạo form với các giá trị mặc định hoặc từ dữ liệu đầu vào
    this.form = this.fb.group({
      type: [this.data.tax?.type || 'VAT', Validators.required],
      rate: [this.data.tax?.rate || 0, [Validators.min(0), Validators.max(100)]],
      amount: [this.data.tax?.amount || 0, [Validators.required, Validators.min(0)]]
    });
  }

  ngOnInit(): void {
    // Đăng ký theo dõi sự thay đổi của tỷ lệ thuế để tính toán số tiền thuế tự động
    this.form.get('rate')?.valueChanges.subscribe(value => {
      if (!this.manualTaxAmount && this.data.subTotal) {
        this.updateTaxAmount();
      }
    });

    // Đánh dấu khi người dùng nhập số tiền thuế thủ công
    this.form.get('amount')?.valueChanges.subscribe(value => {
      if (value !== this.calculateTaxAmount()) {
        this.manualTaxAmount = true;
      }
    });
  }

  /**
   * Cập nhật số tiền thuế tự động dựa trên tỷ lệ thuế và tổng tiền hàng
   */
  updateTaxAmount(): void {
    const taxAmount = this.calculateTaxAmount();
    if (taxAmount !== null) {
      this.form.get('amount')?.setValue(taxAmount, { emitEvent: false });
      this.manualTaxAmount = false;
    }
  }

  /**
   * Tính toán số tiền thuế dựa trên tỷ lệ thuế và tổng tiền hàng
   * @returns Số tiền thuế hoặc null nếu không đủ dữ liệu để tính
   */
  calculateTaxAmount(): number | null {
    const taxRate = this.form.get('rate')?.value;

    if (taxRate === null || taxRate === undefined || !this.data.subTotal) {
      return null;
    }

    return Number(((this.data.subTotal * taxRate) / 100).toFixed(0));
  }

  /**
   * Xử lý khi người dùng nhấn Lưu, tạo đối tượng TaxInfo từ form
   */
  onSave(): void {
    if (this.form.invalid) {
      return;
    }

    const formValue = this.form.value;

    // Tạo đối tượng TaxInfo từ giá trị form
    const result: TaxInfo = {
      type: formValue.type,
      rate: formValue.rate || 0,
      amount: formValue.amount
    };

    // Đóng modal và trả về kết quả
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  /**
   * Xử lý khi người dùng nhấn Hủy
   */
  onCancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss();
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): TaxFormModalResult {
    if (!this.form.valid) {
      return undefined;
    }

    const formValue = this.form.value;
    const result: TaxInfo = {
      type: formValue.type,
      rate: formValue.rate || 0,
      amount: formValue.amount
    };

    return result;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.form.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: TaxFormModalData): void {
    this.data = data;

    // Cập nhật form với dữ liệu mới
    if (data.tax) {
      this.form.patchValue({
        type: data.tax.type || 'VAT',
        rate: data.tax.rate || 0,
        amount: data.tax.amount || 0
      });
    }

    // Cập nhật subTotal để tính toán lại thuế
    if (data.subTotal !== undefined) {
      this.updateTaxAmount();
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào field đầu tiên khi modal mở
    setTimeout(() => {
      const firstInput = document.querySelector('mat-select[formControlName="type"]') as HTMLElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Kiểm tra xem có thay đổi nào chưa được lưu không
    if (this.form.dirty && this.form.valid) {
      // Có thể thêm logic confirm nếu cần
      return true;
    }
    return true; // Luôn cho phép đóng modal
  }
}
