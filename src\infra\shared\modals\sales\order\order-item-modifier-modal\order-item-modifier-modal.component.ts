import { Component, Inject, OnInit, Optional, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { ModifierGroup, OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { ProductModifierGroup } from 'salehub_shared_contracts/requests/shared/product';

import { ProductModifierFormModalData } from './order-item-modifier-modal.service';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

@Component({
  selector: 'app-order-item-modifier-modal',
  templateUrl: './order-item-modifier-modal.component.html',
  styleUrls: ['./order-item-modifier-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatDialogModule,
    TranslateModule
  ]
})
export class OrderItemModifierModalComponent implements OnInit, StrictModalComponent<ProductModifierFormModalData, ModifierGroup[]> {
  // State signals
  activeFilter = signal<string | null>(null);
  selectedItems = signal<Map<string, number>>(new Map<string, number>());
  totalPrice = signal<number>(0);

  // Data từ inject
  productOptionList = signal<ProductModifierGroup[]>([]);
  orderItem = signal<OrderItemBaseDetails | null>(null);
  data: ProductModifierFormModalData;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: ProductModifierFormModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: ProductModifierFormModalData,
    @Optional() private dialogRef?: MatDialogRef<OrderItemModifierModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<OrderItemModifierModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || { list: [], data: {} as OrderItemBaseDetails };
  }

  ngOnInit(): void {
    // Khởi tạo dữ liệu từ data được inject
    this.productOptionList.set(this.data.list);
    this.orderItem.set(this.data.data);

    // Đặt filter đầu tiên làm mặc định nếu có
    if (this.productOptionList().length > 0) {
      this.activeFilter.set(this.productOptionList()[0].name);
    }

    // Khởi tạo selectedItems từ modifierGroups nếu đã có
    this.initSelectedItems();

    // Tính toán tổng tiền ban đầu
    this.calculateTotalPrice();
  }

  /**
   * Khởi tạo danh sách các item đã được chọn từ modifierGroups đã có
   */
  private initSelectedItems(): void {
    const itemsMap = new Map<string, number>();

    const modifierGroups = this.orderItem()?.modifierGroups;
    if (modifierGroups) {
      modifierGroups.forEach(group => {
        group.modifiers.forEach(modifier => {
          if (modifier.product?.productId) {
            itemsMap.set(modifier.product.productId, modifier.quantity);
          }
        });
      });
    }

    this.selectedItems.set(itemsMap);
  }

  /**
   * Tính toán tổng giá trị các item đã chọn
   */
  private calculateTotalPrice(): void {
    let total = 0;

    this.productOptionList().forEach(optionList => {
      optionList.items.forEach(item => {
        const quantity = this.selectedItems().get(item.productId) || 0;
        total += item.price * quantity;
      });
    });

    this.totalPrice.set(total);
  }

  /**
   * Xử lý khi nhấn vào một filter
   */
  setActiveFilter(filterName: string): void {
    this.activeFilter.set(filterName);
  }

  /**
   * Xử lý khi người dùng chọn một item
   */
  selectItem(itemId: string): void {
    const currentItems = new Map(this.selectedItems());
    if (!currentItems.has(itemId)) {
      currentItems.set(itemId, 1);
    }
    this.selectedItems.set(currentItems);
    this.calculateTotalPrice();
  }

  /**
   * Tăng số lượng của một item đã chọn
   */
  increaseItemQuantity(itemId: string): void {
    const currentItems = new Map(this.selectedItems());
    const currentQuantity = currentItems.get(itemId) || 0;
    currentItems.set(itemId, currentQuantity + 1);
    this.selectedItems.set(currentItems);
    this.calculateTotalPrice();
  }

  /**
   * Giảm số lượng của một item đã chọn
   */
  decreaseItemQuantity(itemId: string): void {
    const currentItems = new Map(this.selectedItems());
    const currentQuantity = currentItems.get(itemId) || 0;

    if (currentQuantity <= 1) {
      currentItems.delete(itemId);
    } else {
      currentItems.set(itemId, currentQuantity - 1);
    }

    this.selectedItems.set(currentItems);
    this.calculateTotalPrice();
  }

  /**
   * Kiểm tra xem một item có được chọn không
   */
  isItemSelected(itemId: string): boolean {
    return this.selectedItems().has(itemId);
  }

  /**
   * Lấy số lượng của một item đã chọn
   */
  getItemQuantity(itemId: string): number {
    return this.selectedItems().get(itemId) || 0;
  }

  /**
   * Reset lại toàn bộ lựa chọn
   */
  resetSelections(): void {
    this.selectedItems.set(new Map<string, number>());
    this.calculateTotalPrice();
  }

  /**
   * Đóng modal và trả về dữ liệu đã chọn
   */
  addToOrder(): void {
    // Tạo các modifier groups từ các items đã chọn
    const modifierGroups: ModifierGroup[] = [];

    // Nhóm các item theo filter
    this.productOptionList().forEach(optionList => {
      const modifiers = optionList.items
        .filter(item => this.selectedItems().has(item.productId))
        .map(item => {
          const quantity = this.selectedItems().get(item.productId) || 0;
          return {
            _id: item.productId,
            productId: item.productId,
            product: {
              _id: item.productId,
              name: item.name,
              price: item.price,
              cost: item.cost
            },
            quantity,
            price: item.price,
            subTotal: item.price * quantity,
            total: item.price * quantity,
            netTotal: item.price * quantity,
            discount: 0,
            discountRatio: 0,
            tax: 0
          };
        });

      if (modifiers.length > 0) {
        modifierGroups.push({
          _id: optionList.name,
          name: optionList.name,
          modifiers
        });
      }
    });

    // Đóng modal và trả về dữ liệu
    this.close(modifierGroups);
  }

  /**
   * Đóng modal không lưu gì
   */
  close(result?: any): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  /**
   * Format giá thành chuỗi có định dạng tiền tệ
   */
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      maximumFractionDigits: 0
    }).format(amount);
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): ModifierGroup[] {
    // Tạo các modifier groups từ các items đã chọn
    const modifierGroups: ModifierGroup[] = [];

    // Nhóm các item theo filter
    this.productOptionList().forEach(optionList => {
      const modifiers = optionList.items
        .filter(item => this.selectedItems().has(item.productId))
        .map(item => {
          const quantity = this.selectedItems().get(item.productId) || 0;
          return {
            _id: item.productId,
            productId: item.productId,
            product: {
              _id: item.productId,
              name: item.name,
              price: item.price,
              cost: item.cost
            },
            quantity,
            price: item.price,
            subTotal: item.price * quantity,
            total: item.price * quantity,
            netTotal: item.price * quantity,
            discount: 0,
            discountRatio: 0,
            tax: 0
          };
        });

      if (modifiers.length > 0) {
        modifierGroups.push({
          _id: optionList.name,
          name: optionList.name,
          modifiers
        });
      }
    });

    return modifierGroups;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // OrderItemModifierModal luôn valid vì có thể không chọn modifier nào
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: ProductModifierFormModalData): void {
    this.data = data;
    this.productOptionList.set(data.list);
    this.orderItem.set(data.data);

    // Đặt filter đầu tiên làm mặc định nếu có
    if (this.productOptionList().length > 0) {
      this.activeFilter.set(this.productOptionList()[0].name);
    }

    // Khởi tạo selectedItems từ modifierGroups nếu đã có
    this.initSelectedItems();

    // Tính toán tổng tiền ban đầu
    this.calculateTotalPrice();
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Kiểm tra dữ liệu bắt buộc
    if (!this.data.list || this.data.list.length === 0) {
      console.warn('OrderItemModifierModalComponent: list is empty');
    }
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
