import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ProductCategory } from '@mock/product_form';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

@Component({
  selector: 'app-category-form-modal',
  templateUrl: './category-form-modal.component.html',
  styleUrls: ['./category-form-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    ReactiveFormsModule
  ]
})
export class CategoryFormModalComponent implements OnInit, StrictModalComponent<{ category?: ProductCategory, parentCategories: ProductCategory[] }, ProductCategory> {
  categoryForm: FormGroup;
  dialogTitle = 'Thêm danh mục sản phẩm mới';
  data: { category?: ProductCategory, parentCategories: ProductCategory[] };

  constructor(
    private fb: FormBuilder,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: { category?: ProductCategory, parentCategories: ProductCategory[] },
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: { category?: ProductCategory, parentCategories: ProductCategory[] },
    @Optional() private dialogRef?: MatDialogRef<CategoryFormModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<CategoryFormModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || { parentCategories: [] };

    this.categoryForm = this.fb.group({
      _id: ['', []],
      name: ['', [Validators.required]],
      code: ['', [Validators.required]],
      parentId: ['', []],
      description: ['', []],
      isActive: [true, []]
    });
  }

  ngOnInit(): void {
    if (this.data && this.data.category) {
      this.dialogTitle = 'Chỉnh sửa danh mục sản phẩm';
      this.categoryForm.patchValue(this.data.category);
    }
  }

  onNoClick(): void {
    this.close();
  }

  close(result?: any): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  onSubmit(): void {
    if (this.categoryForm.valid) {
      const formValue = this.categoryForm.value;

      // Tạo ID mới nếu là thêm mới
      if (!formValue._id) {
        formValue._id = `cat_${Date.now().toString()}`;
      }

      this.close(formValue);
    } else {
      // Đánh dấu tất cả các trường là đã chạm để hiển thị lỗi
      Object.keys(this.categoryForm.controls).forEach(key => {
        const control = this.categoryForm.get(key);
        control?.markAsTouched();
      });
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): ProductCategory {
    if (!this.categoryForm.valid) {
      throw new Error('Category form is not valid');
    }

    const formValue = this.categoryForm.value;

    // Tạo ID mới nếu là thêm mới
    if (!formValue._id) {
      formValue._id = `cat_${Date.now().toString()}`;
    }

    // Thêm timestamps
    const now = new Date();
    return {
      ...formValue,
      createdAt: formValue.createdAt || now,
      updatedAt: now
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.categoryForm.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: { category?: ProductCategory, parentCategories: ProductCategory[] }): void {
    this.data = data;

    if (data.category) {
      this.dialogTitle = 'Chỉnh sửa danh mục sản phẩm';
      this.categoryForm.patchValue(data.category);
    } else {
      this.dialogTitle = 'Thêm danh mục sản phẩm mới';
      this.categoryForm.reset();
      this.categoryForm.patchValue({ isActive: true });
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào input đầu tiên khi modal mở
    setTimeout(() => {
      const firstInput = document.querySelector('input[formControlName="name"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
