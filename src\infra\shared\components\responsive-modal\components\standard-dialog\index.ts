// StandardDialog Module Exports
// Xuất tất cả components, services, interfaces và utilities

// Components
export { StandardDialogWrapperComponent } from './standard-dialog.component';
// Note: StandardDialogWrapperComponent là internal component, không nên sử dụng trực tiếp

// Service
export { StandardDialogService } from './standard-dialog.service';

// Interfaces
export type { StandardDialogConfig } from './models/standard-dialog-config.interface';
export type {
  StandardDialogResult
} from '../../models/view/standard-dialog-result.interface';
export {
  isConfirmResult,
  isCancelResult,
  isCloseResult,
  isCustomResult
} from '../../models/view/standard-dialog-result.interface';

// Modal Component Interfaces
export type {
  ModalComponent,
  ModalComponentWithData,
  ValidatableModalComponent,
  AdvancedModalComponent
} from '../../models/view/modal-component.interface';
export {
  isModalComponent,
  isModalComponentWithData,
  isValidatableModalComponent
} from '../../models/view/modal-component.interface';

// Re-export Angular Material Dialog types for convenience
export {
  MatDialogRef,
  MAT_DIALOG_DATA
} from '@angular/material/dialog';
