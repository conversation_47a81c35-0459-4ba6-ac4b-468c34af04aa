<!-- Checkbox Field Container -->
@if (isVisible()) {
  <div [class]="config.currentViewMode === 'form' ? 'field-row-form' : 'field-row-view'">

    <!-- Label Column -->
    <div class="field-label-column">
      <label class="field-label-text">
        {{ config.field.label }}
        @if (isFieldRequired()) {
          <span class="required-asterisk">*</span>
        }
      </label>

      @if (isReadOnlyState()) {
        <mat-icon
          class="read-only-icon"
          [matTooltip]="'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.READ_ONLY_TOOLTIP' | translate"
          matTooltipPosition="above">
          lock
        </mat-icon>
      }
    </div>

    <!-- Value Column -->
    <div class="field-value-column">

      <!-- PREVIEW MODE: Display toggle state -->
      @if (config.currentViewMode === 'preview') {
        <div class="field-view-value">
          <div class="field-toggle-display">
            <mat-icon class="toggle-icon" [class.enabled]="placeholderValue()" [class.disabled]="!placeholderValue()">
              {{ placeholderValue() ? 'toggle_on' : 'toggle_off' }}
            </mat-icon>
            <span class="toggle-label">
              {{ (config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.CHECKBOX') | translate }}
            </span>
          </div>
        </div>
      }

      <!-- VIEW MODE: Display actual values hoặc inline edit -->
      @else if (config.currentViewMode === 'view') {
        @if (!isFieldInEditMode()) {
          <!-- Normal view mode -->
          <div class="field-view-value">
            @if (currentValue() !== null && currentValue() !== undefined) {
              <div class="field-toggle-display">
                <mat-icon class="toggle-icon" [class.enabled]="getBooleanValue()" [class.disabled]="!getBooleanValue()">
                  {{ getBooleanValue() ? 'toggle_on' : 'toggle_off' }}
                </mat-icon>
                <span class="toggle-label">
                  {{ getBooleanValue() ? 'Có' : 'Không' }}
                </span>

                <!-- Edit Actions - chỉ hiển thị khi có quyền write -->
                @if (canEditField()) {
                  <div class="field-edit-actions">
                    <button type="button" class="btn btn-edit" (click)="startEditMode()">
                      <mat-icon>edit</mat-icon>
                      {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.EDIT' | translate }}
                    </button>
                  </div>
                }
              </div>
            } @else {
              <span class="empty">{{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_MESSAGES.EMPTY_VALUE' | translate }}</span>

              <!-- Edit Actions - chỉ hiển thị khi có quyền write -->
              @if (canEditField()) {
                <div class="field-edit-actions">
                  <button type="button" class="btn btn-edit" (click)="startEditMode()">
                    <mat-icon>edit</mat-icon>
                    {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.EDIT' | translate }}
                  </button>
                </div>
              }
            }
          </div>
        } @else {
          <!-- Inline edit mode -->
          <div class="field-inline-edit">
            <div class="field-input-container">
              <div class="toggle-form-container">
                <mat-slide-toggle
                  [formControl]="formControl()"
                  [disabled]="isReadOnly()"
                  class="field-toggle">
                  {{ (config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.CHECKBOX') | translate }}
                </mat-slide-toggle>
              </div>
            </div>

            <!-- Edit Actions -->
            <div class="field-edit-actions editing">
              <button type="button" class="btn btn-save" (click)="saveEditMode()">
                <mat-icon>save</mat-icon>
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.SAVE' | translate }}
              </button>
              <button type="button" class="btn btn-cancel" (click)="cancelEditMode()">
                <mat-icon>close</mat-icon>
                {{ 'DYNAMIC_LAYOUT_RENDERER.FIELD_ACTIONS.CANCEL' | translate }}
              </button>
            </div>
          </div>
        }
      }

      <!-- FORM MODE: Display toggle control -->
      @else {
        <div class="toggle-form-container">
          <mat-slide-toggle
            [formControl]="formControl()"
            [disabled]="isReadOnlyState()"
            class="field-toggle">
            {{ (config.field.placeholder || 'DYNAMIC_LAYOUT_RENDERER.FIELD_PLACEHOLDERS.CHECKBOX') | translate }}
          </mat-slide-toggle>
        </div>
      }

      <!-- Field-Level Validation Errors -->
      @if (shouldShowValidationErrors()) {
        <div class="field-validation-errors" aria-live="polite">
          @for (error of getValidationErrors(); track error) {
            <div class="validation-error-item">
              <mat-icon class="error-icon">error</mat-icon>
              <span class="error-message">{{ error | translate }}</span>
            </div>
          }
        </div>
      }

      <!-- Field Tooltip/Description -->
      @if (config.field.tooltip) {
        <div class="field-tooltip">
          <small class="text-muted">{{ config.field.tooltip }}</small>
        </div>
      }
    </div>
  </div>
}
