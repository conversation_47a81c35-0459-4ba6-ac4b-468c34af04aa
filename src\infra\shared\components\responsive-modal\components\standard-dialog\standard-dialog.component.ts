import {
  Component,
  Inject,
  TemplateRef,
  ChangeDetectionStrategy,
  computed,
  signal,
  ViewChild,
  ViewContainerRef,
  OnInit,
  ComponentRef,
  Type
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { FlashMessageService } from '@core/services/flash_message.service';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { StandardDialogConfig } from './models/standard-dialog-config.interface';
import { StandardDialogResult } from '../../models/view/standard-dialog-result.interface';
import {
  ModalComponent,
  isModalComponent,
  isModalComponentWithData,
  ValidatableModalComponent,
  isValidatableModalComponent,
  AdvancedModalComponent,
  StrictModalComponent,
  isStrictModalComponent
} from '../../models/view/modal-component.interface';

/**
 * StandardDialogWrapperComponent - Refactored wrapper component cho StandardDialogService
 *
 * Chức năng chính:
 * - Wrapper component đơn giản hóa theo đề xuất cải tiến
 * - Dynamic component loading không dùng ComponentPortal
 * - Tận dụng đầy đủ cơ chế MatDialog
 * - Component con implement ModalComponent interface
 * - Type-safe với TypeScript interfaces
 *
 * Cải tiến:
 * - Component con có thể truy cập MatDialogRef trực tiếp
 * - Không giới hạn format kết quả trả về
 * - Dễ mở rộng và customize
 * - Thống nhất với BottomSheet pattern
 */
@Component({
  selector: 'app-standard-dialog-wrapper',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './standard-dialog.component.html',
  styleUrls: ['./standard-dialog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StandardDialogWrapperComponent<T extends StrictModalComponent = StrictModalComponent> implements OnInit {
  @ViewChild('dynamicComponentContainer', { read: ViewContainerRef, static: true })
  dynamicComponentContainer!: ViewContainerRef;

  // Signals cho reactive state management
  private readonly _config = signal<StandardDialogConfig & { component?: Type<T> }>({});
  private componentRef: ComponentRef<T> | null = null;

  // Computed properties
  readonly title = computed(() => this._config().title);
  readonly enableClose = computed(() => this._config().enableClose ?? true);
  readonly useDefault = computed(() => this._config().actions?.useDefault ?? true);
  readonly customActionsTemplate = computed(() => this._config().actions?.customActionsTemplate);

  // Type checking helpers
  readonly isTitleTemplate = computed(() => this.title() instanceof TemplateRef);
  readonly isTitleString = computed(() => typeof this.title() === 'string');

  // Validation helper - với type constraint T extends StrictModalComponent
  readonly isComponentValid = computed(() => {
    if (!this.componentRef?.instance) {
      return true;
    }

    const instance = this.componentRef.instance;

    // Với type constraint T extends StrictModalComponent, instance luôn có isValid() method
    if (isStrictModalComponent(instance)) {
      const isValid = instance.isValid();
      return isValid;
    }

    // Fallback cho ValidatableModalComponent (backward compatibility)
    if (isValidatableModalComponent(instance)) {
      const isValid = (instance as ValidatableModalComponent).isValid();
      return isValid;
    }

    return true; // Mặc định là valid nếu không có validation
  });

  constructor(
    private readonly dialogRef: MatDialogRef<StandardDialogWrapperComponent<T>, T>,
    @Inject(MAT_DIALOG_DATA) public readonly data: StandardDialogConfig & { component?: Type<T> },
    private flashMessageService: FlashMessageService,
    private translateService: TranslateService
  ) {
    this._config.set(data);
  }

  ngOnInit(): void {
    if (this.data.component) {
      this.loadDynamicComponent();
    }
  }

  /**
   * Load dynamic component - đơn giản hóa logic
   */
  private loadDynamicComponent(): void {
    if (!this.data.component || !this.dynamicComponentContainer) {
      return;
    }

    this.dynamicComponentContainer.clear();
    this.componentRef = this.dynamicComponentContainer.createComponent(this.data.component);

    // Inject data vào component
    this.injectDataToComponent();

    // Inject MatDialogRef để component có thể tự đóng dialog
    this.injectDialogRefToComponent();

    // Gọi onModalOpen() nếu component implement AdvancedModalComponent
    this.callOnModalOpen();

    this.componentRef.changeDetectorRef.detectChanges();
  }

  /**
   * Inject data vào component con
   */
  private injectDataToComponent(): void {
    if (!this.componentRef?.instance) return;

    const componentData = this.data.data;
    if (!componentData) return;

    const instance = this.componentRef.instance as Record<string, unknown>;

    // Method 1: Gọi updateData() nếu có (ModalComponentWithData)
    if (isModalComponentWithData(instance)) {
      instance.updateData!(componentData);
      return;
    }

    // Method 2: Assign vào property data
    if ('data' in instance) {
      instance.data = componentData;
      return;
    }

    // Method 3: Assign từng property
    if (typeof componentData === 'object') {
      Object.assign(instance, componentData);
    }
  }

  /**
   * Inject MatDialogRef để component có thể tự đóng dialog
   */
  private injectDialogRefToComponent(): void {
    if (!this.componentRef?.instance) return;

    const instance = this.componentRef.instance as Record<string, unknown>;

    // Inject dialogRef nếu component có property này
    if ('dialogRef' in instance) {
      instance.dialogRef = this.dialogRef;
    }
  }

  /**
   * Gọi onModalOpen() nếu component implement AdvancedModalComponent
   */
  private callOnModalOpen(): void {
    if (!this.componentRef?.instance) return;

    const instance = this.componentRef.instance as Record<string, unknown>;

    // Gọi onModalOpen() nếu có
    if (typeof instance.onModalOpen === 'function') {
      instance.onModalOpen();
    }
  }

  /**
   * Gọi onModalClose() nếu component implement AdvancedModalComponent
   * @returns Promise<boolean> - true nếu có thể đóng, false nếu không
   */
  private async callOnModalClose(): Promise<boolean> {
    if (!this.componentRef?.instance) return true;

    const instance = this.componentRef.instance as any;

    // Gọi onModalClose() nếu có
    if (typeof instance.onModalClose === 'function') {
      const result = instance.onModalClose();

      // Xử lý cả sync và async result
      if (result instanceof Promise) {
        return await result;
      }

      return result !== false; // Mặc định là true nếu không return false
    }

    return true; // Mặc định cho phép đóng
  }

  /**
   * Lấy data từ component con để trả về
   */
  private getComponentData(): any {
    if (!this.componentRef?.instance) {
      return this.data.data;
    }

    const instance = this.componentRef.instance;

    // Method 1: Sử dụng ModalComponent interface
    if (isModalComponent(instance)) {
      return instance.getModalResult();
    }

    // Method 2: Lấy từ property data
    if (instance && typeof instance === 'object' && 'data' in instance) {
      return (instance as Record<string, any>).data;
    }

    // Fallback: Trả về data ban đầu
    return this.data.data;
  }

  /**
   * Xử lý sự kiện Confirm
   */
  async onConfirm(): Promise<void> {
    // Kiểm tra validation trước
    if (!this.isComponentValid()) {
      // Thay thế console.warn bằng FlashMessageService
      this.flashMessageService.warning(
        this.translateService.instant('FLASH_MESSAGES.WARNING.FORM.VALIDATION_FAILED')
      );
      return;
    }

    // Gọi onModalClose() nếu có để kiểm tra có thể đóng không
    const canClose = await this.callOnModalClose();
    if (!canClose) {
      // Xóa console.log debug không cần thiết - component tự xử lý thông báo
      return;
    }

    this.dialogRef.close(this.getComponentData());
  }

  /**
   * Xử lý sự kiện Cancel
   */
  onCancel(): void {
    this.dialogRef.close();
  }

  /**
   * Xử lý sự kiện Close
   */
  onClose(): void {
    this.dialogRef.close();
  }

  onCustomAction(actionName: string, payload?: any): void {
    this.dialogRef.close();
  }


  /**
   * Getter để template có thể truy cập TemplateRef type
   */
  get TemplateRef() {
    return TemplateRef;
  }
}
