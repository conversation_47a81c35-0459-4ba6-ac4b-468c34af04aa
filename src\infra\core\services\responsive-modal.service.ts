import { Injectable, Type, TemplateRef, ElementRef, ComponentRef, ViewContainerRef, ApplicationRef, Injector, createComponent, EmbeddedViewRef } from '@angular/core';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { MatDialogRef, MatDialogConfig } from '@angular/material/dialog';
import { MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { Popover } from 'primeng/popover';
import { ViewportService } from './viewport.service';
import { StandardDialogService, StandardDialogConfig } from '@shared/components/responsive-modal/components/standard-dialog';
import { StandardBottomSheetService, StandardBottomSheetConfig } from '@shared/components/responsive-modal/components/standard-bottom-sheet';
import { StandardOverlayModalService, StandardOverlayModalConfig } from '@shared/components/responsive-modal/components/standard-overlay-modal';
import { ConnectedOverlayModalService } from '@shared/components/responsive-modal/components/standard-overlay-modal/connected-overlay-modal.service';
import {
  StrictModalComponent
} from '@/shared/components/responsive-modal/models/view/modal-component.interface';
import { mergeDeep } from '@ngx-translate/core';
import { Subject, firstValueFrom } from 'rxjs';

/**
 * Interface cho legacy modal config với properties cũ
 */
export interface LegacyModalConfig<TData = unknown> {
  title?: string | TemplateRef<unknown>;
  dialogTitle?: string;
  enableClose?: boolean;
  useDefault?: boolean;
  customActionsTemplate?: TemplateRef<unknown>;
  actions?: {
    useDefault?: boolean;
    customActionsTemplate?: TemplateRef<unknown>;
  };
  data?: TData;
  /**
   * chỉ nhận panelClass được truyền vào, loại bỏ panelClass mặc định
   */
  disableDefaultPanelClass?: boolean;
  panelClass?: string | string[];
  width?: string;
  height?: string;
  maxWidth?: string;
  maxHeight?: string;
  minWidth?: string;
  minHeight?: string;
  disableClose?: boolean;
  hasBackdrop?: boolean;
  backdropClass?: string | string[];
  position?: {
    top?: string;
    bottom?: string;
    left?: string;
    right?: string;
  };
  direction?: 'ltr' | 'rtl';
  autoFocus?: boolean;
  restoreFocus?: boolean;
  closeOnNavigation?: boolean;


  /**
   * vị trí modal overlay khi nhấn vào con trỏ chuột
   * - 'center': Modal center tại vị trí con trỏ chuột (legacy)
   * - 'left': Modal bắt đầu từ vị trí con trỏ chuột (left edge = mouse X, legacy)
   * - 'right': Modal kết thúc tại vị trí con trỏ chuột (right edge = mouse X, legacy)
   * - 'top-left': Modal hiển thị phía trên bên trái vị trí con trỏ chuột
   * - 'top-center': Modal hiển thị phía trên và center theo vị trí con trỏ chuột
   * - 'top-right': Modal hiển thị phía trên bên phải vị trí con trỏ chuột
   * - 'bottom-left': Modal hiển thị phía dưới bên trái vị trí con trỏ chuột
   * - 'bottom-center': Modal hiển thị phía dưới và center theo vị trí con trỏ chuột (default)
   * - 'bottom-right': Modal hiển thị phía dưới bên phải vị trí con trỏ chuột
   * @default 'bottom-center'
   */
  overlayModalPosition?: 'center' | 'left' | 'right' | 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
}

/**
 * Threads-like animation configuration cho mat-menu modals
 */
export interface ThreadsAnimationConfig {
  /**
   * Enable/disable Threads-like animation
   * @default true
   */
  enabled?: boolean;

  /**
   * Loại animation: 'basic' (chỉ scale) hoặc 'enhanced' (scale + blur)
   * @default 'basic'
   */
  type?: 'basic' | 'enhanced';

  /**
   * Custom CSS classes để override default animation
   */
  customClasses?: {
    container?: string;
    menu?: string;
  };
}

/**
 * Interface cho responsive modal config kết hợp StandardDialogConfig và StandardBottomSheetConfig
 */
export interface ResponsiveModalConfig<TData = unknown> extends LegacyModalConfig<TData> {
  // Từ StandardDialogConfig - support both string and TemplateRef
  title?: string | TemplateRef<unknown>;
  enableClose?: boolean;
  actions?: {
    useDefault?: boolean;
    customActionsTemplate?: TemplateRef<unknown>;
  };

  // Common properties
  data?: TData;

  /**
   * Threads-like animation configuration cho mat-menu modals
   * Chỉ áp dụng khi sử dụng mat-menu mode (openWithMatMenu, openWithPopover)
   */
  threadsAnimation?: ThreadsAnimationConfig;
}

/**
 * ResponsiveModalService - Unified modal service với auto-selection
 *
 * Chức năng chính:
 * - Tự động chọn StandardDialog (desktop) hoặc StandardBottomSheet (mobile) dựa trên breakpoint
 * - Maintain API tương tự MatDialog để dễ migration
 * - Sử dụng StandardDialogService và StandardBottomSheetService với enhanced features
 * - Backward compatibility với existing code
 *
 * Sử dụng:
 * - Thay thế ResponsiveModalService cũ mà không cần thay đổi code
 * - API tương tự nhưng với enhanced features từ Standard services
 */
@Injectable({
  providedIn: 'root'
})
export class ResponsiveModalService {
  // Track active popover để implement toggle behavior
  private activePopover: {
    popoverRef: ComponentRef<Popover>;
    dynamicComponentRef: ComponentRef<any>;
    triggerElement: HTMLElement;
    resolve: (value: any) => void;
    isClosingProgrammatically?: boolean; // Flag để phân biệt user close vs programmatic close
  } | null = null;
  private activeOverlays = new Map<string, {
    overlayRef: OverlayRef;
    componentRef: ComponentRef<any>;
    closeSubject: Subject<any>;
  }>();

  constructor(
    private standardDialogService: StandardDialogService,
    private standardBottomSheetService: StandardBottomSheetService,
    private standardOverlayModalService: StandardOverlayModalService,
    private connectedOverlayModalService: ConnectedOverlayModalService,
    private viewportService: ViewportService,
    private appRef: ApplicationRef,
    private injector: Injector
  ) {}

  // ===================================================================
  // THREADS-LIKE ANIMATION HELPER METHODS
  // ===================================================================

  /**
   * Apply Threads-like animation classes to container element
   * @param element - Container element để apply animation classes
   * @param config - Threads animation configuration
   */
  private applyThreadsAnimation(element: HTMLElement, config?: ThreadsAnimationConfig): void {
    if (!element || !config?.enabled) {
      return;
    }

    // Remove existing animation classes
    this.removeThreadsAnimation(element);

    // Apply animation classes based on type
    const animationType = config.type || 'basic';

    if (config.customClasses?.container) {
      // Use custom container class
      element.classList.add(config.customClasses.container);
    } else {
      // Use default classes
      if (animationType === 'enhanced') {
        element.classList.add('apply-threads-menu-enhanced');
      } else {
        element.classList.add('apply-threads-menu-basic');
      }
    }

    console.log(`🎭 Applied Threads animation (${animationType}) to container:`, element);
  }

  /**
   * Remove Threads-like animation classes from container element
   * @param element - Container element để remove animation classes
   */
  private removeThreadsAnimation(element: HTMLElement): void {
    if (!element) {
      return;
    }

    // Remove all possible animation classes
    const classesToRemove = [
      'apply-threads-menu-basic',
      'apply-threads-menu-enhanced',
      'threads-menu-backdrop-scale',
      'threads-menu-backdrop-enhanced',
      'mat-menu-open',
      'mat-menu-closed'
    ];

    classesToRemove.forEach(className => {
      element.classList.remove(className);
    });
  }

  /**
   * Apply Threads-like animation classes to mat-menu panel
   * @param menuPanel - Mat-menu panel element
   * @param config - Threads animation configuration
   */
  private applyThreadsMenuAnimation(menuPanel: HTMLElement, config?: ThreadsAnimationConfig): void {
    if (!menuPanel || !config?.enabled) {
      return;
    }

    if (config.customClasses?.menu) {
      // Use custom menu class
      menuPanel.classList.add(config.customClasses.menu);
    } else {
      // Use default menu animation class
      menuPanel.classList.add('threads-menu-slide-up');
    }

    console.log(`🎭 Applied Threads menu animation to panel:`, menuPanel);
  }

  /**
   * Find container element for applying Threads animation
   * @param triggerElement - Trigger element để tìm container
   * @returns Container element hoặc null nếu không tìm thấy
   */
  private findAnimationContainer(triggerElement: HTMLElement): HTMLElement | null {
    // Tìm container element theo thứ tự ưu tiên
    let container: HTMLElement | null = null;

    // 1. Tìm element có class threads animation
    container = triggerElement.closest('.apply-threads-menu-basic, .apply-threads-menu-enhanced') as HTMLElement;
    if (container) {
      return container;
    }

    // 2. Tìm parent element có thể apply animation
    container = triggerElement.closest('.card, .modal-content, .dialog-content, .container') as HTMLElement;
    if (container) {
      return container;
    }

    // 3. Fallback to parent element
    container = triggerElement.parentElement;
    if (container) {
      return container;
    }

    // 4. Last resort: use body
    return document.body;
  }

  /**
   * Get default Threads animation config
   * @returns Default ThreadsAnimationConfig
   */
  private getDefaultThreadsConfig(): ThreadsAnimationConfig {
    return {
      enabled: true,
      type: 'basic'
    };
  }



  /**
   * Mở modal với auto-selection giữa dialog và bottom sheet
   * Type Safety: Component phải implement StrictModalComponent interface
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param modalConfig - Cấu hình modal (StandardDialogConfig hoặc StandardBottomSheetConfig)
   * @param forceMode - Force sử dụng dialog hoặc bottom-sheet (optional)
   * @returns Promise<TResult | undefined> - Kết quả khi modal đóng
   */
  async open<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    modalConfig: ResponsiveModalConfig<TData> = {},
    forceMode?: 'dialog' | 'bottom-sheet'
  ): Promise<TResult | undefined> {
    // Runtime validation: Kiểm tra component có implement StrictModalComponent interface không
    // try {
    //   // Cố gắng tạo instance với empty constructor trước
    //   let tempInstance: any;
    //   try {
    //     tempInstance = new component();
    //   } catch (constructorError) {
    //     // Nếu constructor có dependencies, tạo instance với undefined values
    //     tempInstance = Object.create(component.prototype);
    //   }

    //   if (!isStrictModalComponent(tempInstance)) {
    //     console.warn(
    //       `ResponsiveModalService: Component ${component.name} không implement StrictModalComponent interface.`,
    //       'Recommended: Implement getModalResult(), isValid(), updateData?(), onModalOpen?(), onModalClose?() methods để có type safety đầy đủ.'
    //     );
    //   }
    // } catch (error) {
    //   // Ignore validation errors, component vẫn có thể hoạt động
    //   console.debug(`ResponsiveModalService: Skipped validation for component ${component.name} (có dependencies injection)`);
    // }


    const useBottomSheet =
      forceMode === 'bottom-sheet' || (forceMode !== 'dialog' && this.viewportService.isMobile());


    if (useBottomSheet) {
      // Mở bottom sheet với StandardBottomSheetService
      return this.standardBottomSheetService.openAsync<TData, TResult, TComponent>(
        component, 
        this.buildModalConfig('bottom-sheet', modalConfig)  as StandardBottomSheetConfig<TData>
      );
    } else {
      // Mở dialog với StandardDialogService
      return this.standardDialogService.openAsync<TData, TResult, TComponent>(
        component, 
        this.buildModalConfig('dialog', modalConfig) as StandardDialogConfig<TData>
      );
    }
  }

  /**
   * Mở modal với PrimeNG Popover thực sự trên desktop và bottom-sheet trên mobile
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param event - MouseEvent để xác định vị trí hiển thị overlay
   * @param modalConfig - Cấu hình modal (ResponsiveModalConfig)
   * @param forceMode - Force sử dụng dialog hoặc bottom-sheet (optional)
   * @returns Promise<TResult | undefined> - Kết quả khi modal đóng
   */
  async openWithPopover<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    event: MouseEvent,
    modalConfig: ResponsiveModalConfig<TData> = {},
    forceMode?: 'dialog' | 'bottom-sheet'
  ): Promise<TResult | undefined> {
    // Set default modalConfig nếu không có
    modalConfig = modalConfig || {};

    // Check viewport size thay vì platform detection để support responsive testing
    const currentViewport = this.viewportService.getCurrentViewport();
    const isMobileViewport = currentViewport && currentViewport.displaySize.windowWidth < 768; // Bootstrap md breakpoint

    const useBottomSheet =
      forceMode === 'bottom-sheet' || (forceMode !== 'dialog' && isMobileViewport);

    if (useBottomSheet) {
      // Mobile: Sử dụng StandardBottomSheetService (đã tự động apply Threads animation)
      const bottomSheetConfig = this.buildModalConfig('bottom-sheet', modalConfig) as StandardBottomSheetConfig<TData>;

      // StandardBottomSheetService đã tự động apply Threads animation
      return this.standardBottomSheetService.openAsync<TData, TResult, TComponent>(
        component,
        bottomSheetConfig
      );
    } else {
      // Desktop: Apply Threads animation và sử dụng PrimeNG Popover thực sự
      const threadsConfig = modalConfig.threadsAnimation || this.getDefaultThreadsConfig();

      // Find trigger element from event
      const triggerElement = event.target as HTMLElement;
      if (triggerElement && threadsConfig.enabled) {
        const container = this.findAnimationContainer(triggerElement);
        if (container) {
          this.applyThreadsAnimation(container, threadsConfig);
          container.classList.add('mat-menu-open');
          console.log(`🎭 Applied Threads animation to popover container:`, container);
        }
      }

      return this.openWithPrimeNGPopover<TData, TResult, TComponent>(
        component,
        event,
        modalConfig
      );
    }
  }

  /**
   * Private method để handle PrimeNG Popover implementation
   * Sử dụng PrimeNG Popover component thực sự với improved UX và toggle behavior
   */
  private async openWithPrimeNGPopover<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    event: MouseEvent,
    modalConfig: ResponsiveModalConfig<TData>
  ): Promise<TResult | undefined> {
    const triggerElement = (event.currentTarget as HTMLElement) || (event.target as HTMLElement);

    if (!triggerElement) {
      // Fallback về StandardOverlayModalService nếu không có trigger element
      const result$ = this.standardOverlayModalService.open<TComponent, TData, TResult>(
        component,
        event,
        this.buildModalConfig('overlay', modalConfig) as StandardOverlayModalConfig<TData>
      );
      return firstValueFrom(result$);
    }

    // Toggle behavior: Nếu click vào cùng trigger element, đóng popover hiện tại
    if (this.activePopover && this.activePopover.triggerElement === triggerElement) {
      this.activePopover.isClosingProgrammatically = true;
      this.activePopover.popoverRef.instance.hide();
      return Promise.resolve(undefined);
    }

    // Đóng popover hiện tại nếu có (khi click vào trigger element khác)
    if (this.activePopover) {
      // Set flag để biết đây là programmatic close
      this.activePopover.isClosingProgrammatically = true;

      // Trigger hide để cleanup
      this.activePopover.popoverRef.instance.hide();

      // Đợi cleanup hoàn tất bằng cách listen onHide event
      return new Promise<TResult | undefined>((resolve, reject) => {
        // Override resolve function để tạo modal mới sau cleanup
        this.activePopover!.resolve = (_value: any) => {
          // Cleanup đã hoàn tất, bây giờ tạo modal mới
          this.createNewPopover(component, event, modalConfig)
            .then((result) => resolve(result as TResult | undefined))
            .catch((error) => reject(error));
        };
      });
    }

    // Nếu không có popover hiện tại, tạo mới trực tiếp
    return this.createNewPopover(component, event, modalConfig);
  }

  /**
   * Private method để tạo popover mới
   */
  private createNewPopover<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    event: MouseEvent,
    modalConfig: ResponsiveModalConfig<TData>,
    externalResolve?: (value: TResult | undefined) => void,
    externalReject?: (reason?: any) => void
  ): Promise<TResult | undefined> {
    const triggerElement = (event.currentTarget as HTMLElement) || (event.target as HTMLElement);

    return new Promise<TResult | undefined>((resolve, reject) => {
      try {
        // Tạo dynamic component trước để có content sẵn sàng
        const dynamicComponentRef = createComponent(component, {
          environmentInjector: this.appRef.injector,
          elementInjector: this.injector
        });

        // Set component data
        if (dynamicComponentRef.instance && modalConfig.data) {
          Object.assign(dynamicComponentRef.instance, modalConfig.data);
        }

        // Attach dynamic component to DOM để render
        this.appRef.attachView(dynamicComponentRef.hostView);

        // Tạo PrimeNG Popover component
        const popoverRef = createComponent(Popover, {
          environmentInjector: this.appRef.injector,
          elementInjector: this.injector
        });

        // Configure popover properties cho smooth animation
        const popoverInstance = popoverRef.instance;
        popoverInstance.dismissable = modalConfig.enableClose ?? true;
        popoverInstance.autoZIndex = true;
        popoverInstance.baseZIndex = 1000;
        popoverInstance.focusOnShow = true;
        popoverInstance.showTransitionOptions = '200ms cubic-bezier(0.25, 0.8, 0.25, 1)'; // Material Design easing
        popoverInstance.hideTransitionOptions = '150ms cubic-bezier(0.4, 0.0, 0.2, 1)'; // Material Design easing
        popoverInstance.appendTo = 'body'; // Đảm bảo popover được append vào body

        // Attach popover to DOM
        this.appRef.attachView(popoverRef.hostView);
        document.body.appendChild(popoverRef.location.nativeElement);

        // Listen for popover show event để render content ngay khi popover xuất hiện
        popoverInstance.onShow.subscribe(() => {
          // Tìm popover content element và inject component
          setTimeout(() => {
            const popoverElements = document.querySelectorAll('.p-popover');
            const latestPopover = popoverElements[popoverElements.length - 1];

            if (latestPopover) {
              const popoverContent = latestPopover.querySelector('.p-popover-content');
              if (popoverContent && dynamicComponentRef.location.nativeElement) {
                // Clear existing content và add component
                popoverContent.innerHTML = '';
                popoverContent.appendChild(dynamicComponentRef.location.nativeElement);
                console.log('Component rendered in popover on show event');
              }
            }
          }, 0); // Sử dụng 0ms để render ngay trong next tick
        });

        // Track active popover cho toggle behavior
        this.activePopover = {
          popoverRef,
          dynamicComponentRef,
          triggerElement,
          resolve
        };

        // Handle component result - StrictModalComponent interface
        if (dynamicComponentRef.instance) {
          const instance = dynamicComponentRef.instance as any;

          // Subscribe to result Subject if exists
          if (instance.result && typeof instance.result.subscribe === 'function') {
            instance.result.subscribe((result: TResult) => {
              popoverInstance.hide();
              if (externalResolve) {
                externalResolve(result);
              } else {
                resolve(result);
              }
            });
          }

          // Set up default result for testing (chỉ khi cần thiết)
          if (modalConfig.data && (modalConfig.data as any).autoConfirm) {
            setTimeout(() => {
              if (instance.result && typeof instance.result.next === 'function') {
                instance.result.next({ action: 'confirm', data: modalConfig.data } as any);
              }
            }, 1000);
          }
        }

        // Listen for popover hide events
        popoverInstance.onHide.subscribe(() => {
          const wasClosingProgrammatically = this.activePopover?.isClosingProgrammatically || false;
          const overrideResolve = this.activePopover?.resolve;

          this.cleanupActivePopover();

          // Nếu là programmatic close và có override resolve, gọi override function
          if (wasClosingProgrammatically && overrideResolve) {
            overrideResolve(undefined);
          }
          // Nếu là user close, resolve bình thường
          else if (!wasClosingProgrammatically) {
            if (externalResolve) {
              externalResolve(undefined);
            } else {
              resolve(undefined);
            }
          }
        });

        // Show popover với content đã sẵn sàng
        popoverInstance.show(event, triggerElement);
        console.log('PrimeNG Popover shown with pre-rendered content');

      } catch (error) {
        console.error('Error in createNewPopover:', error);
        this.cleanupActivePopover();
        if (externalReject) {
          externalReject(error);
        } else {
          reject(error);
        }
      }
    });
  }

  /**
   * Cleanup active popover và reset state
   */
  private cleanupActivePopover(): void {
    if (!this.activePopover) {
      return;
    }

    try {
      const { popoverRef, dynamicComponentRef } = this.activePopover;

      // Cleanup dynamic component
      if (dynamicComponentRef) {
        this.appRef.detachView(dynamicComponentRef.hostView);
        dynamicComponentRef.destroy();
      }

      // Cleanup popover
      if (popoverRef) {
        this.appRef.detachView(popoverRef.hostView);
        popoverRef.destroy();
      }

      // Cleanup Threads animation if applied
      if (this.activePopover.triggerElement) {
        const container = this.findAnimationContainer(this.activePopover.triggerElement);
        if (container) {
          // Remove mat-menu-open và add mat-menu-closed for closing animation
          container.classList.remove('mat-menu-open');
          container.classList.add('mat-menu-closed');

          // Remove animation classes sau khi animation hoàn thành
          setTimeout(() => {
            this.removeThreadsAnimation(container);
          }, 250); // Match với animation duration

          console.log(`🎭 Cleaned up Threads animation from popover container:`, container);
        }
      }

      // Reset active popover state
      this.activePopover = null;
      console.log('Active popover cleaned up successfully');

    } catch (error) {
      console.error('Error cleaning up active popover:', error);
      // Đảm bảo reset state ngay cả khi có lỗi
      this.activePopover = null;
    }
  }

  /**
   * Mở modal với Angular Material mat-menu API thực sự trên desktop và bottom-sheet trên mobile
   *
   * @param component - Component class phải extends StrictModalComponent<TData, TResult>
   * @param triggerElement - Element hoặc ElementRef để position modal (như mat-menu)
   * @param config - Cấu hình modal (MatDialogConfig compatible)
   * @param data - Dữ liệu truyền vào component
   * @returns MatDialogRef hoặc MatBottomSheetRef tùy theo breakpoint
   */
  openWithMatMenu<
    T extends StrictModalComponent<D, R>,
    D = any,
    R = any
  >(
    component: Type<T>,
    triggerElement: ElementRef | HTMLElement,
    config?: MatDialogConfig<D> & { threadsAnimation?: ThreadsAnimationConfig },
    data?: D
  ): MatDialogRef<T, R> | MatBottomSheetRef<T, R> {
    // Validate triggerElement
    if (!triggerElement) {
      throw new Error('ResponsiveModalService.openWithMatMenu: triggerElement is required');
    }

    // Extract HTMLElement từ ElementRef nếu cần
    const element = triggerElement instanceof ElementRef
      ? triggerElement.nativeElement
      : triggerElement;

    if (!element || !element.getBoundingClientRect) {
      throw new Error('ResponsiveModalService.openWithMatMenu: triggerElement must be a valid HTMLElement or ElementRef');
    }

    // Detect desktop vs mobile sử dụng ViewportService
    const viewport = this.viewportService.getCurrentViewport();
    const isDesktop = viewport.isDesktop;

    // Get Threads animation config
    const threadsConfig = config?.threadsAnimation || this.getDefaultThreadsConfig();

    if (isDesktop) {
      // Desktop: Apply Threads animation và sử dụng Angular Material MatMenu API
      const container = this.findAnimationContainer(element);
      if (container && threadsConfig.enabled) {
        this.applyThreadsAnimation(container, threadsConfig);

        // Add mat-menu-open class for animation trigger
        container.classList.add('mat-menu-open');

        console.log(`🎭 Applied Threads animation to mat-menu container:`, container);
      }

      return this.createMatMenuWithComponent<T, D, R>(component, element, config, data, threadsConfig);
    } else {
      // Mobile: Sử dụng StandardBottomSheetService (đã tự động apply Threads animation)
      const finalData = data !== undefined ? data : (config?.data !== null ? config?.data : undefined);

      const bottomSheetConfig: StandardBottomSheetConfig<D> = {
        ...(finalData !== undefined && { data: finalData }),
        hasBackdrop: config?.hasBackdrop !== false,
        disableClose: config?.disableClose || false,
        panelClass: ['responsive-mat-menu-bottom-sheet', ...(Array.isArray(config?.panelClass) ? config.panelClass : config?.panelClass ? [config.panelClass] : [])]
      };

      // StandardBottomSheetService đã tự động apply Threads animation
      const resultPromise = this.standardBottomSheetService.openAsync(component as any, bottomSheetConfig);

      return {
        afterDismissed: () => {
          return new Promise(resolve => {
            resultPromise.then(resolve).catch(() => resolve(undefined));
          });
        },
        dismiss: (_result?: R) => {
          this.standardBottomSheetService.dismiss();
        }
      } as any;
    }
  }

  /**
   * Tạo mat-menu-like modal sử dụng StandardOverlayModalService với mat-menu styling
   * Approach này đơn giản hơn và tận dụng existing overlay infrastructure
   */
  private createMatMenuWithComponent<
    T extends StrictModalComponent<D, R>,
    D = any,
    R = any
  >(
    component: Type<T>,
    triggerElement: HTMLElement,
    config?: MatDialogConfig<D>,
    data?: D,
    threadsConfig?: ThreadsAnimationConfig
  ): MatDialogRef<T, R> {
    // Tạo fake MouseEvent từ trigger element position
    const rect = triggerElement.getBoundingClientRect();
    const fakeEvent = new MouseEvent('click', {
      clientX: rect.left,
      clientY: rect.bottom + 4 // 4px offset giống mat-menu
    });

    // Cấu hình overlay với mat-menu styling
    const overlayConfig: StandardOverlayModalConfig<D> = {
      positioning: {
        overlayModalPosition: 'bottom-left', // Giống mat-menu default position
        dimensions: {
          maxWidth: config?.maxWidth as string || '280px',
          maxHeight: config?.maxHeight as string || '400px',
          minWidth: '112px' // Mat-menu minimum width
        }
      },
      data: data !== undefined ? data : (config?.data !== null ? config?.data : undefined),
      disableClose: config?.disableClose || false, // StandardOverlayModalConfig sử dụng disableClose thay vì hasBackdrop
      panelClass: [
        'mat-menu-panel', // Sử dụng mat-menu panel class
        'mat-elevation-z4', // Mat-menu elevation
        'responsive-mat-menu-overlay',
        // Add Threads animation classes for menu panel
        ...(threadsConfig?.enabled ? ['threads-menu-slide-up'] : []),
        ...(threadsConfig?.customClasses?.menu ? [threadsConfig.customClasses.menu] : []),
        ...(Array.isArray(config?.panelClass) ? config.panelClass : config?.panelClass ? [config.panelClass] : [])
      ],
      backdropClass: 'cdk-overlay-transparent-backdrop' // Transparent backdrop giống mat-menu
    };

    // Sử dụng StandardOverlayModalService
    const result$ = this.standardOverlayModalService.open<T, D, R>(
      component,
      fakeEvent,
      overlayConfig
    );

    // Tạo MatDialogRef-like object để maintain compatibility
    const dialogRef = {
      afterClosed: () => {
        // Cleanup Threads animation khi modal đóng
        result$.subscribe({
          complete: () => {
            const container = this.findAnimationContainer(triggerElement);
            if (container && threadsConfig?.enabled) {
              // Remove mat-menu-open và add mat-menu-closed for closing animation
              container.classList.remove('mat-menu-open');
              container.classList.add('mat-menu-closed');

              // Remove animation classes sau khi animation hoàn thành
              setTimeout(() => {
                this.removeThreadsAnimation(container);
              }, 250); // Match với animation duration

              console.log(`🎭 Cleaned up Threads animation from mat-menu container:`, container);
            }
          }
        });
        return result$;
      },
      close: (result?: R) => {
        // Trigger close với result
        // StandardOverlayModalService sẽ handle việc đóng overlay
        if (result !== undefined) {
          // Emit result through the observable
          result$.subscribe(); // This will trigger the close
        }
      }
    } as MatDialogRef<T, R>;

    return dialogRef;
  }

  /**
   * Implementation của openWithOverlay với overloads
   */
  async openWithOverlay<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    event: MouseEvent,
    modalConfig: ResponsiveModalConfig<TData> = {},
    forceMode?: 'dialog' | 'bottom-sheet'
  ): Promise<TResult | undefined> {
    // Set default modalConfig nếu không có
    modalConfig = modalConfig || {};

    const useBottomSheet =
      forceMode === 'bottom-sheet' || (forceMode !== 'dialog' && this.viewportService.isMobile());


    if (useBottomSheet) {
      return this.standardBottomSheetService.openAsync<TData, TResult, TComponent>(
        component, 
        this.buildModalConfig('bottom-sheet', modalConfig) as StandardBottomSheetConfig<TData>
      );
    } else {
      return this.openWithCdkOverlayAtPosition<TData, TResult, TComponent>(component, event, modalConfig);
    }
  }


  /**
   * Mở modal với CDK Overlay tại vị trí element (connected overlay pattern)
   *
   * @param component - Component class
   * @param event - MouseEvent để extract connected element
   * @param modalConfig - Cấu hình modal
   * @returns Promise<TResult | undefined>
   */
  private async openWithCdkOverlayAtPosition<
    TData = unknown,
    TResult = unknown,
    TComponent extends StrictModalComponent<TData, TResult> = StrictModalComponent<TData, TResult>
  >(
    component: Type<TComponent>,
    event: MouseEvent,
    modalConfig: ResponsiveModalConfig<TData> = {}
  ): Promise<TResult | undefined> {
    // Extract connected element từ MouseEvent
    // Ưu tiên currentTarget (element có event listener), fallback về target
    const connectedElement = (event.currentTarget as HTMLElement) || (event.target as HTMLElement);

    if (!connectedElement) {
      // Fallback về StandardOverlayModalService nếu không có connected element
      const result$ = this.standardOverlayModalService.open<TComponent, TData, TResult>(
        component,
        event,
        this.buildModalConfig('overlay', modalConfig) as StandardOverlayModalConfig<TData>
      );
      return firstValueFrom(result$);
    }

    // Sử dụng ConnectedOverlayModalService để modal hoạt động giống mat-menu
    const result$ = this.connectedOverlayModalService.openWithConnectedElement<TComponent, TData, TResult>(
      component,
      connectedElement,
      this.buildModalConfig('overlay', modalConfig) as StandardOverlayModalConfig<TData>,
      event // Truyền MouseEvent để sử dụng mouse position
    );

    // Convert Observable to Promise để maintain backward compatibility
    return firstValueFrom(result$);
  }

  private buildModalConfig<TData = unknown>(type: 'dialog' | 'bottom-sheet' | 'overlay', modalConfig: ResponsiveModalConfig<TData>) {
    const defaultConfig: LegacyModalConfig<TData> = {
      enableClose: true,
      disableClose: false,
      hasBackdrop: true,
      actions: {
        useDefault: true
      },
    };

    const getPanelClass = (defaultPanelClass: string) => {
      if(!modalConfig.disableDefaultPanelClass) {
        if(Array.isArray(modalConfig.panelClass)) {
          return [
            ...modalConfig.panelClass,
            defaultPanelClass
          ]
        } else if(modalConfig.panelClass !== defaultPanelClass) {
          return [
            modalConfig.panelClass as string, 
            defaultPanelClass
          ];
        }
      }

      return modalConfig.panelClass;
    }

    switch (type) {
      case 'dialog':
        modalConfig.panelClass = getPanelClass('responsive-dialog');
        return mergeDeep(defaultConfig, modalConfig);

      case 'bottom-sheet':
        modalConfig.panelClass = getPanelClass('responsive-bottom-sheet');
        return mergeDeep(defaultConfig, modalConfig);

      case 'overlay':
        const overlayConfig: StandardOverlayModalConfig<TData> = {
          positioning: {
            overlayModalPosition: modalConfig.overlayModalPosition || 'bottom-center',
            dimensions: {
              width: this.parseSize(modalConfig.width),
              height: this.parseSize(modalConfig.height),
              maxWidth: modalConfig.maxWidth,
              maxHeight: modalConfig.maxHeight,
              minWidth: modalConfig.minWidth,
              minHeight: modalConfig.minHeight
            }
          }
        };
        modalConfig.panelClass = getPanelClass('responsive-overlay-modal');

        return mergeDeep(
          mergeDeep(defaultConfig, modalConfig),
          overlayConfig
        );
    }
  }

  /**
   * Đóng overlay theo ID
   */
  private closeOverlayById<TResult>(overlayId: string, result: TResult | undefined): void {
    const overlayData = this.activeOverlays.get(overlayId);
    if (overlayData) {
      this.closeOverlay(overlayData.overlayRef, overlayData.componentRef, overlayData.closeSubject, result);
      this.activeOverlays.delete(overlayId);
    }
  }

  /**
   * Đóng overlay và cleanup
   */
  private closeOverlay<TResult>(
    overlayRef: OverlayRef,
    componentRef: ComponentRef<any>,
    closeSubject: Subject<TResult | undefined>,
    result: TResult | undefined
  ): void {
    // Gọi onModalClose nếu có
    if (typeof componentRef.instance.onModalClose === 'function') {
      componentRef.instance.onModalClose();
    }

    // Emit result
    closeSubject.next(result);
    closeSubject.complete();

    // Dispose overlay
    overlayRef.dispose();
  }

  /**
   * Public method để component có thể đóng overlay
   */
  public closeModal<TResult>(result?: TResult): void {
    // Đóng overlay gần nhất (last in, first out)
    const overlayIds = Array.from(this.activeOverlays.keys());
    if (overlayIds.length > 0) {
      const lastOverlayId = overlayIds[overlayIds.length - 1];
      this.closeOverlayById(lastOverlayId, result);
    }
  }


  /**
   * Kiểm tra xem có modal nào đang mở không
   * Backward compatibility method
   *
   * @returns boolean - true nếu có modal đang mở
   */
  hasOpenModals(): boolean {
    return this.standardDialogService.hasOpenDialogs();
  }

  /**
   * Đóng tất cả modals đang mở
   * Backward compatibility method
   */
  closeAll(): void {
    this.standardDialogService.closeAll();
    this.standardBottomSheetService.dismiss();
  }



  /**
   * Helper method để parse size string thành number (px only)
   * @param size - Size string hoặc number
   * @returns number | undefined
   */
  private parseSize(size: string | number | undefined): number | undefined {
    if (typeof size === 'number') return size;
    if (typeof size === 'string' && size.endsWith('px')) {
      return parseInt(size.replace('px', ''), 10);
    }
    return undefined;
  }
}
