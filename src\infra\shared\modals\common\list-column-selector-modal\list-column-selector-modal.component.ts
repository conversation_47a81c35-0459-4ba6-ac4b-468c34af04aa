import { Component, EventEmitter, Input, Output, signal, ChangeDetectionStrategy, ChangeDetectorRef, OnInit, ViewEncapsulation, Optional, Inject, ElementRef, Renderer2 } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Column, ListColumnConfig } from '../../../models/view/list-layout.model';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatCardModule } from '@angular/material/card';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { TranslateModule } from '@ngx-translate/core';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Kiểu dữ liệu trả về từ modal
 */
export type ListColumnSelectorModalResult = {
  regularColumns: Column[];
  pinnedColumns: Column[];
};

@Component({
  selector: 'app-list-column-selector',
  standalone: true,
  imports: [
    CommonModule,
    DragDropModule,
    MatCheckboxModule,
    MatCardModule,
    MatListModule,
    MatIconModule,
    MatButtonModule,
    MatTooltipModule,
    MatDialogModule,
    TranslateModule
  ],
  templateUrl: './list-column-selector-modal.component.html',
  styleUrls: ['./list-column-selector-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class ListColumnSelectorModalComponent implements OnInit, StrictModalComponent<ListColumnConfig, ListColumnSelectorModalResult> {
  data!: ListColumnConfig;

  allColumns = signal<Column[]>([]);
  pinnedColumns = signal<Column[]>([]);
  regularColumns = signal<Column[]>([]);

  selectedColumns = signal<string[]>([]);
  private maxPinnedColumns: number = 3;

  /**
   * Method để update data từ bên ngoài (được gọi bởi StandardDialogWrapperComponent)
   */
  updateData(newData: ListColumnConfig): void {
    this.data = newData;
    this.initializeData();
  }

  constructor(
    private elementRef: ElementRef,
    private renderer: Renderer2,
    @Optional() private dialogRef?: MatDialogRef<ListColumnSelectorModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<ListColumnSelectorModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: ListColumnConfig,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: ListColumnConfig
  ) {
    // Lấy data từ dialog hoặc bottom sheet
    this.data = (this.dialogData || this.bottomSheetData) as ListColumnConfig;
  }

  ngOnInit(): void {
    // Khởi tạo data sau khi component được khởi tạo
    this.initializeData();

    // Kiểm tra lại sau một tick để đảm bảo data đã được assign từ StandardDialogWrapperComponent
    setTimeout(() => {
      this.initializeData();
    }, 0);
  }

  /**
   * Khởi tạo signals với data hiện tại
   */
  private initializeData(): void {
    // Kiểm tra và khởi tạo data mặc định nếu cần
    if (!this.data) {
      this.data = {
        allColumns: [],
        pinnedColumns: [],
        regularColumns: [],
        maxPinnedColumns: 3
      };
    }

    // Chỉ khởi tạo signals nếu có data thực sự
    if (this.data.allColumns && this.data.allColumns.length > 0) {
      this.selectedColumns.set([
        ...((this.data.regularColumns || []).map(v => v._id)),
        ...((this.data.pinnedColumns || []).map(v => v._id)),
      ]);
      this.allColumns.set([...(this.data.allColumns || [])]);
      this.pinnedColumns.set([...(this.data.pinnedColumns || [])]);
      this.regularColumns.set([...(this.data.regularColumns || [])]);
      this.maxPinnedColumns = this.data.maxPinnedColumns || 3;
    }
  }


  toggleColumn(column: Column) {
    const selected = [...this.selectedColumns()];

    if (selected.includes(column._id)) {
      this.selectedColumns.set(selected.filter(col => col !== column._id));

      if (this.hasColumnItemInArray(this.pinnedColumns(), column)) {
        this.pinnedColumns.set(this.pinnedColumns().filter(col => col._id !== column._id));
      } else {
        this.regularColumns.set(this.regularColumns().filter(col => col._id !== column._id));
      }
    } else {
      this.selectedColumns.set([...selected, column._id]);

      if (!(this.hasColumnItemInArray(this.regularColumns(), column))) {
        this.regularColumns.set([...this.regularColumns(), column]);
      }
    }
  }

  hasColumnItemInArray(arr: Column[], item: Column) {
    return arr.some(obj => obj._id === item._id)
  }

  togglePin(column: Column) {
    const pinned = [...this.pinnedColumns()];
    const regular = [...this.regularColumns()];

    if (this.hasColumnItemInArray(pinned, column)) {
      // Bỏ pin: chuyển từ pinned sang regular
      this.pinnedColumns.set(pinned.filter(col => col._id !== column._id));
      this.regularColumns.set([...regular, column]);
      this.addSuccessAnimation(column._id, 'unpin');
    } else {
      // Kiểm tra giới hạn maxPinnedColumns
      if (this.maxPinnedColumns !== undefined && pinned.length >= this.maxPinnedColumns) {
        console.warn(`Cannot pin column "${column.label}". Maximum pinned columns (${this.maxPinnedColumns}) reached.`);
        this.addErrorAnimation(column._id);
        return;
      }
      // Pin: chuyển từ regular sang pinned
      if (!this.selectedColumns().includes(column._id)) {
        this.selectedColumns.set([...this.selectedColumns(), column._id]);
      }
      this.pinnedColumns.set([...pinned, column]);
      this.regularColumns.set(regular.filter(col => col._id !== column._id));
      this.addSuccessAnimation(column._id, 'pin');
    }
  }

  drop(event: CdkDragDrop<string[]>) {
    const previousContainer = event.previousContainer;
    const currentContainer = event.container;

    if (previousContainer === currentContainer) {
      // Kéo thả trong cùng danh sách
      const items = currentContainer.id === 'pinnedList' ? [...this.pinnedColumns()] : [...this.regularColumns()];
      moveItemInArray(items, event.previousIndex, event.currentIndex);
      if (currentContainer.id === 'pinnedList') {
        this.pinnedColumns.set(items);
      } else {
        this.regularColumns.set(items);
      }
      // Thêm hiệu ứng thành công cho việc sắp xếp lại
      const movedItem = items[event.currentIndex];
      this.addSuccessAnimation(movedItem._id, 'reorder');
    } else {
      // Kiểm tra giới hạn maxPinnedColumns khi kéo sang pinnedList
      if (currentContainer.id === 'pinnedList' && this.maxPinnedColumns !== undefined && this.pinnedColumns().length >= this.maxPinnedColumns) {
        console.warn(`Cannot move column to Fixed Columns. Maximum pinned columns (${this.maxPinnedColumns}) reached.`);
        // Thêm hiệu ứng lỗi
        this.addErrorShakeAnimation(currentContainer.element.nativeElement);
        return;
      }

      const previousItems = previousContainer.id === 'pinnedList' ? [...this.pinnedColumns()] : [...this.regularColumns()];
      const currentItems = currentContainer.id === 'pinnedList' ? [...this.pinnedColumns()] : [...this.regularColumns()];

      transferArrayItem(
        previousItems,
        currentItems,
        event.previousIndex,
        event.currentIndex
      );

      if (previousContainer.id === 'pinnedList') {
        this.pinnedColumns.set(previousItems);
        this.regularColumns.set(currentItems);
        const movedItem = currentItems[event.currentIndex];
        if (this.pinnedColumns().includes(movedItem)) {
          this.pinnedColumns.set(this.pinnedColumns().filter(col => col !== movedItem));
        }
        this.addSuccessAnimation(movedItem._id, 'unpin');
      } else {
        this.regularColumns.set(previousItems);
        this.pinnedColumns.set(currentItems);
        const movedItem = currentItems[event.currentIndex];
        if (!this.pinnedColumns().includes(movedItem)) {
          this.pinnedColumns.set([...this.pinnedColumns(), movedItem]);
        }
        this.addSuccessAnimation(movedItem._id, 'pin');
      }
    }
  }


  trackByColumn(_index: number, col: Column): string {
    return col._id;
  }


  /**
   * Xử lý khi nhấn nút Hủy
   * Khi sử dụng StandardDialogWrapperComponent, không tự đóng modal
   */
  onCancel(): void {
    // Không làm gì - để StandardDialogWrapperComponent xử lý
  }

  /**
   * Xử lý khi nhấn nút Xác nhận
   * Khi sử dụng StandardDialogWrapperComponent, không tự đóng modal
   */
  onConfirm(): void {
    // Không làm gì - để StandardDialogWrapperComponent xử lý
  }

  /**
   * Method để StandardDialogWrapperComponent lấy data khi user click Xác nhận
   * Được gọi bởi StandardDialogWrapperComponent.dialogData()
   */
  getModalResult(): ListColumnConfig {
    return {
      allColumns: this.data.allColumns,
      maxPinnedColumns: this.data.maxPinnedColumns,
      regularColumns: this.regularColumns(),
      pinnedColumns: this.pinnedColumns()
    };
  }

  /**
   * Method để validate dữ liệu trước khi đóng modal
   * Được gọi bởi StandardDialogWrapperComponent để kiểm tra trước khi confirm
   */
  isValid(): boolean {
    // Kiểm tra ít nhất phải có 1 cột được chọn
    const totalSelectedColumns = this.regularColumns().length + this.pinnedColumns().length;

    if (totalSelectedColumns === 0) {
      console.warn('ListColumnSelectorModal: Phải chọn ít nhất 1 cột để hiển thị');
      return false;
    }

    // Kiểm tra không vượt quá giới hạn pinned columns
    if (this.pinnedColumns().length > this.maxPinnedColumns) {
      console.warn(`ListColumnSelectorModal: Số cột ghim (${this.pinnedColumns().length}) vượt quá giới hạn (${this.maxPinnedColumns})`);
      return false;
    }

    return true;
  }

  /**
   * Method được gọi khi modal được mở
   * Optional method từ AdvancedModalComponent interface
   */
  onModalOpen?(): void {
    // Có thể thêm logic khởi tạo bổ sung ở đây
  }

  /**
   * Method được gọi trước khi modal đóng
   * Optional method từ AdvancedModalComponent interface
   * @returns true để cho phép đóng, false để ngăn chặn
   */
  onModalClose?(): boolean | Promise<boolean> {
    // Kiểm tra xem có thay đổi nào chưa được lưu không
    const hasChanges = this.hasUnsavedChanges();

    if (hasChanges) {
      console.log('ListColumnSelectorModal: Có thay đổi chưa được lưu');
      // Có thể hiển thị confirm dialog ở đây
      // Hiện tại cho phép đóng
    }

    return true;
  }

  /**
   * Kiểm tra xem có thay đổi nào chưa được lưu không
   */
  private hasUnsavedChanges(): boolean {
    // So sánh với dữ liệu ban đầu
    const originalRegularIds = (this.data.regularColumns || []).map(col => col._id).sort();
    const originalPinnedIds = (this.data.pinnedColumns || []).map(col => col._id).sort();

    const currentRegularIds = this.regularColumns().map(col => col._id).sort();
    const currentPinnedIds = this.pinnedColumns().map(col => col._id).sort();

    const regularChanged = JSON.stringify(originalRegularIds) !== JSON.stringify(currentRegularIds);
    const pinnedChanged = JSON.stringify(originalPinnedIds) !== JSON.stringify(currentPinnedIds);

    return regularChanged || pinnedChanged;
  }

  /**
   * Thêm hiệu ứng thành công cho item
   */
  private addSuccessAnimation(columnId: string, action: 'pin' | 'unpin' | 'reorder'): void {
    setTimeout(() => {
      const element = this.elementRef.nativeElement.querySelector(`[data-column-id="${columnId}"]`);
      if (element) {
        this.renderer.addClass(element, 'cdk-drag-drop-success');

        // Thêm class tương ứng với action
        switch (action) {
          case 'pin':
            this.renderer.addClass(element, 'success-pin');
            break;
          case 'unpin':
            this.renderer.addClass(element, 'success-unpin');
            break;
          case 'reorder':
            this.renderer.addClass(element, 'success-reorder');
            break;
        }

        // Xóa class sau khi animation hoàn thành
        setTimeout(() => {
          this.renderer.removeClass(element, 'cdk-drag-drop-success');
          this.renderer.removeClass(element, `success-${action}`);
        }, 600);
      }
    }, 50);
  }

  /**
   * Thêm hiệu ứng lỗi cho item
   */
  private addErrorAnimation(columnId: string): void {
    setTimeout(() => {
      const element = this.elementRef.nativeElement.querySelector(`[data-column-id="${columnId}"]`);
      if (element) {
        this.renderer.addClass(element, 'error-shake');

        // Xóa class sau khi animation hoàn thành
        setTimeout(() => {
          this.renderer.removeClass(element, 'error-shake');
        }, 500);
      }
    }, 50);
  }

  /**
   * Thêm hiệu ứng lắc cho container khi có lỗi
   */
  private addErrorShakeAnimation(containerElement: HTMLElement): void {
    this.renderer.addClass(containerElement, 'error-container-shake');

    // Xóa class sau khi animation hoàn thành
    setTimeout(() => {
      this.renderer.removeClass(containerElement, 'error-container-shake');
    }, 500);
  }

  /**
   * Đóng modal (fallback cho trường hợp không dùng StandardDialogWrapperComponent)
   */
  private close(isCancel: boolean): void {
    let result: ListColumnSelectorModalResult | null = null;
    if(!isCancel) {
      result = {
        regularColumns: this.regularColumns(),
        pinnedColumns: this.pinnedColumns()
      }
    }
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
