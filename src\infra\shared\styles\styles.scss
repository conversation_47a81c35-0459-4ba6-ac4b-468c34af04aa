// @forward './fonts/google-sans.scss';
@forward './fonts/metropolis.scss';

// @forward './fonts/keen-icon-duotone.scss';
@forward './fonts/keen-icon-solid.scss';
@forward './fonts/keen-icons-outline.scss';
@forward './fonts/keen-icons-filled.scss';

// @forward './fonts/bootstrap-icons.scss';
// @forward './fonts/line-awesome.scss';
@forward './fonts/fontawesome.scss';
@forward './fonts/material-icons.scss';

// @forward '../../node_modules/bootstrap/dist/css/bootstrap-reboot.min.css';
@forward '../../../../node_modules/bootstrap/dist/css/bootstrap.min.css';
@forward './component/bootstrap-expand.scss';

// @forward '../../node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css';
@forward '../../../../node_modules/ngx-toastr/toastr.css';

@use './component/animation.scss';
@use './component/metronic.scss';
@use './component/base.scss';
@use './component/toast.scss';
@use './component/swiper.scss';
@use './component/button.scss';

@use './angular/index.scss';
@use './directive/resize-panel.scss';
@use './directive/overlay-spinner.scss';

// Connected Overlay Modal styles
@use '../components/responsive-modal/components/standard-overlay-modal/connected-overlay-modal.scss';

// PrimeNG Popover-like styling for ResponsiveModalService.openWithPopover
.responsive-primeng-popover-overlay {
  // Base PrimeNG Popover styling
  background: var(--p-popover-background, #ffffff);
  border: 1px solid var(--p-popover-border-color, #dee2e6);
  border-radius: var(--p-popover-border-radius, 6px);
  box-shadow: var(--p-popover-shadow, 0 2px 12px 0 rgba(0, 0, 0, 0.1));
  color: var(--p-popover-color, #495057);
  padding: var(--p-popover-content-padding, 1rem);

  // Animation
  animation: p-popover-enter 150ms ease-out;
  transform-origin: top;

  // Ensure proper z-index
  z-index: 1100;

  // Remove default overlay modal styling
  .standard-overlay-modal-content {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
  }

  // Style the wrapper to look like PrimeNG Popover content
  .standard-overlay-modal-wrapper {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0;
  }

  // Header styling
  .standard-overlay-modal-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--p-popover-border-color, #dee2e6);
    background: transparent;

    .standard-overlay-modal-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--p-popover-color, #495057);
      margin: 0;
    }

    .standard-overlay-modal-close-button {
      color: var(--p-text-muted-color, #6c757d);

      &:hover {
        color: var(--p-text-color, #495057);
      }
    }
  }

  // Body styling
  .standard-overlay-modal-body {
    padding: 1rem;
    background: transparent;
  }

  // Footer styling
  .standard-overlay-modal-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--p-popover-border-color, #dee2e6);
    background: transparent;

    .standard-overlay-modal-actions {
      gap: 0.5rem;

      button {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
      }
    }
  }
}

// Animation keyframes for PrimeNG Popover-like entrance
@keyframes p-popover-enter {
  0% {
    opacity: 0;
    transform: scaleY(0.8);
  }
  100% {
    opacity: 1;
    transform: scaleY(1);
  }
}

// Dark theme support
.p-dark .responsive-primeng-popover-overlay {
  background: var(--p-surface-800, #2d3748);
  border-color: var(--p-surface-700, #4a5568);
  color: var(--p-surface-100, #f7fafc);

  .standard-overlay-modal-header {
    border-bottom-color: var(--p-surface-700, #4a5568);

    .standard-overlay-modal-title {
      color: var(--p-surface-100, #f7fafc);
    }
  }

  .standard-overlay-modal-footer {
    border-top-color: var(--p-surface-700, #4a5568);
  }
}


