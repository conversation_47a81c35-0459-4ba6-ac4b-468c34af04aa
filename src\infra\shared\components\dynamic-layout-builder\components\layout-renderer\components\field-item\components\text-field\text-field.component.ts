import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

import { AbstractFieldComponent } from '../base/abstract-field.component';
import { FieldComponentInterface } from '../base/field-component.interface';
import { FieldItemConfig } from '../../../../../../models/dynamic-layout-renderer.model';
import { FieldValue, TextFieldTypes } from '@domain/entities/field.entity';
import { getFieldIcon, getFieldIconColor, generatePlaceHolderData } from '../../../../../../utils/field.utils';

/**
 * Component chuyên biệt xử lý các text-based fields
 * 
 * Supported field types:
 * - text: Văn bản thông thường
 * - email: Email với validation
 * - phone: Số điện thoại
 * - url: URL với validation
 * - search: Tìm kiếm
 * 
 * Features:
 * - View mode: Hiển thị mock data
 * - Form mode: Input controls với validation
 * - Permission-based visibility và read-only state
 * - i18n support cho placeholders
 * - Responsive design
 */
@Component({
  selector: 'app-text-field',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './text-field.component.html',
  styleUrls: ['./text-field.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TextFieldComponent extends AbstractFieldComponent implements FieldComponentInterface {

  /**
   * Configuration object chứa tất cả dữ liệu cần thiết
   * Tuân thủ yêu cầu: component chỉ nhận 1 input object
   */
  @Input({ required: true }) config!: FieldItemConfig;


  // ===== IMPLEMENT ABSTRACT METHODS =====

  /**
   * Validate rằng field type được hỗ trợ
   */
  protected validateFieldType(): void {
    const supportedTypes: TextFieldTypes[] = ['text', 'email', 'phone', 'url', 'search'];
    if (!supportedTypes.includes(this.config.field.type as TextFieldTypes)) {
      console.error(`TextFieldComponent: Unsupported field type '${this.config.field.type}'`);
    }
  }

  /**
   * Generate placeholder value dựa trên field type
   */
  protected override generatePlaceholderValue(): void {
    const fieldType = this.config.field.type;
    this.placeholderValue.set(generatePlaceHolderData(fieldType, this.translateService));
  }

  /**
   * Lấy validators phù hợp cho field type
   * UPDATED: Sử dụng FieldValidationService tập trung
   */
  protected override getValidators(): any[] {
    return this.getAllValidators();
  }

  // ===== COMPONENT-SPECIFIC METHODS =====

  /**
   * Lấy HTML input type phù hợp
   */
  getInputType(): string {
    switch (this.config.field.type) {
      case 'email':
        return 'email';
      case 'phone':
        return 'tel';
      case 'url':
        return 'url';
      case 'search':
        return 'search';
      default:
        return 'text';
    }
  }

  /**
   * Kiểm tra xem giá trị có phải là URL hợp lệ không
   */
  isUrl(value: string): boolean {
    if (!value) return false;

    try {
      const url = new URL(value);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch {
      // Fallback: kiểm tra pattern đơn giản
      const urlPattern = /^https?:\/\/.+/i;
      return urlPattern.test(value);
    }
  }

  /**
   * Convert FieldValue thành string để sử dụng trong template
   */
  toStringValue(value: FieldValue): string {
    return String(value || '');
  }
}
