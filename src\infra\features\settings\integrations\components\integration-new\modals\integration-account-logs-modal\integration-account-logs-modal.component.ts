import { Component, signal, computed, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { TranslateModule } from '@ngx-translate/core';

import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';
import { 
  IntegrationAccountLogsModalData, 
  IntegrationAccountLogsModalResult 
} from './integration-account-logs-modal.interfaces';
import { IntegrationAccountLog } from '@/mock/settings/integration_view_account.mock';

/**
 * Modal component để xem nhật ký hoạt động tài khoản tích hợp
 * Chỉ chứa phần Logs từ modal gốc
 */
@Component({
  selector: 'app-integration-account-logs-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatCardModule,
    MatDividerModule,
    ScrollingModule,
    TranslateModule
  ],
  templateUrl: './integration-account-logs-modal.component.html',
  styleUrls: ['./integration-account-logs-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IntegrationAccountLogsModalComponent implements StrictModalComponent<IntegrationAccountLogsModalData, IntegrationAccountLogsModalResult> {
  
  // Dữ liệu đầu vào từ modal service
  data = signal<IntegrationAccountLogsModalData | null>(null);
  
  // Computed properties
  platformName = computed(() => this.data()?.platformName || '');
  accountName = computed(() => this.data()?.accountName || '');
  logs = computed(() => this.data()?.logs || []);
  
  // Computed cho title modal
  modalTitle = computed(() => {
    const platform = this.platformName();
    const account = this.accountName();
    if (account) {
      return `${platform} - ${account}`;
    }
    return platform;
  });
  
  constructor() {}
  
  /**
   * Lấy CSS class cho log entry dựa trên type
   * @param log Log entry
   * @returns CSS class string
   */
  getLogTypeClass(log: IntegrationAccountLog): string {
    switch (log.type) {
      case 'success':
        return 'text-success';
      case 'error':
        return 'text-danger';
      case 'warning':
        return 'text-warning';
      case 'info':
      default:
        return 'text-info';
    }
  }
  
  /**
   * Format thời gian log theo định dạng [HH:mm:ss DD/MM/YYYY]
   * @param date Date object
   * @returns Formatted time string
   */
  formatLogTime(date: Date): string {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `[${hours}:${minutes}:${seconds} ${day}/${month}/${year}]`;
  }
  
  // ===== StrictModalComponent Interface Implementation =====
  
  /**
   * Cập nhật dữ liệu cho modal
   */
  updateData(data: IntegrationAccountLogsModalData): void {
    this.data.set(data);
  }
  
  /**
   * Trả về kết quả từ modal component
   */
  getModalResult(): IntegrationAccountLogsModalResult {
    return {
      action: 'close'
    };
  }
  
  /**
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return true; // Modal này luôn valid
  }
  
  /**
   * Method được gọi khi modal được mở
   */
  onModalOpen?(): void {
    console.log('Integration Account Logs Modal opened');
  }
  
  /**
   * Method được gọi trước khi modal đóng
   */
  onModalClose?(): boolean | Promise<boolean> {
    console.log('Integration Account Logs Modal closing');
    return true;
  }
}
