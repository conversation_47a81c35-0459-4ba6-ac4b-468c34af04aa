import { TemplateRef } from '@angular/core';
import { MatBottomSheetConfig } from '@angular/material/bottom-sheet';
import { BaseStandardModalConfig } from '../../../../../models/view/base-standard-modal-config.interface';

/**
 * C<PERSON>u hình cho StandardBottomSheetService
 * Extends MatBottomSheetConfig và BaseStandardModalConfig với các enhanced features
 * Định nghĩa các tùy chọn để tùy chỉnh bottom sheet behavior và appearance
 */
export interface StandardBottomSheetConfig<D = any> extends
  Omit<MatBottomSheetConfig<D>, 'data'>,
  BaseStandardModalConfig {

  /**
   * Dữ liệu được truyền vào component được load động
   * Thay thế cho MatBottomSheetConfig.data để tránh conflict
   */
  data?: D;

  /**
   * Dữ liệu bổ sung cho component (alias cho data)
   * <PERSON><PERSON> maintain backward compatibility
   */
  componentData?: D;

  // Các properties width, height, disableClose, panelClass, backdropClass
  // đã được inherit từ MatBottomSheetConfig
}
