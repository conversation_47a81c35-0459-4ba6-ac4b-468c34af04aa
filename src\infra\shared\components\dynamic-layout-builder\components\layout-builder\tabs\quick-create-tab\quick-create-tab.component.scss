// Quick Create Tab Styles - Optimized with Mobile Support
.quick-create-tab-container {
  display: flex;
  height: 100%;
  background: #f8f9fa;

  // Responsive design cho mobile
  @media (max-width: 768px) {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  // Left Panel - Field Types Sidebar
  .fields-sidebar {
    width: 350px;
    min-width: 350px;
    background: white;
    border-right: 1px solid #e9ecef;
    overflow-y: auto;

    // Responsive design cho mobile
    @media (max-width: 768px) {
      width: 100%;
      min-width: unset;
      border-right: none;
      border-bottom: 1px solid #e9ecef;
      max-height: 40vh;
      order: 2; // Đặt sau main content trên mobile
    }

    .sidebar-header {
      border-bottom: 1px solid #e9ecef;
      padding-bottom: 1rem;

      h5 {
        color: #495057;
        font-weight: 600;
      }
    }

    // Field Type Items - Reuse Create Tab Styling
    .field-type-item {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      margin-bottom: 0.5rem;
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      cursor: move;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
        border-color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background-color: #f8f9fa;
        border-color: #dee2e6;
        pointer-events: none; // Prevent any interaction

        .field-icon {
          color: #6c757d !important;
        }

        .field-label {
          color: #6c757d !important;
          text-decoration: line-through;
        }

        &:hover {
          transform: none;
          box-shadow: none;
          border-color: #dee2e6;
        }
      }

      .used-indicator {
        color: #28a745;
        font-size: 18px;
        margin-left: auto;
        animation: fadeIn 0.3s ease-in-out;
      }

      .field-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        margin-right: 0.75rem;
        color: #2196f3;

        // Icon colors được set thông qua inline style từ TypeScript
      }

      .field-info {
        flex: 1;

        .field-label {
          font-weight: 500;
          color: #495057;
          font-size: 0.875rem;
        }
      }
    }

    // Section Groups
    .section-group {
      margin-bottom: 1.5rem;

      .section-header {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        margin-bottom: 0.75rem;

        .section-icon {
          font-size: 18px;
          margin-right: 0.5rem;
          color: #6c757d;
        }

        .section-title {
          flex: 1;
          font-weight: 600;
          color: #495057;
          font-size: 0.9rem;
        }

        .section-count {
          font-size: 0.8rem;
          color: #6c757d;
        }
      }
    }

    // Info Panel
    .info-panel {
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 6px;
      padding: 1rem;
      margin-top: 1rem;

      .info-header {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;

        .info-icon {
          font-size: 18px;
          margin-right: 0.5rem;
          color: #1976d2;
        }

        h6 {
          color: #1976d2;
          font-weight: 600;
          margin: 0;
        }
      }

      p {
        color: #1565c0;
        font-size: 0.8rem;
        margin: 0;
      }
    }
  }

  // Right Panel - Main Layout Area
  .layout-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // Responsive design cho mobile
    @media (max-width: 768px) {
      order: 1; // Đặt trước left panel trên mobile
      overflow: visible;
      min-height: 60vh;
    }

    // Add Field Section - Mobile Friendly
    .add-field-section {
      padding: 0 1rem;

      .add-field-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover:not(:disabled) {
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }

        // Responsive design
        @media (max-width: 768px) {
          padding: 1rem;
          font-size: 1rem;

          mat-icon {
            font-size: 24px;
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    // Quick Create Field List
    .quick-create-field-list {
      flex: 1;
      padding: 1rem;

      .fields-list-container {
        min-height: 250px;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 1rem;
        background: white;

        .field-list-item {
          display: flex;
          align-items: center;
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          background: white;
          border: 1px solid #e9ecef;
          border-radius: 6px;

          .drag-handle {
            margin-right: 0.75rem;
            cursor: grab;
            color: #6c757d;

            &:active {
              cursor: grabbing;
            }
          }

          .field-icon {
            font-size: 20px;
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
            color: #2196f3;

            // Icon colors được set thông qua inline style từ TypeScript
          }

          .field-info {
            flex: 1;

            .field-label {
              font-weight: 500;
              color: #495057;
              font-size: 0.875rem;
            }

            .field-type {
              font-size: 0.75rem;
              color: #6c757d;
              text-transform: uppercase;
            }
          }

          .field-actions {
            .btn {
              padding: 0.25rem;
              border-radius: 4px;

              mat-icon {
                font-size: 16px;
                height: 16px;
                width: 16px;
              }
            }
          }
        }

        // Empty Drop Zone Placeholder
        .empty-drop-zone-placeholder {
          min-height: 200px;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;

          .drop-zone-content {
            .drop-zone-icon {
              font-size: 48px;
              color: #6c757d;
              margin-bottom: 1rem;
            }

            .drop-zone-title {
              color: #495057;
              font-weight: 600;
              margin-bottom: 0.5rem;
            }

            .drop-zone-description {
              color: #6c757d;
              font-size: 0.875rem;
            }
          }
        }
      }
    }
  }
}



// ===== CDK DRAG & DROP ENHANCED ANIMATIONS =====
// Drag Preview - Hiệu ứng khi kéo item
.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 8px;
  // Enhanced shadow với nhiều lớp để tạo độ sâu
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35),
              0 12px 25px rgba(0, 0, 0, 0.25),
              0 6px 15px rgba(0, 0, 0, 0.2),
              0 2px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  opacity: 0.98;
  // Enhanced transform với rotation và scale
  transform: scale(1.08) rotate(3deg) translateZ(0);
  z-index: 10000; // Increased z-index để đảm bảo hiển thị trên tất cả elements
  border: 2px solid #007bff;
  // Smooth animation với custom easing
  transition: all 250ms cubic-bezier(0.25, 0.8, 0.25, 1);
  // Thêm backdrop filter cho hiệu ứng glass
  backdrop-filter: blur(8px);
  // Animation khi xuất hiện
  animation: dragPreviewAppear 0.2s ease-out forwards;

  .field-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: #007bff;
    // Thêm glow effect cho icon
    filter: drop-shadow(0 0 4px rgba(0, 123, 255, 0.3));
  }

  .field-label {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
    // Thêm text shadow nhẹ
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  // Hover effect khi drag
  &:hover {
    transform: scale(1.12) rotate(4deg) translateZ(0);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.4),
                0 15px 30px rgba(0, 0, 0, 0.3),
                0 8px 18px rgba(0, 0, 0, 0.25);
  }
}

// Drag Placeholder - Vị trí sẽ drop
.cdk-drag-placeholder {
  opacity: 0.3;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 2px dashed #007bff;
  border-radius: 8px;
  min-height: 52px;
  position: relative;
  // Smooth transition với custom timing
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  // Pulsing animation
  animation: placeholderPulse 2s ease-in-out infinite;

  // Thêm shimmer effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(0, 123, 255, 0.2),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }

  // Thêm dots pattern
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    background: radial-gradient(circle, #007bff 2px, transparent 2px);
    background-size: 8px 8px;
    opacity: 0.3;
  }
}

// Drag Animating - Hiệu ứng khi item đang di chuyển
.cdk-drag-animating {
  // Smooth return animation với spring effect
  transition: transform 400ms cubic-bezier(0.34, 1.56, 0.64, 1);

  // Thêm scale effect khi return
  &.returning {
    animation: returnToPosition 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  }
}

// ===== ENHANCED HOVER & DRAG STATES =====
// Field type items (left panel) - Enhanced hover effects
.field-type-item {
  transition: all 250ms cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;

  // Enhanced hover effect
  &:hover:not(.disabled) {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.2),
                0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);

    // Thêm shine effect
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
      );
      transition: left 0.5s;
      left: 100%;
    }
  }

  // Disabled state
  &.cdk-drag-disabled {
    cursor: not-allowed;
    opacity: 0.5;
    filter: grayscale(0.3);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(108, 117, 125, 0.1) 2px,
        rgba(108, 117, 125, 0.1) 4px
      );
      pointer-events: none;
    }
  }

  // Active drag state - Fixed: Đảm bảo item gốc vẫn hiển thị khi drag
  &.cdk-drag-dragging {
    opacity: 0.3; // Giảm opacity nhưng vẫn hiển thị để user biết vị trí gốc
    transform: scale(0.95); // Scale nhỏ hơn để phân biệt với drag preview
    z-index: 1;
    background: rgba(0, 123, 255, 0.1);
    border: 1px dashed #007bff;
  }
}

// Field list items (right panel) - Enhanced hover effects
.field-list-item {
  transition: all 250ms cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;

  &:hover {
    transform: translateY(-2px) translateX(2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12),
                0 2px 8px rgba(0, 0, 0, 0.08);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-color: #007bff;
  }

  // Active drag state - Fixed: Item gốc vẫn hiển thị khi drag trong sortable list
  &.cdk-drag-dragging {
    opacity: 0.4; // Opacity vừa phải để user vẫn thấy vị trí gốc
    transform: scale(0.98) translateY(2px); // Scale nhỏ và dịch chuyển nhẹ
    z-index: 1;
    background: rgba(0, 123, 255, 0.08);
    border: 1px dashed rgba(0, 123, 255, 0.3);
    box-shadow: inset 0 0 10px rgba(0, 123, 255, 0.1);
  }
}

// ===== DROP ZONE ENHANCED EFFECTS =====
// Drop zone khi đang nhận item
.cdk-drop-list-receiving {
  background: linear-gradient(135deg,
    rgba(0, 123, 255, 0.12) 0%,
    rgba(0, 123, 255, 0.08) 50%,
    rgba(0, 123, 255, 0.04) 100%);
  border: 2px dashed #007bff;
  border-radius: 10px;
  position: relative;
  // Enhanced glow animation
  animation: dropZoneReceiving 1.2s ease-in-out infinite alternate;

  // Thêm overlay effect
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #007bff, #0056b3, #007bff);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.15;
    animation: borderGlow 2s linear infinite;
  }
}

// Drop list khi đang drag - Enhanced effects cho items
.cdk-drop-list-dragging {
  // Field type items (left panel) - Hiệu ứng khi drag
  .field-type-item:not(.cdk-drag-placeholder):not(.disabled) {
    transition: all 300ms cubic-bezier(0.25, 0.8, 0.25, 1);

    &:hover {
      transform: scale(1.03) translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 123, 255, 0.2);
      border-color: #007bff;
    }

    // Thêm subtle animation khi không hover
    animation: subtleFloat 3s ease-in-out infinite;
  }

  // Field list items (right panel) - Hiệu ứng khi reorder
  .field-list-item:not(.cdk-drag-placeholder) {
    transition: all 300ms cubic-bezier(0.25, 0.8, 0.25, 1);

    &:hover {
      transform: translateY(-1px) scale(1.01);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  // Mat-row specific styling (nếu có table rows)
  .mat-row:not(.cdk-drag-placeholder) {
    transition: all 300ms cubic-bezier(0.25, 0.8, 0.25, 1);

    &:hover {
      background-color: rgba(0, 123, 255, 0.05);
      transform: translateX(4px);
      box-shadow: 4px 0 8px rgba(0, 123, 255, 0.1);
    }

    // Zebra striping effect khi drag
    &:nth-child(even) {
      background-color: rgba(248, 249, 250, 0.5);
    }

    &:nth-child(odd) {
      background-color: rgba(255, 255, 255, 0.8);
    }
  }
}

// Removed .drag-preview class - Angular CDK automatically applies .cdk-drag-preview

// ===== ENHANCED KEYFRAME ANIMATIONS =====
// Fade in animation cho các elements
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// Drag preview xuất hiện
@keyframes dragPreviewAppear {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1) rotate(1deg);
  }
  100% {
    opacity: 0.98;
    transform: scale(1.08) rotate(3deg);
  }
}

// Placeholder pulsing effect
@keyframes placeholderPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
    border-color: #007bff;
  }
  50% {
    opacity: 0.5;
    transform: scale(1.01);
    border-color: #0056b3;
  }
}

// Shimmer effect cho placeholder
@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// Drop zone receiving animation
@keyframes dropZoneReceiving {
  0% {
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.3),
                inset 0 0 20px rgba(0, 123, 255, 0.1);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 25px rgba(0, 123, 255, 0.6),
                inset 0 0 30px rgba(0, 123, 255, 0.2);
    transform: scale(1.002);
  }
}

// Border glow animation
@keyframes borderGlow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// Return to position animation
@keyframes returnToPosition {
  0% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

// Subtle float animation cho items
@keyframes subtleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-1px);
  }
}

// Drag start animation
@keyframes dragStart {
  0% {
    transform: scale(1) rotate(0deg);
  }
  100% {
    transform: scale(1.05) rotate(2deg);
  }
}

// Drag handle animation
.drag-handle {
  transition: all 200ms ease-in-out;

  &:hover {
    color: #007bff;
    transform: scale(1.1);
  }
}

// Used indicator animation
.used-indicator {
  animation: fadeIn 0.3s ease-in-out;
}

// Drag starting animation
.drag-starting {
  animation: dragStart 0.2s ease-out forwards;
}

// Drop zone active state
.drop-zone-active {
  background: linear-gradient(135deg, rgba(0, 123, 255, 0.08), rgba(0, 123, 255, 0.03));
  border: 2px dashed rgba(0, 123, 255, 0.5);
  border-radius: 8px;
  animation: dropZoneGlow 1.5s ease-in-out infinite alternate;

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 8px;
    z-index: -1;
    opacity: 0.1;
  }
}

// Enhanced field item states
.field-type-item {
  position: relative;

  &.disabled {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(108, 117, 125, 0.1);
      border-radius: 4px;
      pointer-events: none;
    }
  }

  &:not(.disabled) {
    cursor: grab;

    &:active {
      cursor: grabbing;
    }
  }
}

// Field list item enhanced states
.field-list-item {
  position: relative;
  cursor: grab;

  &:active {
    cursor: grabbing;
  }

  &.cdk-drag-preview {
    cursor: grabbing;
  }
}

// ===== ENHANCED JAVASCRIPT EFFECTS =====
// Body class khi đang drag
body.is-dragging {
  user-select: none;

  * {
    cursor: grabbing !important;
  }
}

// Ripple effect cho drag start
.drag-ripple {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(0, 123, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: rippleExpand 0.6s ease-out forwards;
  pointer-events: none;
  z-index: 1000;
}

@keyframes rippleExpand {
  0% {
    width: 20px;
    height: 20px;
    opacity: 0.8;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

// Other draggable items highlight khi drag
.other-draggable-highlighted {
  opacity: 0.7;
  transform: scale(0.98);
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
    transform: scale(1);
  }
}

// Drop success indicator
.drop-success-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1001;
  color: #28a745;
  font-size: 48px;
  animation: dropSuccess 1s ease-out forwards;
  pointer-events: none;

  i {
    filter: drop-shadow(0 0 8px rgba(40, 167, 69, 0.5));
  }
}

@keyframes dropSuccess {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

// Enhanced drag starting animation
.drag-starting {
  animation: enhancedDragStart 0.2s ease-out forwards;
  z-index: 999;
}

@keyframes enhancedDragStart {
  0% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    transform: scale(1.08) rotate(1deg);
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
  }
  100% {
    transform: scale(1.05) rotate(2deg);
    box-shadow: 0 12px 25px rgba(0, 123, 255, 0.4);
  }
}

// Enhanced returning animation
.returning {
  animation: enhancedReturn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

@keyframes enhancedReturn {
  0% {
    transform: scale(1.05) rotate(2deg);
  }
  50% {
    transform: scale(0.95) rotate(-1deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}
