import { Injectable, Type, ElementRef, ComponentRef } from '@angular/core';
import { Overlay, OverlayConfig, OverlayRef, ConnectedPosition } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { Observable, Subject } from 'rxjs';
import { take } from 'rxjs/operators';

import { StandardOverlayModalWrapperComponent } from './standard-overlay-modal.component';
import { 
  StandardOverlayModalConfig, 
  StandardOverlayModalConfigWithComponent,
  DEFAULT_OVERLAY_MODAL_CONFIG 
} from './models/standard-overlay-modal-config.interface';
import { 
  StandardOverlayModalResult,
  StandardOverlayModalResultFactory 
} from '../../models/view/standard-overlay-modal-result.interface';
import { StrictModalComponent } from '../../models/view/modal-component.interface';

/**
 * StandardOverlayModalService - Service để quản lý overlay modals với positioning
 *
 * Chức năng chính:
 * - Tạo và quản lý overlay modals với CDK Overlay
 * - Mouse positioning: Modal căn giữa theo vị trí chuột
 * - Element positioning: Modal hiển thị gần element trigger
 * - Smart viewport awareness: Tự động điều chỉnh vị trí khi tràn viewport
 * - Type-safe với TypeScript generics
 * - Consistent API với StandardDialogService và StandardBottomSheetService
 *
 * Pattern tương đồng:
 * - Sử dụng wrapper component pattern
 * - Observable-based result handling
 * - Generic type support cho component và data
 * - Lifecycle management với proper cleanup
 */
@Injectable({
  providedIn: 'root'
})
export class StandardOverlayModalService {
  constructor(private overlay: Overlay) {}

  /**
   * Mở overlay modal với component và MouseEvent
   *
   * @param component - Component sẽ được render trong modal
   * @param event - MouseEvent để extract positioning
   * @param config - Configuration cho modal
   * @returns Observable<TResult | undefined> - Kết quả từ modal
   */
  open<
    TComponent extends StrictModalComponent = StrictModalComponent,
    TData = any,
    TResult = any
  >(
    component: Type<TComponent>,
    event: MouseEvent,
    config: StandardOverlayModalConfig<TData> = {}
  ): Observable<TResult | undefined> {
    // Extract mouse position từ MouseEvent
    const mousePosition = { x: event.clientX, y: event.clientY };

    // Merge với default config
    const mergedConfig: StandardOverlayModalConfigWithComponent<TComponent, TData> = {
      ...DEFAULT_OVERLAY_MODAL_CONFIG,
      ...config,
      component
    };

    // Tạo overlay với mouse position
    const overlayRef = this.createOverlay(mergedConfig, mousePosition);

    // Tạo component portal
    const portal = new ComponentPortal(StandardOverlayModalWrapperComponent);
    const componentRef = overlayRef.attach(portal);

    // Inject data vào wrapper component
    this.injectDataToWrapper(componentRef, mergedConfig, overlayRef);

    // Setup result handling
    return this.setupResultHandling<TResult>(overlayRef, componentRef);
  }

  /**
   * Tạo overlay với configuration và mouse position
   */
  private createOverlay(
    config: StandardOverlayModalConfig,
    mousePosition: { x: number; y: number }
  ): OverlayRef {
    const overlayConfig = this.buildOverlayConfig(config, mousePosition);
    return this.overlay.create(overlayConfig);
  }

  /**
   * Build OverlayConfig từ StandardOverlayModalConfig và mouse position
   */
  private buildOverlayConfig(
    config: StandardOverlayModalConfig,
    mousePosition: { x: number; y: number }
  ): OverlayConfig {
    const positioning = config.positioning || {};

    // Determine positioning strategy
    let positionStrategy;

    if (positioning.elementRef) {
      // Element positioning với connected strategy (fallback)
      const connectedPositions = this.calculateConnectedPositions();
      positionStrategy = this.overlay.position()
        .flexibleConnectedTo(positioning.elementRef)
        .withPositions(connectedPositions)
        .withPush(true)
        .withViewportMargin(positioning.viewportMargin || 16);
    } else {
      // Mouse positioning với global strategy (primary)
      const positions = this.calculateGlobalPositions(mousePosition, config);
      positionStrategy = this.overlay.position()
        .global()
        .left(`${positions.left}px`)
        .top(`${positions.top}px`);
    }

    // Build scroll strategy
    let scrollStrategy;
    switch (config.scrollStrategy) {
      case 'close':
        scrollStrategy = this.overlay.scrollStrategies.close();
        break;
      case 'block':
        scrollStrategy = this.overlay.scrollStrategies.block();
        break;
      case 'reposition':
      default:
        scrollStrategy = this.overlay.scrollStrategies.reposition();
        break;
    }

    return new OverlayConfig({
      hasBackdrop: true,
      backdropClass: config.backdropClass || 'cdk-overlay-transparent-backdrop',
      panelClass: config.panelClass || 'standard-overlay-modal-panel',
      positionStrategy,
      scrollStrategy,
      disposeOnNavigation: true,
      width: positioning.dimensions?.width || 'auto',
      height: positioning.dimensions?.height || 'auto',
      maxWidth: positioning.dimensions?.maxWidth || '600px',
      maxHeight: positioning.dimensions?.maxHeight || '80vh',
      minWidth: positioning.dimensions?.minWidth || '300px',
      minHeight: positioning.dimensions?.minHeight || 'auto'
    });
  }

  /**
   * Tính toán global positions cho mouse positioning
   */
  private calculateGlobalPositions(
    mousePosition: { x: number; y: number },
    config: StandardOverlayModalConfig
  ): { left: number; top: number } {
    const { x, y } = mousePosition;
    const positioning = config.positioning || {};
    const dimensions = positioning.dimensions || {};
    const offset = positioning.offset || { x: 0, y: 8 };
    const viewportMargin = positioning.viewportMargin || 16;
    const overlayModalPosition = positioning.overlayModalPosition || 'bottom-center';

    // Estimate modal dimensions
    const modalWidth = this.parseSize(dimensions.width) ||
                      this.parseSize(dimensions.maxWidth) || 350;
    const modalHeight = this.parseSize(dimensions.height) ||
                       this.parseSize(dimensions.maxHeight) || 400;

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Calculate horizontal và vertical position based on alignment option
    let left: number;
    let top: number;

    switch (overlayModalPosition) {
      // Legacy options (maintain backward compatibility)
      case 'left':
        // Modal bắt đầu từ vị trí con trỏ chuột (left edge = mouse X)
        left = x + (offset.x || 0);
        top = y + (offset.y || 8);
        break;
      case 'right':
        // Modal kết thúc tại vị trí con trỏ chuột (right edge = mouse X)
        left = x - modalWidth + (offset.x || 0);
        top = y + (offset.y || 8);
        break;
      case 'center':
        // Modal center tại vị trí con trỏ chuột (legacy behavior)
        left = x - (modalWidth / 2) + (offset.x || 0);
        top = y + (offset.y || 8);
        break;

      // New positioning options
      case 'top-left':
        // Modal hiển thị phía trên bên trái vị trí con trỏ chuột
        left = x + (offset.x || 0);
        top = y - modalHeight - (offset.y || 8);
        break;
      case 'top-center':
        // Modal hiển thị phía trên và center theo vị trí con trỏ chuột
        left = x - (modalWidth / 2) + (offset.x || 0);
        top = y - modalHeight - (offset.y || 8);
        break;
      case 'top-right':
        // Modal hiển thị phía trên bên phải vị trí con trỏ chuột
        left = x - modalWidth + (offset.x || 0);
        top = y - modalHeight - (offset.y || 8);
        break;
      case 'bottom-left':
        // Modal hiển thị phía dưới bên trái vị trí con trỏ chuột
        left = x + (offset.x || 0);
        top = y + (offset.y || 8);
        break;
      case 'bottom-center':
      default:
        // Modal hiển thị phía dưới và center theo vị trí con trỏ chuột (new default)
        left = x - (modalWidth / 2) + (offset.x || 0);
        top = y + (offset.y || 8);
        break;
      case 'bottom-right':
        // Modal hiển thị phía dưới bên phải vị trí con trỏ chuột
        left = x - modalWidth + (offset.x || 0);
        top = y + (offset.y || 8);
        break;
    }

    // Smart positioning với viewport awareness
    const rightEdge = left + modalWidth;
    const leftEdge = left;
    const bottomEdge = top + modalHeight;
    const topEdge = top;

    // Adjust horizontal position nếu modal vượt ra ngoài viewport
    if (rightEdge > viewportWidth - viewportMargin) {
      // Nếu modal vượt ra bên phải, điều chỉnh dựa trên alignment
      switch (overlayModalPosition) {
        case 'left':
        case 'top-left':
        case 'bottom-left':
          // Với left alignment, ưu tiên giữ left edge tại mouse position
          // Chỉ điều chỉnh nếu thực sự cần thiết
          if (rightEdge > viewportWidth - viewportMargin) {
            left = viewportWidth - modalWidth - viewportMargin;
          }
          break;
        case 'right':
        case 'top-right':
        case 'bottom-right':
          // Với right alignment, giữ right edge tại mouse position
          left = x - modalWidth + (offset.x || 0);
          // Nếu vẫn vượt ra ngoài, điều chỉnh
          if (left < viewportMargin) {
            left = viewportMargin;
          }
          break;
        case 'center':
        case 'top-center':
        case 'bottom-center':
        default:
          left = viewportWidth - modalWidth - viewportMargin;
          break;
      }
    }

    if (leftEdge < viewportMargin) {
      // Nếu modal vượt ra bên trái, điều chỉnh dựa trên alignment
      switch (overlayModalPosition) {
        case 'right':
        case 'top-right':
        case 'bottom-right':
          // Với right alignment, ưu tiên giữ right edge tại mouse position
          // Chỉ điều chỉnh nếu thực sự cần thiết
          if (leftEdge < viewportMargin) {
            left = viewportMargin;
          }
          break;
        case 'left':
        case 'top-left':
        case 'bottom-left':
          // Với left alignment, giữ left edge tại mouse position
          left = x + (offset.x || 0);
          // Nếu vẫn vượt ra ngoài, điều chỉnh
          if (left + modalWidth > viewportWidth - viewportMargin) {
            left = viewportWidth - modalWidth - viewportMargin;
          }
          break;
        case 'center':
        case 'top-center':
        case 'bottom-center':
        default:
          left = viewportMargin;
          break;
      }
    }

    // Adjust vertical position nếu modal vượt ra ngoài viewport
    if (bottomEdge > viewportHeight - viewportMargin) {
      // Nếu modal vượt ra phía dưới, điều chỉnh dựa trên alignment
      switch (overlayModalPosition) {
        case 'top-left':
        case 'top-center':
        case 'top-right':
          // Với top alignment, ưu tiên giữ top position
          // Chỉ điều chỉnh nếu thực sự cần thiết
          if (bottomEdge > viewportHeight - viewportMargin) {
            top = viewportHeight - modalHeight - viewportMargin;
          }
          break;
        case 'bottom-left':
        case 'bottom-center':
        case 'bottom-right':
        case 'left':
        case 'right':
        case 'center':
        default:
          // Với bottom alignment, flip to top nếu có đủ không gian
          const topFlipPosition = y - modalHeight - (offset.y || 8);
          if (topFlipPosition >= viewportMargin) {
            top = topFlipPosition;
          } else {
            top = viewportHeight - modalHeight - viewportMargin;
          }
          break;
      }
    }

    if (topEdge < viewportMargin) {
      // Nếu modal vượt ra phía trên, điều chỉnh dựa trên alignment
      switch (overlayModalPosition) {
        case 'bottom-left':
        case 'bottom-center':
        case 'bottom-right':
          // Với bottom alignment, ưu tiên giữ bottom position
          // Chỉ điều chỉnh nếu thực sự cần thiết
          if (topEdge < viewportMargin) {
            top = viewportMargin;
          }
          break;
        case 'top-left':
        case 'top-center':
        case 'top-right':
        case 'left':
        case 'right':
        case 'center':
        default:
          // Với top alignment, flip to bottom nếu có đủ không gian
          const bottomFlipPosition = y + (offset.y || 8);
          if (bottomFlipPosition + modalHeight <= viewportHeight - viewportMargin) {
            top = bottomFlipPosition;
          } else {
            top = viewportMargin;
          }
          break;
      }
    }

    // Nếu modal quá rộng hoặc quá cao cho viewport, center nó
    if (modalWidth > viewportWidth - (viewportMargin * 2)) {
      left = viewportMargin;
    }

    if (modalHeight > viewportHeight - (viewportMargin * 2)) {
      top = viewportMargin;
    }



    return { left, top };
  }

  /**
   * Tính toán connected positions cho element positioning
   */
  private calculateConnectedPositions(): ConnectedPosition[] {
    return [
      // Below, aligned to start
      {
        originX: 'start',
        originY: 'bottom',
        overlayX: 'start',
        overlayY: 'top',
        offsetY: 8
      },
      // Below, aligned to end
      {
        originX: 'end',
        originY: 'bottom',
        overlayX: 'end',
        overlayY: 'top',
        offsetY: 8
      },
      // Above, aligned to start
      {
        originX: 'start',
        originY: 'top',
        overlayX: 'start',
        overlayY: 'bottom',
        offsetY: -8
      },
      // Above, aligned to end
      {
        originX: 'end',
        originY: 'top',
        overlayX: 'end',
        overlayY: 'bottom',
        offsetY: -8
      }
    ];
  }

  /**
   * Parse size string to number (px only)
   */
  private parseSize(size: string | number | undefined): number | undefined {
    if (typeof size === 'number') return size;
    if (typeof size === 'string' && size.endsWith('px')) {
      return parseInt(size.replace('px', ''), 10);
    }
    return undefined;
  }

  /**
   * Inject data vào wrapper component
   */
  private injectDataToWrapper<TComponent extends StrictModalComponent, TData>(
    componentRef: ComponentRef<StandardOverlayModalWrapperComponent<TComponent>>,
    config: StandardOverlayModalConfigWithComponent<TComponent, TData>,
    overlayRef: OverlayRef
  ): void {
    // Inject config data theo pattern của StandardDialogService
    const instance = componentRef.instance as any;

    // Inject data object với component reference
    instance.data = {
      ...config,
      component: config.component
    };

    // Inject overlay ref để component có thể tự đóng
    instance.overlayRef = overlayRef;
  }

  /**
   * Setup result handling với Observable
   */
  private setupResultHandling<TResult>(
    overlayRef: OverlayRef,
    componentRef: ComponentRef<StandardOverlayModalWrapperComponent>
  ): Observable<TResult | undefined> {
    const resultSubject = new Subject<TResult | undefined>();

    // Handle backdrop click
    overlayRef.backdropClick().subscribe(() => {
      resultSubject.next(undefined);
      resultSubject.complete();
      overlayRef.dispose();
    });

    // Handle overlay disposal
    overlayRef.detachments().subscribe(() => {
      const result = componentRef.instance.getComponentData();
      resultSubject.next(result);
      resultSubject.complete();
    });

    return resultSubject.asObservable().pipe(take(1));
  }
}
