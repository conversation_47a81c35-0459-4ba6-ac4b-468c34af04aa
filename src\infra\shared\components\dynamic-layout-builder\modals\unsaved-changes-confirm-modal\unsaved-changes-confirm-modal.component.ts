import { Component, Inject, ChangeDetectionStrategy, inject, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { FlashMessageService } from '@core/services/flash_message.service';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';
import { LayoutSwitchConfirmData } from '../../services/dynamic-layout-config-state.service';

/**
 * Modal component để confirm khi user switch layout mà có unsaved changes
 * Hiển thị 3 options: Save & Switch, Discard & Switch, Cancel
 */
@Component({
  selector: 'app-unsaved-changes-confirm-modal',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    TranslateModule
  ],
  templateUrl: './unsaved-changes-confirm-modal.component.html',
  styleUrls: ['./unsaved-changes-confirm-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UnsavedChangesConfirmModalComponent implements StrictModalComponent<LayoutSwitchConfirmData, UnsavedChangesConfirmResult> {

  /**
   * Injected services
   */
  private flashMessageService = inject(FlashMessageService);
  private translateService = inject(TranslateService);

  /**
   * Component state
   */
  isSaving = false;

  /**
   * Data cho modal
   */
  data: LayoutSwitchConfirmData;

  constructor(
    @Optional() public dialogRef?: MatDialogRef<UnsavedChangesConfirmModalComponent>,
    @Optional() public bottomSheetRef?: MatBottomSheetRef<UnsavedChangesConfirmModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: LayoutSwitchConfirmData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: LayoutSwitchConfirmData
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {} as LayoutSwitchConfirmData;
    console.log('🔄 UnsavedChangesConfirmModal: Khởi tạo với data:', this.data);
  }



  /**
   * Xử lý khi user chọn "Save & Switch"
   * Emit result với action 'save'
   */
  onSaveAndSwitch(): void {
    console.log('💾 UnsavedChangesConfirmModal: User chọn Save & Switch');

    this.isSaving = true;

    const result: UnsavedChangesConfirmResult = {
      action: 'save',
      fromLayout: this.data.fromLayout,
      toLayout: this.data.toLayout
    };

    this.close(result);
  }

  /**
   * Xử lý khi user chọn "Discard & Switch"
   * Emit result với action 'discard'
   */
  onDiscardAndSwitch(): void {
    console.log('🗑️ UnsavedChangesConfirmModal: User chọn Discard & Switch');

    const result: UnsavedChangesConfirmResult = {
      action: 'discard',
      fromLayout: this.data.fromLayout,
      toLayout: this.data.toLayout
    };

    this.close(result);
  }

  /**
   * Xử lý khi user chọn "Cancel"
   * Đóng modal mà không làm gì
   */
  onCancel(): void {
    console.log('❌ UnsavedChangesConfirmModal: User chọn Cancel');

    const result: UnsavedChangesConfirmResult = {
      action: 'cancel'
    };

    this.close(result);
  }

  /**
   * Xử lý khi user click backdrop hoặc ESC
   * Tương đương với Cancel
   */
  onNoClick(): void {
    this.onCancel();
  }

  /**
   * Đóng modal với kết quả
   */
  private close(result: UnsavedChangesConfirmResult): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): UnsavedChangesConfirmResult {
    // Mặc định trả về cancel nếu không có action cụ thể
    return { action: 'cancel' };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   * Modal này luôn valid
   */
  isValid(): boolean {
    return true; // Luôn valid vì không có dữ liệu cần validate
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: LayoutSwitchConfirmData): void {
    this.data = data;
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào nút save khi modal mở
    setTimeout(() => {
      const saveButton = document.querySelector('button[mat-raised-button]') as HTMLButtonElement;
      if (saveButton) {
        saveButton.focus();
      }
    }, 100);
  }
}

/**
 * Interface cho result của modal
 */
export interface UnsavedChangesConfirmResult {
  action: 'save' | 'discard' | 'cancel';
  fromLayout?: any;
  toLayout?: any;
}
