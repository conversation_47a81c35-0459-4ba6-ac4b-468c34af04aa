import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';

import { ProductVariant, VariantFormModalData, VariantFormModalResult } from '../../../models/view/variant-form-modal.model';
import { ChipFormFieldComponent } from '@/shared/components/input/chip-form-field/chip-form-field.component';
import { mockProductVariants } from '@mock/product_form';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Component VariantFormModal
 * Form tạo và chỉnh sửa thuộc tính sản phẩm theo interface ProductVariant
 */
@Component({
  selector: 'app-variant-form-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatCheckboxModule,
    MatCardModule,
    MatIconModule,
    TranslateModule,
    MatDialogModule,
    ChipFormFieldComponent
  ],
  templateUrl: './variant-form-modal.component.html',
  styleUrls: ['./variant-form-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class VariantFormModalComponent implements OnInit, StrictModalComponent<VariantFormModalData, VariantFormModalResult> {
  /**
   * Form cho thuộc tính sản phẩm
   */
  variantForm: FormGroup;

  /**
   * ID của thuộc tính đang chỉnh sửa (nếu có)
   */
  editId?: string;

  /**
   * Danh sách gợi ý cho giá trị thuộc tính
   */
  variantValueSuggestions: string[] = [];

  /**
   * Dữ liệu từ inject
   */
  data: VariantFormModalData;

  constructor(
    private fb: FormBuilder,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: VariantFormModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: VariantFormModalData,
    @Optional() private dialogRef?: MatDialogRef<VariantFormModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<VariantFormModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || {};

    // Khởi tạo form
    this.variantForm = this.fb.group({
      name: ['', Validators.required],
      values: [[], Validators.required],
      isActive: [true]
    });

    // Lấy ID thuộc tính đang chỉnh sửa (nếu có)
    this.editId = this.data.editId;

    // Lấy danh sách gợi ý (nếu có)
    if (this.data.valueSuggestions) {
      this.variantValueSuggestions = this.data.valueSuggestions;
    }
  }

  /**
   * Khởi tạo component
   */
  ngOnInit(): void {
    // Tạo danh sách gợi ý từ các giá trị thuộc tính hiện có nếu chưa có
    if (this.variantValueSuggestions.length === 0) {
      this.loadValueSuggestions();
    }
  }

  /**
   * Tải danh sách gợi ý giá trị thuộc tính
   */
  loadValueSuggestions(): void {
    // Tạo danh sách gợi ý từ tất cả các giá trị thuộc tính hiện có
    const allValues = mockProductVariants.flatMap(variant => variant.values);
    this.variantValueSuggestions = [...new Set(allValues)]; // Loại bỏ các giá trị trùng lặp
  }

  /**
   * Lưu thuộc tính
   */
  saveVariant(): void {
    if (this.variantForm.invalid) {
      return;
    }

    const formValue = this.variantForm.value;

    // Tạo đối tượng thuộc tính mới
    const variant: ProductVariant = {
      _id: this.editId || `var${mockProductVariants.length + 1}`,
      name: formValue.name,
      values: formValue.values,
      isActive: formValue.isActive,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    if (this.editId) {
      // Cập nhật thuộc tính hiện có
      const index = mockProductVariants.findIndex(v => v._id === this.editId);
      if (index !== -1) {
        mockProductVariants[index] = {
          ...mockProductVariants[index],
          ...variant,
          updatedAt: new Date()
        };
      }
    } else {
      // Thêm thuộc tính mới
      mockProductVariants.push(variant);
    }

    // Đóng modal và trả về thuộc tính đã lưu
    this.close(variant);
  }

  /**
   * Hủy và đóng form
   */
  cancel(): void {
    this.close(null);
  }

  /**
   * Đóng modal
   */
  private close(result: ProductVariant | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): VariantFormModalResult {
    if (!this.variantForm.valid) {
      return null;
    }

    const formValue = this.variantForm.value;

    // Tạo ID mới nếu là thêm mới
    if (!this.editId) {
      formValue._id = `variant_${Date.now().toString()}`;
    } else {
      formValue._id = this.editId;
    }

    // Thêm timestamps
    const now = new Date();
    return {
      ...formValue,
      createdAt: formValue.createdAt || now,
      updatedAt: now
    };
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    return this.variantForm.valid;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: VariantFormModalData): void {
    this.data = data;
    this.editId = data.editId;

    if (data.valueSuggestions) {
      this.variantValueSuggestions = data.valueSuggestions;
    }

    // Nếu có editId, load dữ liệu thuộc tính để chỉnh sửa
    if (this.editId) {
      const existingVariant = mockProductVariants.find(v => v._id === this.editId);
      if (existingVariant) {
        this.variantForm.patchValue(existingVariant);
      }
    }
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Focus vào input đầu tiên khi modal mở
    setTimeout(() => {
      const firstInput = document.querySelector('input[formControlName="name"]') as HTMLInputElement;
      if (firstInput) {
        firstInput.focus();
      }
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
