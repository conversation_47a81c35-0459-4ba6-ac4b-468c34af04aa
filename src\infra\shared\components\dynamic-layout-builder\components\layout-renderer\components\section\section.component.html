<!-- Section Container -->
@if (hasVisibleFields()) {
  <div class="section-container mb-4">

    <!-- Section Header -->
    @if (shouldShowTitle()) {
      <div class="section-header mb-3">
        <h5 class="section-title mb-0">
          {{ config.section.title }}
        </h5>
      </div>
    }

    <!-- Section Content -->
    <div class="section-content">
      <!-- Fields Container - khôi phục logic single/double column -->
      <div class="section-fields-container"
           [class.single-column]="!isDoubleColumn()"
           [class.double-column]="isDoubleColumn()">
        @for (field of visibleFields(); track field._id) {
          <app-field-item
            [config]="createFieldItemConfig(field)"
            (valueChange)="onFieldValueChange($event)"
            class="field-item">
          </app-field-item>
        }
      </div>
    </div>
  </div>
}
