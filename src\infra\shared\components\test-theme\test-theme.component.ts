import { ChangeDetectionStrategy, Component, OnInit, inject, TemplateRef, ViewChild } from '@angular/core';
import { FlashMessageService } from '@core/services/flash_message.service';
import { TranslateService } from '@ngx-translate/core';
import { PromotionModalService } from '@features/sales/order-form/components/modals/promotion-modal';
import { VariantFormModalService } from '@shared/modals/product/variant-form-modal';
import { MatDialog } from '@angular/material/dialog';
import { NoteModalService } from '@features/sales/order-form/components/modals/note-modal';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';
import { MixedPaymentModalService } from '@features/sales/order-form/components/modals/mixed-payment-modal/mixed-payment-modal.service';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { OrderItemModifierModalService } from '@shared/modals/sales/order/order-item-modifier-modal/order-item-modifier-modal.service';
import { mockProductSelectorConfig } from '@mock/sales/order_form.mock';
import { OrderItemBaseDetails } from 'salehub_shared_contracts/entities/oms/order/order_components/order_item';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { OrderItemVariantUnitSelectionModalService } from '@shared/modals/sales/order/order-item-variant-unit-selection-modal';
import { OrderProductPickerComponent } from '@shared/components/product-selection/order-product-picker/order-product-picker.component';
import { InputPlaceComponent } from '../input/input-place/input-place.component';
import { AddressManualSelectorComponent } from '../input/input-place/components/address-manual-selector/address-manual-selector.component';
import { InputAddressComponent } from '../input/input-place/components/input-address/input-address.component';
import { DynamicLayoutRendererComponent } from '../dynamic-layout-builder/components/layout-renderer/dynamic-layout-renderer.component';
import { DynamicLayoutRendererConfig } from '../dynamic-layout-builder/models/dynamic-layout-renderer.model';
import { mockLayoutRendererConfig, simpleMockLayouts } from '@mock/product/simple_layout.mock';
import { mockProductModifierGroupList, mockProductUnits, mockVariantList, mockProductList } from '@mock/product/product.mock';
import { BatchModalComponent, BatchData } from '@shared/modals/warehouse/batch-modal/batch-modal.component';
import { CategoryProductModalComponent } from '@features/warehouse/goods-receipt/components/category-product-modal/category-product-modal.component';
import { CategoryProductModalData } from '@features/warehouse/goods-receipt/models/view/goods-receipt.view-model';
import { AdditionalCostModalService } from '@features/warehouse/goods-receipt/components/additional-cost-modal/additional-cost-modal.service';
import { TaxFormModalService } from '@shared/modals/common/tax-form-modal';
import { TaxInfo } from 'salehub_shared_contracts/entities/ims/inventory/goods_receipt';
import { QualityCheckRejectModalComponent, RejectedItem, QualityCheckRejectModalData } from '@features/warehouse/goods-receipt/components/quality-check-reject-modal/quality-check-reject-modal.component';
import { SimpleNoteModalService } from '@shared/modals/common/simple-note-modal';
import { ProductFilterModalComponent } from '@features/warehouse/inventory-check/components/product-filter-modal/product-filter-modal.component';
import { FieldPermissionModalService } from '@/shared/components/dynamic-layout-builder/modals/field-permission';
import { ProductFilterModalData, EmbeddedProductSerial, ProductListItem } from '@features/warehouse/inventory-check/models/api/inventory-check.dto';
import { ProductFilterResult } from '@features/warehouse/inventory-check/models/view/inventory-check.view-model';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';
import { ConfirmModalService } from '@shared/modals/common/confirm-modal';
import { FieldPropertiesData, FieldPropertiesModalComponent } from '@/shared/components/dynamic-layout-builder/modals/field-properties/field-properties-modal.component';
import { FieldFiltersComponent } from '../field-filters/field-filters.component';
import { Field } from '@domain/entities/field.entity';
import { FilterChangeEvent, FieldFilter } from '../field-filters/models/view/field-filter-view.model';
import { mockCustomerFields } from '@mock/fields.mock';
import { StandardDialogService, StandardDialogResult } from '../responsive-modal/components/standard-dialog';
import { SimpleTestComponent } from '../responsive-modal/components/standard-dialog/test-components/simple-test.component';
import { StandardBottomSheetService, StandardBottomSheetResult } from '../responsive-modal/components/standard-bottom-sheet';
import { firstValueFrom } from 'rxjs';
import { CreateLayoutModalService } from '../dynamic-layout-builder/modals/create-layout-modal/create-layout-modal.service';
import { CreateLayoutModalResult } from '../dynamic-layout-builder/modals/create-layout-modal/create-layout-modal.component';
import { DynamicLayoutRendererModalService } from '../dynamic-layout-builder/modals/dynamic-layout-renderer-modal/dynamic-layout-renderer-modal.service';
import { DynamicLayoutRendererModalData, DynamicLayoutRendererModalResult, DynamicLayoutRendererFormData } from '../dynamic-layout-builder/models/dynamic-layout-renderer.model';
import {
  SimpleBottomSheetTestComponent,
  SimpleBottomSheetTestData,
  SimpleBottomSheetTestResult
} from '../responsive-modal/components/standard-bottom-sheet/test-components/simple-bottom-sheet-test.component';
import { ListColumnSelectorModalComponent } from '@shared/modals/common/list-column-selector-modal/list-column-selector-modal.component';
import { ListColumnConfig } from '@shared/models/view/list-layout.model';
import { ValidationTestComponent, ValidationTestData, ValidationTestResult } from '../responsive-modal/components/standard-dialog/test-components/validation-test.component';
import { StrictTestModalComponent, StrictTestModalData, StrictTestModalResult } from './components/strict-test-modal.component';
import { ResponsiveModalConfig } from '@core/services/responsive-modal.service';
import { SettingsListComponent, SettingChangeEvent, SettingsListConfig, SettingsSaveEvent, AutoSaveToggleEvent } from '../settings/settings-list.component';
import { mockAllSettings, mockDefaultSettingValues } from '@mock/settings/all-settings.mock';
import { IntegrationAccountViewModalComponent } from '@features/settings/integrations/components/integration-new/modals/integration-account-view-modal/integration-account-view-modal.component';
import { IntegrationAccountViewModalData, IntegrationAccountViewModalResult } from '@features/settings/integrations/components/integration-new/modals/integration-account-view-modal/integration-account-view-modal.interfaces';
import { IntegrationAccountSettingsModalComponent } from '@features/settings/integrations/components/integration-new/modals/integration-account-settings-modal/integration-account-settings-modal.component';
import { IntegrationAccountSettingsModalData, IntegrationAccountSettingsModalResult } from '@features/settings/integrations/components/integration-new/modals/integration-account-settings-modal/integration-account-settings-modal.interfaces';
import { IntegrationAccountLogsModalComponent } from '@features/settings/integrations/components/integration-new/modals/integration-account-logs-modal/integration-account-logs-modal.component';
import { IntegrationAccountLogsModalData, IntegrationAccountLogsModalResult } from '@features/settings/integrations/components/integration-new/modals/integration-account-logs-modal/integration-account-logs-modal.interfaces';
import { getMockLogsByPlatform } from '@/mock/settings/integration_view_account.mock';
import { DynamicLayoutBuilderComponent } from '../dynamic-layout-builder/components/layout-builder/dynamic-layout-builder.component';

/**
 * Interface cho kết quả test modal với action và data
 */
export interface TestModalResult {
  action: 'confirm' | 'cancel' | 'custom';
  data?: unknown;
  customAction?: {
    name: string;
    value: unknown;
  };
}

@Component({
  selector: 'app-test-theme',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    InputPlaceComponent,
    DynamicLayoutBuilderComponent,
    DynamicLayoutRendererComponent,
    SettingsListComponent
    // OrderProductPickerComponent
  ],
  providers: [
    // Provide CreateLayoutModalService để tránh NullInjectorError
    CreateLayoutModalService,
    // Provide DynamicLayoutRendererModalService để test modal
    DynamicLayoutRendererModalService
  ],
  templateUrl: './test-theme.component.html',
  styleUrl: './test-theme.component.scss'
})
export class TestThemeComponent implements OnInit {
  // Sử dụng property injection thay vì constructor
  private dialog = inject(MatDialog);
  private bottomSheet = inject(MatBottomSheet);
  private responsiveModalService = inject(ResponsiveModalService);
  private flashMessageService = inject(FlashMessageService);
  private dynamicLayoutRendererModalService = inject(DynamicLayoutRendererModalService);

  // Feature toggle system để bật/tắt các test sections
  testFeatures: { [feature: string]: boolean } = {
    // Enable ResponsiveModalService.openWithPopover test sections
    'popoverModalTest': true,          // 🎯 Popover Modal Test
    'popoverPositioningTest': true,    // 📍 Popover Positioning Test
    'popoverResponsiveTest': true,     // 📱 Popover Responsive Test

    // Enable ResponsiveModalService.openWithMatMenu test sections
    'matMenuModalTest': true,          // 🎯 Mat Menu Modal Test
    'matMenuPositioningTest': true,    // 📍 Mat Menu Positioning Test
    'matMenuResponsiveTest': true,     // 📱 Mat Menu Responsive Test

    // Enable Threads-like Mat-Menu Animation test
    'threadsMenuAnimation': true,      // 🎭 Threads-like Animation Test

    // Enable ResponsiveModalService Threads Animation Integration test
    'responsiveModalThreadsAnimation': true, // 🚀 ResponsiveModalService Integration Test

    // Enable StandardBottomSheetService Threads Animation Integration test
    'standardBottomSheetThreadsAnimation': true, // 🎭 StandardBottomSheetService Integration Test

    // Enable ResponsiveModalService.openWithOverlay test sections
    'multiplePositionTest': true,      // 🎯 Multiple Trigger Position Test
    'positioningOptionsTest': true,    // 📍 Positioning Options Test (9 Alignment Options)

    // Disable tất cả các test sections khác
    'dynamicLayoutRenderer': false,
    'settingsListComponent': false,
    'dynamicLayoutBuilder': false,
    'productModifiersBottomSheet': false,
    'batchDialog': false,
    'inputPlace': false,
    'testDialogs': false,
    'additionalCostDialog': false,
    'taxDialog': false,
    'selectAdditionalCostsDialog': false,
    'qualityCheckRejectDialog': false,
    'simpleNoteDialog': false,
    'noteModal': false,
    'promotionModal': false,
    'variantFormModal': false,
    'productFilterDialog': false,
    'serialNumberDialog': false,
    'confirmModal': false,
    'fieldPermissionModal': false,
    'fieldPropertiesModal': false,
    'standardDialogComponent': false,
    'standardBottomSheetService': false,
    'createLayoutModal': false,
    'responsiveModalService': false,
    'dynamicLayoutRendererModal': false
  };

  // Test layout configs for Dynamic Layout Builder - sử dụng simple mock data
  testLayoutBuilderConfig = {
    layoutId: simpleMockLayouts[0]._id || 'layout-1',
    layouts: simpleMockLayouts,
    defaultLayoutConfig: simpleMockLayouts[0], // Simple layout làm default
    currentPermissionProfileId: 'admin',
    permissionProfiles: [
      { _id: 'admin', name: 'Administrator', permission: 'read_write' as const },
      { _id: 'manager', name: 'Manager', permission: 'read_write' as const },
      { _id: 'user', name: 'Standard User', permission: 'read' as const },
      { _id: 'guest', name: 'Guest User', permission: 'none' as const }
    ]
  };

  // Test config cho DynamicLayoutRenderer
  testLayoutRendererConfig = mockLayoutRendererConfig;
  private translateService = inject(TranslateService);
  private mixedPaymentModalService = inject(MixedPaymentModalService);
  private productModifierFormModalService = inject(OrderItemModifierModalService);
  private orderItemVariantUnitSelectionModalService = inject(OrderItemVariantUnitSelectionModalService);
  private noteModalService = inject(NoteModalService);
  private promotionModalService = inject(PromotionModalService);
  private variantFormModalService = inject(VariantFormModalService);
  private simpleNoteModalService = inject(SimpleNoteModalService);
  private fieldPermissionModalService = inject(FieldPermissionModalService);
  private taxFormModalService = inject(TaxFormModalService);
  private confirmModalService = inject(ConfirmModalService);
  private standardDialogService = inject(StandardDialogService);
  private standardBottomSheetService = inject(StandardBottomSheetService);
  private createLayoutModalService = inject(CreateLayoutModalService);

  // Dữ liệu mẫu cho ProductSelector
  productSelectorConfig = mockProductSelectorConfig;

  selectedProducts: OrderItemBaseDetails[] = [];

  // Mock data cho Field Filters
  mockFields = mockCustomerFields;

  // Threads Menu Animation Status
  threadsMenuStatus: { [key: string]: string } = {
    basic: 'Closed',
    enhanced: 'Closed'
  };

  // ResponsiveModalService Threads Animation Status
  responsiveModalThreadsStatus: { [key: string]: string } = {
    integration: 'Ready',
    lastTest: 'None',
    animationType: 'Basic',
    mode: 'Auto'
  };

  // StandardBottomSheetService Threads Animation Status
  standardBottomSheetThreadsStatus: { [key: string]: string } = {
    integration: 'Ready',
    lastTest: 'None',
    appDrawerFound: 'Unknown',
    animationActive: 'No'
  };

  // Mock data cho Settings
  settingsConfig: SettingsListConfig = {
    settings: mockAllSettings,
    values: { ...mockDefaultSettingValues },
    showDividers: true,
    showCard: false,
    containerClass: '',
    autoSave: false, // Thay đổi thành false để test save/cancel functionality
    showSaveButtons: true
  };

  // ViewChild cho custom templates
  @ViewChild('customTitleTemplate', { static: true }) customTitleTemplate!: TemplateRef<any>;
  @ViewChild('customActionsTemplate', { static: true }) customActionsTemplate!: TemplateRef<any>;

  ngOnInit(): void {
  }

  /**
   * Xử lý khi setting thay đổi
   */
  onSettingChange(event: SettingChangeEvent): void {
    console.log('Setting changed:', event);

    // Hiển thị thông báo về thay đổi setting
    this.flashMessageService.success(
      this.translateService.instant('FLASH_MESSAGES.SUCCESS.SETTINGS.SETTING_UPDATED'),
      {
        description: `${event.setting.name}: ${this.getSettingValueDisplay(event)}`
      }
    );
  }

  /**
   * Xử lý khi lưu settings
   */
  onSettingsSave(event: SettingsSaveEvent): void {
    console.log('Settings saved:', event);

    // Hiển thị thông báo lưu thành công
    this.flashMessageService.success(
      this.translateService.instant('SETTINGS.SAVE_SUCCESS'),
      {
        description: `Đã lưu ${Object.keys(event.changedSettings).length} cài đặt`
      }
    );
  }

  /**
   * Xử lý khi hủy thay đổi settings
   */
  onSettingsCancel(): void {
    console.log('Settings cancelled');

    // Hiển thị thông báo hủy thành công
    this.flashMessageService.info(
      this.translateService.instant('SETTINGS.CANCEL_SUCCESS')
    );
  }

  /**
   * Xử lý khi toggle auto save
   */
  onAutoSaveToggle(event: AutoSaveToggleEvent): void {
    console.log('Auto save toggled:', event);

    // Hiển thị thông báo thay đổi auto save
    const messageKey = event.enabled ? 'SETTINGS.AUTO_SAVE_ENABLED' : 'SETTINGS.AUTO_SAVE_DISABLED';
    this.flashMessageService.info(
      this.translateService.instant(messageKey)
    );
  }

  /**
   * Lấy text hiển thị cho giá trị setting
   */
  private getSettingValueDisplay(event: SettingChangeEvent): string {
    if (event.setting.type === 'toggle') {
      return event.value ? 'Bật' : 'Tắt';
    }

    if (event.setting.type === 'select' || event.setting.type === 'radio') {
      const option = event.setting.options?.find(opt => opt.value === event.value);
      return option?.label || event.value;
    }

    return String(event.value);
  }

  async openProductModifiersSheet(): Promise<void> {
    // Tạo mock dữ liệu cho OrderItemBaseDetails
    const orderItem: OrderItemBaseDetails = {
      quantity: 1,
      product: {
        productId: 'prod1',
        cost: 150000,
        name: 'Phở bò tái lăn',
        price: 200000,
        variant: {
          variantId: 'v1',
          attributes: [{ name: 'size', value: 'L' }, { name: 'color', value: 'Xanh' }]
        }
      }
    };

    try {
      // Mở modal chọn modifier sử dụng service
      const result = await this.productModifierFormModalService.open({
        list: mockProductModifierGroupList,
        data: orderItem
      });

      if (result) {
        console.log('Product modifier modal result:', result);
      }
    } catch (error: any) {
      // Thay thế console.error bằng FlashMessageService cho test component
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: `Lỗi khi mở modal chọn modifier: ${error.message}`
        }
      );
    }
  }

  async openMixedPaymentDialog(): Promise<void> {
    // Sample data
    const orderPayment: OrderPayment = {
      totalAmount: 1625000,
      payments: [],
      paidAmount: 0,
      remainingAmount: 1625000,
      paymentStatus: 'unpaid'
    };

    try {
      const result = await this.mixedPaymentModalService.open(orderPayment);
      if (result) {
        console.log('Modal result:', result);
      }
    } catch (error: any) {
      // Thay thế console.error bằng FlashMessageService cho test component
      this.flashMessageService.error(
        this.translateService.instant('FLASH_MESSAGES.ERRORS.API.FAILED'),
        {
          description: `Error opening mixed payment modal: ${error.message}`
        }
      );
    }
  }

  openVariantSelector() {
    // Mở modal chọn variant và unit sử dụng service
    this.orderItemVariantUnitSelectionModalService.open({
      variants: mockVariantList,
      currentValue: {
        variant: mockVariantList[1], // Chọn variant thứ 2 (L, Đỏ)
        unit: mockProductUnits[1] // Chọn đơn vị vỉ
      },
      units: mockProductUnits
    }).then(result => {
      if (result) {
        console.log('Selected variant:', result.variant);
        console.log('Selected unit:', result.unit);
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn variant và unit:', error);
    });
  }

  /**
   * Mở modal form thuộc tính sản phẩm
   */
  async openVariantFormModal(): Promise<void> {
    try {
      const result = await this.variantFormModalService.open({
        valueSuggestions: ['S', 'M', 'L', 'XL', 'Đỏ', 'Xanh', 'Đen', 'Trắng']
      });

      if (result) {
        console.log('Variant form modal result:', result);
        alert(`Thuộc tính đã được tạo: ${result.name} với ${result.values.length} giá trị`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal form thuộc tính sản phẩm:', error);
    }
  }

  openProductForm(): void {
    console.log('Open product form dialog');
    // Placeholder for opening the product form dialog
    // Trong triển khai thực tế, sẽ mở dialog product-form
  }

  openBatchDialog(): void {
    this.responsiveModalService.open<
      BatchData,
      BatchData,
      BatchModalComponent
    >(
      BatchModalComponent,
      {
        data: {
          batchNumber: '',
          manufacturingDate: new Date(),
          expiryDate: null,
          quantity: null
        }
      }
    ).then(result => {
      if (result) {
        console.log('Batch dialog result:', result);
      }
    }).catch(error => {
      console.error('Error in batch modal:', error);
    });
  }

  openCategoryProductDialog(): void {
    this.responsiveModalService
      .open<CategoryProductModalData, ProductListItem[], CategoryProductModalComponent>(
        CategoryProductModalComponent,
        { width: '450px', disableClose: false }
      )
      .then(result => {
        console.log('Selected products:', result);
        if (result) {
          alert(`Đã chọn ${result.length} sản phẩm từ nhóm hàng`);
        }
      })
      .catch(() => {});
  }

  /**
   * Mở dialog thêm/sửa chi phí
   */
  async openAdditionalCostDialog(isEdit: boolean = false): Promise<void> {
    // Khởi tạo dữ liệu mẫu chi phí
    const mockCost: ImportAdditionalCost = {
      _id: 'cost1',
      name: 'Phí vận chuyển',
      costValue: {
        type: 'fixed',
        value: 250000
      },
      paidToSupplier: true,
      allocateToItems: true,
      isActive: true,
      autoAddToPurchaseOrder: false,
      refundOnReturn: true,
      tax: {
        rate: 10,
        amount: 25000
      }
    };

    const additionalCostModalService = inject(AdditionalCostModalService);
    const result = await additionalCostModalService.open({
      cost: isEdit ? mockCost : undefined,
      subTotal: 5000000
    });

    if (result) {
      console.log('Additional cost dialog result:', result);
      alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} chi phí: ${result.name}`);
    }
  }

  // Test SelectAdditionalCostsDialog
  openSelectAdditionalCostsDialog(): void {
    // // Khởi tạo dữ liệu mẫu đã chọn
    // const selectedCosts: ImportAdditionalCost[] = [
    //   mockImportAdditionalCosts[0],
    //   mockImportAdditionalCosts[2]
    // ];

    // const dialogRef = this.dialog.open(SelectAdditionalCostsDialogComponent, {
    //   width: '900px',
    //   data: {
    //     items: mockImportAdditionalCosts,
    //     current: selectedCosts,
    //     subTotal: 5000000
    //   }
    // });

    // dialogRef.afterClosed().subscribe((result: ImportAdditionalCost[] | undefined) => {
    //   if (result) {
    //     console.log('Selected additional costs:', result);
    //     alert(`Đã chọn ${result.length} chi phí khác`);
    //   }
    // });
  }

  // Test TaxFormModal
  async openTaxDialog(isEdit: boolean = false): Promise<void> {
    const mockTax: TaxInfo = {
      type: 'VAT',
      rate: 10,
      amount: 500000
    };

    try {
      const result = await this.taxFormModalService.open({
        tax: isEdit ? mockTax : undefined,
        subTotal: 5000000
      });

      if (result) {
        console.log('Tax modal result:', result);
        alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} thuế: ${result.type} với số tiền ${result.amount} VND`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thuế:', error);
    }
  }

  // Test QualityCheckRejectModal
  async openQualityCheckRejectDialog(isEdit: boolean = false): Promise<void> {
    // Tạo mock data cho GoodsReceiptItem
    const mockItems = [
      {
        _id: 'item1',
        product: {
          productId: 'prod1',
          name: 'Áo thun nam',
          sku: 'ATN123',
          price: 150000,
          cost: 100000
        },
        quantityReceived: 50,
        price: 150000,
        subTotal: 7500000,
        total: 7500000,
        inventoryTransactionId: 'inv1'
      },
      {
        _id: 'item2',
        product: {
          productId: 'prod2',
          name: 'Quần jean nữ',
          sku: 'QJN456',
          price: 320000,
          cost: 200000
        },
        quantityReceived: 30,
        price: 320000,
        subTotal: 9600000,
        total: 9600000,
        inventoryTransactionId: 'inv2'
      },
      {
        _id: 'item3',
        product: {
          productId: 'prod3',
          name: 'Áo khoác nam',
          sku: 'AKN789',
          price: 450000,
          cost: 300000
        },
        quantityReceived: 20,
        price: 450000,
        subTotal: 9000000,
        total: 9000000,
        inventoryTransactionId: 'inv3'
      }
    ];

    // Tạo mock data cho RejectedItem nếu đang chỉnh sửa
    const mockRejectedItem: RejectedItem | undefined = isEdit ? {
      _id: 'item1',
      quantity: 5,
      reason: 'Hàng bị lỗi màu, không đúng với mẫu'
    } : undefined;

    const modalData: QualityCheckRejectModalData = {
      items: mockItems,
      rejectedItem: mockRejectedItem
    };

    const result = await this.responsiveModalService.open<
      QualityCheckRejectModalData,
      RejectedItem | undefined,
      QualityCheckRejectModalComponent
    >(QualityCheckRejectModalComponent, {
      data: modalData,
      width: '500px',
      maxWidth: '95vw'
    });

    if (result) {
      console.log('Quality check reject modal result:', result);
      // Tìm tên sản phẩm để hiển thị
      const selectedItem = mockItems.find(item => item._id === result._id);
      const productName = selectedItem ? selectedItem.product.name : 'Sản phẩm';
      alert(`Đã ${isEdit ? 'cập nhật' : 'thêm'} sản phẩm bị từ chối: ${productName} - Số lượng: ${result.quantity}`);
    }
  }

  /**
   * Mở SimpleNoteModal để thêm/sửa ghi chú
   * @param existingNote Ghi chú hiện tại (nếu có)
   */
  async openSimpleNoteDialog(existingNote: string = ''): Promise<void> {
    try {
      // Mở modal với ghi chú hiện tại (nếu có)
      const title = existingNote ? 'COMMON.EDIT_NOTE' : 'COMMON.ADD_NOTE';
      const result = await this.simpleNoteModalService.open(title, existingNote);

      // Xử lý kết quả
      if (result !== undefined) {
        console.log('Simple note modal result:', result);
        alert(`Ghi chú đã được ${existingNote ? 'cập nhật' : 'thêm'}: ${result}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú đơn giản:', error);
    }
  }

  /**
   * Mở NoteModal để thêm/sửa ghi chú
   */
  async openNoteModal(): Promise<void> {
    try {
      const result = await this.noteModalService.open({
        internalNote: 'Ghi chú nội bộ mẫu',
        note: 'Ghi chú công khai mẫu'
      });

      if (result) {
        console.log('Note modal result:', result);
        alert(`Ghi chú đã được cập nhật:\nNội bộ: ${result.internalNote}\nCông khai: ${result.note}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú:', error);
    }
  }

  /**
   * Mở PromotionModal để thêm/sửa khuyến mãi
   */
  async openPromotionModal(): Promise<void> {
    try {
      const result = await this.promotionModalService.open({
        totalAmount: 500000,
        discountType: 'amount',
        discountValue: 50000,
        promotionName: 'Khuyến mãi mẫu',
        finalAmount: 450000
      });

      if (result) {
        console.log('Promotion modal result:', result);
        alert(`Khuyến mãi đã được cập nhật:\nLoại: ${result.discountType}\nGiá trị: ${result.discountValue}\nTên: ${result.promotionName}\nThành tiền: ${result.finalAmount}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal khuyến mãi:', error);
    }
  }

  /**
   * Mở ConfirmModal để xác nhận hành động
   */
  async openConfirmDialog(): Promise<void> {
    try {
      const result = await this.confirmModalService.confirm({
        title: 'COMMON.CONFIRM_ACTION',
        message: 'COMMON.CONFIRM_ACTION_MESSAGE',
        confirmText: 'COMMON.YES',
        cancelText: 'COMMON.NO',
        confirmColor: 'primary'
      });

      console.log('Confirm modal result:', result);
      alert(`Kết quả xác nhận: ${result ? 'Đồng ý' : 'Hủy bỏ'}`);
    } catch (error) {
      console.error('Lỗi khi mở modal xác nhận:', error);
    }
  }

  /**
   * Mở FieldPermissionModal để thiết lập quyền truy cập field
   */
  async openFieldPermissionModal(): Promise<void> {
    try {
      const result = await this.fieldPermissionModalService.openWithMockData(
        'Customer Name', // Tên field
        5 // Số lượng profiles
      );

      if (result) {
        console.log('Field permission modal result:', result);
        const summary = result.map(p => `${p.name}: ${p.permission}`).join('\n');
        alert(`Quyền truy cập đã được cập nhật:\n${summary}`);
      } else {
        alert('Thiết lập quyền đã bị hủy!');
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thiết lập quyền field:', error);
    }
  }

  /**
   * Mở FieldPropertiesModal để chỉnh sửa thuộc tính field
   */
  async openFieldPropertiesModal(fieldType: string = 'text'): Promise<void> {
    try {
      // Tạo mock data cho CustomField
      const mockField: any = {
        _id: 1,
        label: 'Customer Name',
        type: fieldType,
        value: fieldType === 'checkbox' ? false : (fieldType === 'multi-picklist' ? [] : ''),
        isPublic: false,
        isRequired: true,
        tooltip: 'Enter customer full name',
        constraints: this.getMockConstraints(fieldType)
      };

      const modalData: FieldPropertiesData = {
        field: mockField,
        availableSearchModules: [
          { _id: 'sales_quotes', name: 'Sales Quotes' },
          { _id: 'contacts', name: 'Contacts' },
          { _id: 'transactions', name: 'Transactions' }
        ]
      };

      const result = await this.responsiveModalService.open<
        FieldPropertiesData,
        any,
        FieldPropertiesModalComponent
      >(FieldPropertiesModalComponent, {
        data: modalData,
        width: '600px',
        maxWidth: '95vw',
        maxHeight: '90vh'
      });

      if (result) {
        console.log('Field properties modal result:', result);
        alert(`Thuộc tính field đã được cập nhật:\nNhãn: ${result.field?.label}\nLoại: ${result.field?.type}\nBắt buộc: ${result.field?.isRequired ? 'Có' : 'Không'}`);
      } else {
        alert('Chỉnh sửa thuộc tính đã bị hủy!');
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chỉnh sửa thuộc tính field:', error);
    }
  }

  /**
   * Tạo mock constraints dựa trên field type
   */
  private getMockConstraints(fieldType: string): any {
    switch (fieldType) {
      case 'text':
      case 'phone':
      case 'url':
        return { maxLength: 255, unique: false };
      case 'number':
        return { maxDigits: 9 };
      case 'textarea':
        return { textType: 'small', maxLength: 2000 };
      case 'picklist':
        return {
          picklistOptions: [
            { label: 'Option 1', value: 'Option 1' },
            { label: 'Option 2', value: 'Option 2' },
            { label: 'Option 3', value: 'Option 3' }
          ],
          sortOrder: 'input',
          defaultValue: 'Option 1'
        };
      case 'multi-picklist':
        return {
          picklistOptions: [
            { label: 'Red', value: 'Red' },
            { label: 'Green', value: 'Green' },
            { label: 'Blue', value: 'Blue' },
            { label: 'Yellow', value: 'Yellow' }
          ],
          sortOrder: 'alphabetical',
          defaultValue: ['Red', 'Blue']
        };
      case 'search':
        return { searchModule: 'sales_quotes' };
      case 'user':
        return { userType: 'single' };
      case 'upload-file':
        return { allowMultipleFiles: false, maxFiles: 1 };
      case 'upload-image':
        return { maxImages: 3 };
      case 'currency':
        return { maxDigits: 9, decimalPlaces: 2, rounding: 'normal' };
      case 'decimal':
        return { maxDigits: 16, decimalPlaces: 2, useNumberSeparator: true };
      case 'checkbox':
        return { enableByDefault: false };
      default:
        return {};
    }
  }

  /**
   * Mở ProductFilterDialog để lọc sản phẩm
   */
  openProductFilterDialog(): void {
    // Giả định đã chọn kho với ID 'wh1'
    const warehouseId = 'wh1';

    // Trạng thái bộ lọc hiện tại (giả định đã chọn một số danh mục)
    const currentFilter = {
      category: ['cat1', 'cat3'], // Đã chọn danh mục Quần áo người lớn và Giày dép
      warehouseLocation: null // Chưa chọn vị trí kho
    };

    // Mở modal
    this.responsiveModalService.open<
      ProductFilterModalData,
      ProductFilterResult,
      ProductFilterModalComponent
    >(
      ProductFilterModalComponent,
      {
        data: {
          current: currentFilter,
          warehouseId: warehouseId
        }
      }
    ).then((result: ProductFilterResult | undefined) => {
      if (result) {
        console.log('Product filter modal result:', result);
        alert(`Đã lọc được ${result.products.length} sản phẩm`);
      }
    }).catch(error => {
      console.error('Error opening product filter modal:', error);
    });
  }

  /**
   * Mở SerialNumberDialog để quản lý số serial
   */
  openSerialNumberDialog(): void {
    // Tạo một sản phẩm mẫu
    const mockProduct: ProductListItem = mockProductList[0];

    // Tạo danh sách serial mẫu
    const mockSerials: EmbeddedProductSerial[] = [
      { _id: 'serial1', serialNumber: `${mockProduct.sku}-SN001`, status: 'in_stock' },
      { _id: 'serial2', serialNumber: `${mockProduct.sku}-SN002`, status: 'in_stock' },
      { _id: 'serial3', serialNumber: `${mockProduct.sku}-SN003`, status: 'sold' },
      { _id: 'serial4', serialNumber: `${mockProduct.sku}-SN004`, status: 'damaged' },
      { _id: 'serial5', serialNumber: `${mockProduct.sku}-SN005`, status: 'in_transit' }
    ];

    // Mở dialog
    // const dialogRef = this.dialog.open(SerialNumberDialogComponent, {
    //   width: '600px',
    //   data: {
    //     product: mockProduct,
    //     serials: mockSerials
    //   }
    // });

    // // Xử lý kết quả khi đóng dialog
    // dialogRef.afterClosed().subscribe((result: EmbeddedProductSerial[] | undefined) => {
    //   if (result) {
    //     console.log('Serial number dialog result:', result);
    //     const inStockCount = result.filter(serial =>
    //       serial.status === 'in_stock' || serial.status === 'assigned'
    //     ).length;
    //     alert(`Đã cập nhật ${result.length} số serial, số lượng thực tế: ${inStockCount}`);
    //   }
    // });
  }

  /**
   * Xử lý khi filter thay đổi
   * @param event - Filter change event
   */
  onFieldFilterChange(event: FilterChangeEvent): void {
    console.log('Field filter changed:', event);

    // Tìm field tương ứng
    // const field = this.mockFields.find(f => f._id === event.fieldId);
    // if (field) {
    //   const status = event.isActive ? 'activated' : 'deactivated';
    //   console.log(`Filter for "${field.label}" ${status}`);

    //   if (event.isActive && event.filterValue) {
    //     console.log('Filter value:', event.filterValue);
    //   }
    // }
  }

  /**
   * Xử lý khi các filters được áp dụng
   * @param activeFilters - Danh sách các filter đang active
   */
  onFiltersApplied(activeFilters: FieldFilter[]): void {
    console.log('Active filters applied:', activeFilters);

    if (activeFilters.length > 0) {
      const filterSummary = activeFilters.map(filter => {
        const operator = filter.filterValue?.operator || 'unknown';
        const value = this.getFilterValueDisplay(filter.filterValue);
        return `${filter.field.label}: ${operator}${value ? ` (${value})` : ''}`;
      }).join(', ');

      console.log(`Applied ${activeFilters.length} filter(s): ${filterSummary}`);

      // Hiển thị thông báo
      alert(`✅ Đã áp dụng ${activeFilters.length} bộ lọc:\n${filterSummary}`);
    } else {
      console.log('No filters applied');
    }
  }

  /**
   * Xử lý khi filters được reset
   */
  onFiltersReset(): void {
    console.log('Filters have been reset');
    alert('🔄 Đã đặt lại tất cả bộ lọc');
  }

  /**
   * Helper method để hiển thị giá trị filter
   * @param filterValue - Giá trị filter
   * @returns String representation của giá trị
   */
  private getFilterValueDisplay(filterValue: any): string {
    if (!filterValue) return '';

    if (filterValue.value !== undefined) {
      return String(filterValue.value);
    }

    if (filterValue.values && Array.isArray(filterValue.values)) {
      return filterValue.values.join(', ');
    }

    if (filterValue.minValue !== undefined && filterValue.maxValue !== undefined) {
      return `${filterValue.minValue} - ${filterValue.maxValue}`;
    }

    if (filterValue.timeValue !== undefined && filterValue.timeUnit) {
      return `${filterValue.timeValue} ${filterValue.timeUnit}`;
    }

    return '';
  }

  // ==================== STANDARD DIALOG TESTS ====================

  /**
   * Test StandardDialog với cấu hình cơ bản - API mới
   */
  async openBasicStandardDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Cơ Bản - API Mới',
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          message: 'Đây là nội dung dialog cơ bản với API mới',
          testData: { timestamp: new Date(), version: '2.0' }
        }
      });

      if (result) {
        console.log('Basic dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action} - Data: ${JSON.stringify(testResult.data)}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở basic dialog:', error);
    }
  }

  /**
   * Test StandardDialog với confirm dialog - Tạm thời disable
   */
  async openConfirmStandardDialog(): Promise<void> {
    try {
      // TODO: Implement sau khi có SimpleConfirmComponent
      alert('Confirm dialog chưa được implement với API mới');
    } catch (error) {
      console.error('Lỗi khi mở confirm dialog:', error);
    }
  }

  /**
   * Test StandardDialog với alert dialog - Tạm thời disable
   */
  async openAlertStandardDialog(): Promise<void> {
    try {
      // TODO: Implement sau khi có SimpleAlertComponent
      alert('Alert dialog chưa được implement với API mới');
    } catch (error) {
      console.error('Lỗi khi mở alert dialog:', error);
    }
  }

  /**
   * Test StandardDialog với custom title template - API mới
   */
  async openCustomTitleDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: this.customTitleTemplate,
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          userName: 'Nguyễn Văn A',
          action: 'xóa tài khoản',
          message: 'Dialog với custom title template'
        }
      });

      if (result) {
        console.log('Custom title dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Dialog với title tùy chỉnh: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở custom title dialog:', error);
    }
  }

  /**
   * Test StandardDialog với custom actions template - API mới
   */
  async openCustomActionsDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Với Actions Tùy Chỉnh',
        actions: {
          useDefault: false,
          customActionsTemplate: this.customActionsTemplate
        },
        enableClose: true,
        data: {
          options: ['Lưu', 'Lưu và Tiếp tục', 'Hủy'],
          message: 'Dialog với custom actions template'
        }
      });

      if (result) {
        console.log('Custom actions dialog result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'custom' && testResult.customAction) {
          alert(`Hành động tùy chỉnh: ${testResult.customAction.name}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      }
    } catch (error) {
      console.error('Lỗi khi mở custom actions dialog:', error);
    }
  }

  /**
   * Test StandardDialog không cho phép đóng - API mới
   */
  async openNoCloseDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Không Thể Đóng',
        actions: {
          useDefault: true
        },
        enableClose: false,
        disableClose: true,
        data: {
          message: 'Dialog này chỉ có thể đóng bằng nút Hủy hoặc Xác nhận',
          testFeature: 'No close button'
        }
      });

      if (result) {
        console.log('No close dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở no close dialog:', error);
    }
  }

  /**
   * Test responsive dialog với width tùy chỉnh - API mới
   */
  async openResponsiveDialog(): Promise<void> {
    try {
      const result = await this.standardDialogService.openAsync(SimpleTestComponent, {
        title: 'Dialog Responsive',
        actions: {
          useDefault: true
        },
        enableClose: true,
        width: '800px',
        data: {
          message: 'Dialog này có width cố định 800px và sẽ responsive trên mobile',
          content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
          testFeature: 'Responsive width'
        }
      });

      if (result) {
        console.log('Responsive dialog result:', result);
        const testResult = result as TestModalResult;
        alert(`Dialog responsive: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở responsive dialog:', error);
    }
  }

  /**
   * Handler cho custom actions trong template
   */
  onCustomSave(onCustomAction: Function): void {
    onCustomAction('save', { timestamp: new Date() });
  }

  onCustomSaveAndContinue(onCustomAction: Function): void {
    onCustomAction('save_continue', { timestamp: new Date(), continue: true });
  }

  onCustomCancel(onClose: Function): void {
    onClose();
  }

  // ==================== STANDARD BOTTOM SHEET TESTS ====================

  /**
   * Test StandardBottomSheet với cấu hình cơ bản - API mới
   */
  async openBasicStandardBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Cơ Bản - API Mới',
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          message: 'Đây là nội dung bottom sheet cơ bản với API mới',
          testData: { timestamp: new Date(), version: '2.0', type: 'bottom-sheet' }
        }
      });

      if (result) {
        console.log('Basic bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action} - Data: ${JSON.stringify(testResult.data)}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở basic bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet với custom title template - API mới
   */
  async openCustomTitleBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: this.customTitleTemplate,
        actions: {
          useDefault: true
        },
        enableClose: true,
        data: {
          userName: 'Nguyễn Văn B',
          action: 'xem chi tiết sản phẩm',
          message: 'Bottom sheet với custom title template'
        }
      });

      if (result) {
        console.log('Custom title bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Bottom sheet với title tùy chỉnh: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở custom title bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet với custom actions template - API mới
   */
  async openCustomActionsBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Với Actions Tùy Chỉnh',
        actions: {
          useDefault: false,
          customActionsTemplate: this.customActionsTemplate
        },
        enableClose: true,
        data: {
          options: ['Lưu', 'Lưu và Tiếp tục', 'Hủy'],
          message: 'Bottom sheet với custom actions template'
        }
      });

      if (result) {
        console.log('Custom actions bottom sheet result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'custom' && testResult.customAction) {
          alert(`Hành động tùy chỉnh: ${testResult.customAction.name}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      }
    } catch (error) {
      console.error('Lỗi khi mở custom actions bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet không cho phép đóng - API mới
   */
  async openNoCloseBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Không Thể Đóng',
        actions: {
          useDefault: true
        },
        enableClose: false,
        disableClose: true,
        data: {
          message: 'Bottom sheet này chỉ có thể đóng bằng nút Hủy hoặc Xác nhận',
          testFeature: 'No close button'
        }
      });

      if (result) {
        console.log('No close bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Kết quả: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở no close bottom sheet:', error);
    }
  }

  /**
   * Test StandardBottomSheet với backdrop disabled
   */
  async openNoBackdropBottomSheet(): Promise<void> {
    try {
      const result = await this.standardBottomSheetService.openAsync(SimpleBottomSheetTestComponent, {
        title: 'Bottom Sheet Không Có Backdrop',
        actions: {
          useDefault: true
        },
        enableClose: true,
        hasBackdrop: false,
        data: {
          message: 'Bottom sheet này không có backdrop, không thể đóng bằng click outside',
          testFeature: 'No backdrop'
        }
      });

      if (result) {
        console.log('No backdrop bottom sheet result:', result);
        const testResult = result as TestModalResult;
        alert(`Bottom sheet không backdrop: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở no backdrop bottom sheet:', error);
    }
  }

  // ===== CreateLayoutModal Tests =====

  /**
   * Test CreateLayoutModal với ResponsiveModalService
   */
  async openCreateLayoutModal(): Promise<void> {
    try {
      const existingLayouts = ['Fashion Layout', 'Electronics Layout', 'Food Layout'];

      const result = await this.createLayoutModalService.openWithValidation(
        existingLayouts,
        'New Layout', // default name
        'Layout được tạo từ test' // default description
      );

      if (result) {
        console.log('CreateLayoutModal result:', result);
        this.flashMessageService.success(
          `Layout "${result.name}" đã được tạo thành công!`
        );
      } else {
        console.log('CreateLayoutModal cancelled');
        this.flashMessageService.info('Hủy tạo layout');
      }
    } catch (error) {
      console.error('Lỗi khi mở CreateLayoutModal:', error);
      this.flashMessageService.error('Có lỗi xảy ra khi mở modal tạo layout');
    }
  }

  /**
   * Test CreateLayoutModal không có validation
   */
  async openCreateLayoutModalSimple(): Promise<void> {
    try {
      const result = await this.createLayoutModalService.open();

      if (result) {
        console.log('CreateLayoutModal simple result:', result);
        this.flashMessageService.success(
          `Layout "${result.name}" đã được tạo thành công!`
        );
      } else {
        console.log('CreateLayoutModal simple cancelled');
        this.flashMessageService.info('Hủy tạo layout');
      }
    } catch (error) {
      console.error('Lỗi khi mở CreateLayoutModal simple:', error);
      this.flashMessageService.error('Có lỗi xảy ra khi mở modal tạo layout');
    }
  }

  // ===== ResponsiveModalService Tests =====

  /**
   * Test ResponsiveModalService với auto-selection
   */
  async openResponsiveModal(): Promise<void> {
    try {
      const modalConfig: ResponsiveModalConfig<{ message: string }> = {
        title: 'Responsive Modal Test',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: { message: 'Test ResponsiveModalService với auto-selection' }
      };

      const result = await this.responsiveModalService.open(SimpleBottomSheetTestComponent, modalConfig);

      if (result) {
        console.log('Responsive modal result:', result);
        const testResult = result as TestModalResult;
        alert(`Responsive modal result: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở responsive modal:', error);
    }
  }

  /**
   * Test ResponsiveModalService force dialog mode
   */
  async openForceDialogModal(): Promise<void> {
    try {
      const result = await this.responsiveModalService.open<
        SimpleBottomSheetTestData,
        SimpleBottomSheetTestResult,
        SimpleBottomSheetTestComponent
      >(
        SimpleBottomSheetTestComponent,
        {
          title: 'Force Dialog Mode',
          enableClose: true,
          actions: {
            useDefault: true
          },
          data: { message: 'Test force dialog mode', testData: { id: 2, name: 'Dialog Test' } },
          width: '500px'
        },
        'dialog' // Force dialog mode
      );

      if (result) {
        console.log('Force dialog modal result:', result);
        alert(`Force dialog modal result: ${result.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở force dialog modal:', error);
    }
  }

  /**
   * Test ResponsiveModalService force bottom sheet mode
   */
  async openForceBottomSheetModal(): Promise<void> {
    try {
      const modalConfig: ResponsiveModalConfig<{ message: string }> = {
        title: 'Force Bottom Sheet Mode',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: { message: 'Test force bottom sheet mode' }
      };

      const result = await this.responsiveModalService.open(
        SimpleBottomSheetTestComponent,
        modalConfig,
        'bottom-sheet' // Force bottom sheet mode
      );

      if (result) {
        console.log('Force bottom sheet modal result:', result);
        const testResult = result as TestModalResult;
        alert(`Force bottom sheet modal result: ${testResult.action}`);
      }
    } catch (error) {
      console.error('Lỗi khi mở force bottom sheet modal:', error);
    }
  }

  /**
   * Test ListColumnSelectorModal với force bottom sheet mode
   */
  async openListColumnSelectorBottomSheet(): Promise<void> {
    try {
      // Tạo mock data cho ListColumnConfig theo interface đúng
      const allColumns = [
        { _id: 'id', label: 'ID', sortable: true },
        { _id: 'name', label: 'Tên sản phẩm', sortable: true },
        { _id: 'price', label: 'Giá', sortable: true },
        { _id: 'category', label: 'Danh mục', sortable: true },
        { _id: 'brand', label: 'Thương hiệu', sortable: true },
        { _id: 'stock', label: 'Tồn kho', sortable: true },
        { _id: 'status', label: 'Trạng thái', sortable: false }
      ];

      const regularColumns = [
        { _id: 'id', label: 'ID', sortable: true },
        { _id: 'name', label: 'Tên sản phẩm', sortable: true },
        { _id: 'price', label: 'Giá', sortable: true },
        { _id: 'stock', label: 'Tồn kho', sortable: true }
      ];

      const pinnedColumns = [
        { _id: 'status', label: 'Trạng thái', sortable: false }
      ];

      const mockColumnConfig: ListColumnConfig = {
        allColumns,
        regularColumns,
        pinnedColumns,
        maxPinnedColumns: 3
      };

      console.log('Opening ListColumnSelector with bottom sheet mode...');

      const modalConfig: ResponsiveModalConfig<ListColumnConfig> = {
        title: 'Chọn cột hiển thị - Bottom Sheet',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: mockColumnConfig
      };

      const result = await this.responsiveModalService.open(
        ListColumnSelectorModalComponent,
        modalConfig,
        'bottom-sheet' // Force bottom sheet mode để test
      );

      if (result) {
        console.log('ListColumnSelector bottom sheet result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'confirm' && testResult.data) {
          const columnData = testResult.data as { regularColumns: { label: string }[]; pinnedColumns: { label: string }[] };
          const totalColumns = columnData.regularColumns.length + columnData.pinnedColumns.length;
          alert(`✅ Đã cập nhật cột hiển thị!\nSố cột hiển thị: ${totalColumns}\nCột thường: ${columnData.regularColumns.map(col => col.label).join(', ')}\nCột ghim: ${columnData.pinnedColumns.map(col => col.label).join(', ')}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      } else {
        alert('❌ Đã hủy chọn cột');
      }
    } catch (error) {
      console.error('Lỗi khi mở ListColumnSelector bottom sheet:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test ValidationTestComponent với AdvancedModalComponent interface
   */
  async openValidationTestModal(): Promise<void> {
    try {
      // Mock data ban đầu
      const initialData: ValidationTestData = {
        name: 'John',
        email: '', // Email trống để test validation
        agreeToTerms: false
      };

      console.log('Opening ValidationTest modal...');

      const modalConfig: ResponsiveModalConfig<ValidationTestData> = {
        title: '🧪 Test Validation Modal',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: initialData
      };

      const result = await this.responsiveModalService.open(
        ValidationTestComponent,
        modalConfig
      );

      if (result) {
        console.log('ValidationTest modal result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'confirm' && testResult.data) {
          const data = testResult.data as ValidationTestResult;
          alert(`✅ Form đã được submit thành công!\n\nThông tin:\n- Tên: ${data.name}\n- Email: ${data.email}\n- Đồng ý điều khoản: ${data.agreeToTerms ? 'Có' : 'Không'}\n- Thời gian submit: ${data.submittedAt.toLocaleString()}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      } else {
        alert('❌ Đã hủy form');
      }
    } catch (error) {
      console.error('Lỗi khi mở ValidationTest modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test StrictTestModalComponent với type safety đầy đủ
   */
  async openStrictTestModal(): Promise<void> {
    try {
      // Mock data ban đầu
      const initialData: StrictTestModalData = {
        userName: 'John Doe',
        email: '<EMAIL>',
        message: 'Test StrictModalComponent interface với type safety đầy đủ'
      };

      console.log('Opening StrictTest modal...');

      const result = await this.responsiveModalService.open<
        StrictTestModalData,
        StrictTestModalResult,
        StrictTestModalComponent
      >(
        StrictTestModalComponent,
        {
          title: '🧪 Strict Test Modal (Type Safe)',
          enableClose: true,
          actions: {
            useDefault: true
          },
          data: initialData
        }
      );

      if (result) {
        console.log('StrictTest modal result:', result);
        alert(`✅ Form đã được submit thành công với type safety!\n\nThông tin:\n- Tên: ${result.userName}\n- Email: ${result.email}\n- Đồng ý điều khoản: ${result.termsAccepted ? 'Có' : 'Không'}\n- Thời gian submit: ${result.timestamp.toLocaleString()}`);
      } else {
        alert('❌ Đã hủy form');
      }
    } catch (error) {
      console.error('Lỗi khi mở StrictTest modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test ValidationTestComponent với force bottom sheet mode
   */
  async openValidationTestBottomSheet(): Promise<void> {
    try {
      // Mock data ban đầu với một số field đã điền
      const initialData: ValidationTestData = {
        name: 'Jane Doe',
        email: '<EMAIL>',
        agreeToTerms: false // Chưa đồng ý để test validation
      };

      console.log('Opening ValidationTest bottom sheet...');

      const modalConfig: ResponsiveModalConfig<ValidationTestData> = {
        title: '🧪 Test Validation Bottom Sheet',
        enableClose: true,
        actions: {
          useDefault: true
        },
        data: initialData
      };

      const result = await this.responsiveModalService.open(
        ValidationTestComponent,
        modalConfig,
        'bottom-sheet' // Force bottom sheet mode
      );

      if (result) {
        console.log('ValidationTest bottom sheet result:', result);
        const testResult = result as TestModalResult;
        if (testResult.action === 'confirm' && testResult.data) {
          const data = testResult.data as ValidationTestResult;
          alert(`✅ Form đã được submit thành công từ Bottom Sheet!\n\nThông tin:\n- Tên: ${data.name}\n- Email: ${data.email}\n- Đồng ý điều khoản: ${data.agreeToTerms ? 'Có' : 'Không'}\n- Thời gian submit: ${data.submittedAt.toLocaleString()}`);
        } else {
          alert(`Kết quả: ${testResult.action}`);
        }
      } else {
        alert('❌ Đã hủy form');
      }
    } catch (error) {
      console.error('Lỗi khi mở ValidationTest bottom sheet:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test IntegrationAccountViewModalComponent
   */
  async openIntegrationAccountViewModal(): Promise<void> {
    try {
      // Tạo mock settings config
      const settingsConfig: SettingsListConfig = {
        settings: mockAllSettings.slice(0, 6), // Lấy 6 settings đầu tiên
        values: {
          ...mockDefaultSettingValues,
          notifications_enabled: true,
          auto_save: false,
          dark_mode: true
        },
        showDividers: true,
        showCard: false,
        autoSave: false, // Để user phải click Save
        showSaveButtons: true
      };

      // Lấy mock logs cho ShopeeFood
      const logs = getMockLogsByPlatform('shopeefood');

      // Chuẩn bị dữ liệu cho modal
      const modalData: IntegrationAccountViewModalData = {
        config: settingsConfig,
        logs: logs,
        platformName: 'Shopee Food',
        accountName: 'Test Account'
      };

      console.log('Opening Integration Account View modal...');

      const result = await this.responsiveModalService.open<
        IntegrationAccountViewModalData,
        IntegrationAccountViewModalResult
      >(
        IntegrationAccountViewModalComponent,
        {
          title: 'INTEGRATION_ACCOUNT_VIEW_TITLE',
          data: modalData,
          width: '800px',
          maxWidth: '95vw',
          disableClose: false
        }
      );

      if (result) {
        console.log('Integration Account View modal result:', result);
        if (result.settingsChanged) {
          alert(`✅ Settings đã được thay đổi!\n\nAction: ${result.action}\nChanged Settings: ${JSON.stringify(result.changedSettings, null, 2)}`);
        } else {
          alert(`ℹ️ Modal đã đóng mà không có thay đổi.\n\nAction: ${result.action}`);
        }
      } else {
        alert('❌ Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở Integration Account View modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test IntegrationAccountSettingsModalComponent
   */
  async openIntegrationAccountSettingsModal(): Promise<void> {
    try {
      // Tạo mock settings config
      const settingsConfig: SettingsListConfig = {
        settings: mockAllSettings.slice(0, 6), // Lấy 6 settings đầu tiên
        values: {
          ...mockDefaultSettingValues,
          notifications_enabled: true,
          auto_save: false,
          dark_mode: true
        },
        showDividers: true,
        showCard: false,
        autoSave: false, // Để user phải click Save
        showSaveButtons: true
      };

      // Chuẩn bị dữ liệu cho modal
      const modalData: IntegrationAccountSettingsModalData = {
        config: settingsConfig,
        platformName: 'Shopee Food',
        accountName: 'Test Settings Account'
      };

      console.log('Opening Integration Account Settings modal...');

      const result = await this.responsiveModalService.open<
        IntegrationAccountSettingsModalData,
        IntegrationAccountSettingsModalResult
      >(
        IntegrationAccountSettingsModalComponent,
        {
          title: 'INTEGRATION_ACCOUNT_SETTINGS_TITLE',
          data: modalData,
          width: '700px',
          maxWidth: '95vw',
          disableClose: false
        }
      );

      if (result) {
        console.log('Integration Account Settings modal result:', result);
        if (result.settingsChanged) {
          alert(`✅ Settings đã được thay đổi!\n\nAction: ${result.action}\nChanged Settings: ${JSON.stringify(result.changedSettings, null, 2)}`);
        } else {
          alert(`ℹ️ Modal đã đóng mà không có thay đổi.\n\nAction: ${result.action}`);
        }
      } else {
        alert('❌ Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở Integration Account Settings modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  /**
   * Test IntegrationAccountLogsModalComponent
   */
  async openIntegrationAccountLogsModal(): Promise<void> {
    try {
      // Lấy mock logs cho ShopeeFood
      const logs = getMockLogsByPlatform('shopeefood');

      // Chuẩn bị dữ liệu cho modal
      const modalData: IntegrationAccountLogsModalData = {
        logs: logs,
        platformName: 'Shopee Food',
        accountName: 'Test Logs Account'
      };

      console.log('Opening Integration Account Logs modal...');

      const result = await this.responsiveModalService.open<
        IntegrationAccountLogsModalData,
        IntegrationAccountLogsModalResult
      >(
        IntegrationAccountLogsModalComponent,
        {
          title: 'INTEGRATION_ACCOUNT_LOGS_TITLE',
          data: modalData,
          width: '800px',
          maxWidth: '95vw',
          disableClose: false
        }
      );

      if (result) {
        console.log('Integration Account Logs modal result:', result);
        alert(`ℹ️ Logs modal đã đóng.\n\nAction: ${result.action}\nTotal Logs: ${logs.length}`);
      } else {
        alert('❌ Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở Integration Account Logs modal:', error);
      alert(`❌ Lỗi: ${error}`);
    }
  }

  // ===== DynamicLayoutRendererModal Tests =====

  /**
   * Test DynamicLayoutRendererModal trong View Mode (read-only)
   */
  async openDynamicLayoutRendererViewModal(): Promise<void> {
    try {
      console.log('Opening DynamicLayoutRenderer View Modal...');

      const result = await this.dynamicLayoutRendererModalService.openViewMode(
        {
          ...simpleMockLayouts[0], // Sử dụng layout đầu tiên
          currentPermissionProfileId: 'admin',
          showPermissionProfiles: true,
          showAllTab: false,
          defaultView: 'view',
          // DynamicLayoutSharedConfig properties
          defaultLayoutConfig: simpleMockLayouts[0],
          permissionProfiles: [
            { _id: 'admin', name: 'Administrator', permission: 'read_write' as const },
            { _id: 'manager', name: 'Manager', permission: 'read_write' as const },
            { _id: 'user', name: 'Standard User', permission: 'read' as const },
            { _id: 'guest', name: 'Guest User', permission: 'none' as const }
          ]
        },
        {
          title: 'DYNAMIC_LAYOUT_RENDERER_MODAL.TITLES.VIEW_LAYOUT',
          width: '1000px',
          disableClose: false
        }
      );

      if (result) {
        console.log('DynamicLayoutRenderer View Modal result:', result);
        this.flashMessageService.success(
          `Modal đóng với action: ${result.action}`
        );
      } else {
        console.log('DynamicLayoutRenderer View Modal cancelled');
        this.flashMessageService.info('Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở DynamicLayoutRenderer View Modal:', error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test DynamicLayoutRendererModal trong Edit Mode với form values
   */
  async openDynamicLayoutRendererEditModal(): Promise<void> {
    try {
      console.log('Opening DynamicLayoutRenderer Edit Modal...');

      // Mock form values cho test
      const mockFormValues: Record<string, any> = {
        'field-1': 'Nguyễn Văn Test Modal',
        'field-2': '+84987654321',
        'field-3': '<EMAIL>',
        'field-4': '1990-01-15',
        'field-5': '123 Đường Test Modal, Quận 1, TP.HCM',
        'field-6': 'option2', // Size thường mặc
        'field-7': 'red', // Màu sắc yêu thích
        'field-8': 'casual', // Phong cách
        'field-9': 'nike', // Thương hiệu yêu thích
        'field-10': 'Ghi chú test modal với nhiều thông tin chi tiết',
        'field-11': 12345, // Số đơn hàng
        'field-12': 5000000, // Tổng chi tiêu
        'field-13': '2024-01-01', // Lần mua cuối
        'field-14': 'fashion', // Loại sản phẩm thường mua
        'field-15': true, // Đăng ký nhận thông báo
        'field-16': 'sample-invoice-modal.pdf', // Hóa đơn mẫu
        'field-17': 'user2' // Nhân viên phụ trách
      };

      const result = await this.dynamicLayoutRendererModalService.openEditMode(
        {
          ...simpleMockLayouts[0], // Sử dụng layout đầu tiên
          currentPermissionProfileId: 'admin',
          showPermissionProfiles: true,
          showAllTab: true,
          defaultView: 'form',
          formValues: mockFormValues,
          // DynamicLayoutSharedConfig properties
          defaultLayoutConfig: simpleMockLayouts[0],
          permissionProfiles: [
            { _id: 'admin', name: 'Administrator', permission: 'read_write' as const },
            { _id: 'manager', name: 'Manager', permission: 'read_write' as const },
            { _id: 'user', name: 'Standard User', permission: 'read' as const },
            { _id: 'guest', name: 'Guest User', permission: 'none' as const }
          ],
          onFormSave: async (formData: DynamicLayoutRendererFormData) => {
            console.log('Form save callback called with:', formData);
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            console.log('Form saved successfully!');
          }
        },
        {
          title: 'DYNAMIC_LAYOUT_RENDERER_MODAL.TITLES.EDIT_FORM',
          width: '1200px',
          disableClose: true // Không cho đóng khi đang edit
        }
      );

      if (result) {
        console.log('DynamicLayoutRenderer Edit Modal result:', result);
        if (result.action === 'save' && result.formData) {
          this.flashMessageService.success(
            `Form đã được lưu thành công với ${result.formData.values.length} trường dữ liệu!`
          );
        } else {
          this.flashMessageService.info(
            `Modal đóng với action: ${result.action}`
          );
        }
      } else {
        console.log('DynamicLayoutRenderer Edit Modal cancelled');
        this.flashMessageService.info('Modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở DynamicLayoutRenderer Edit Modal:', error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithOverlay method
   * Trên desktop: Hiển thị modal dạng overlay tại vị trí của button được click
   * Trên mobile: Hiển thị mat-bottom-sheet như hiện tại
   */
  async testOpenWithOverlay(event: MouseEvent): Promise<void> {
    try {
      console.log('Testing ResponsiveModalService.openWithOverlay...');

      const result = await this.responsiveModalService.openWithOverlay(
        SimpleTestComponent,
        event, // Sử dụng MouseEvent trực tiếp
        {
          title: 'Overlay Modal Test',
          actions: {
            useDefault: true
          },
          enableClose: true,
          data: {
            message: 'Đây là test modal với overlay positioning',
            content: 'Trên desktop: Modal sẽ hiển thị gần vị trí button được click. Trên mobile: Sẽ hiển thị bottom sheet.',
            testFeature: 'Overlay positioning'
          }
        }
      );

      if (result) {
        console.log('Overlay modal result:', result);
        const testResult = result as TestModalResult;
        this.flashMessageService.success(`Overlay modal: ${testResult.action}`);
      } else {
        console.log('Overlay modal cancelled');
        this.flashMessageService.info('Overlay modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi test overlay modal:', error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithOverlay với force mode
   */
  async testOpenWithOverlayForceMode(event: MouseEvent, forceMode: 'dialog' | 'bottom-sheet'): Promise<void> {
    try {
      console.log(`Testing ResponsiveModalService.openWithOverlay with force mode: ${forceMode}...`);

      const result = await this.responsiveModalService.openWithOverlay(
        SimpleTestComponent,
        event, // Sử dụng MouseEvent trực tiếp
        {
          title: `Overlay Modal (Force ${forceMode})`,
          actions: {
            useDefault: true
          },
          enableClose: true,
          data: {
            message: `Test modal với force mode: ${forceMode}`,
            content: `Modal này được force để hiển thị dạng ${forceMode} bất kể viewport size.`,
            testFeature: `Force ${forceMode} mode`
          }
        },
        forceMode
      );

      if (result) {
        console.log(`Overlay modal (${forceMode}) result:`, result);
        const testResult = result as TestModalResult;
        this.flashMessageService.success(`Overlay modal (${forceMode}): ${testResult.action}`);
      } else {
        console.log(`Overlay modal (${forceMode}) cancelled`);
        this.flashMessageService.info(`Overlay modal (${forceMode}) đã bị hủy`);
      }
    } catch (error) {
      console.error(`Lỗi khi test overlay modal (${forceMode}):`, error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithOverlay với positioning options
   */
  async testOverlayPositioning(event: MouseEvent, position: 'center' | 'left' | 'right' | 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'): Promise<void> {
    try {
      console.log(`Testing ResponsiveModalService.openWithOverlay with position: ${position}...`);

      const result = await this.responsiveModalService.openWithOverlay(
        SimpleTestComponent,
        event,
        {
          title: `Overlay Modal Test - ${position}`,
          actions: {
            useDefault: true
          },
          enableClose: true,
          data: {
            message: `Test modal với positioning: ${position}`,
            content: `Modal được hiển thị với alignment: ${position}. Vị trí được tính toán dựa trên mouse position và viewport constraints.`,
            testFeature: `Positioning: ${position}`
          },
          overlayModalPosition: position
        }
      );

      if (result) {
        console.log(`Overlay modal result (${position}):`, result);
        const testResult = result as TestModalResult;
        this.flashMessageService.success(`Overlay modal (${position}): ${testResult.action}`);
      } else {
        console.log(`Overlay modal (${position}) cancelled`);
        this.flashMessageService.info(`Overlay modal (${position}) đã bị hủy`);
      }
    } catch (error) {
      console.error(`Lỗi khi test overlay modal với position ${position}:`, error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test DynamicLayoutRendererModal với cấu hình tùy chỉnh
   */
  async openDynamicLayoutRendererCustomModal(): Promise<void> {
    try {
      console.log('Opening DynamicLayoutRenderer Custom Modal...');

      const customModalData: DynamicLayoutRendererModalData = {
        ...simpleMockLayouts[0],
        currentPermissionProfileId: 'editor', // Quyền editor thay vì admin
        showPermissionProfiles: true,
        defaultView: 'form',
        showAllTab: true,
        enableEditMode: true,
        title: 'Custom Layout Renderer Modal',
        width: '90vw',
        height: '80vh',
        disableClose: false,
        enableClose: true,
        // DynamicLayoutSharedConfig properties
        defaultLayoutConfig: simpleMockLayouts[0],
        permissionProfiles: [
          { _id: 'admin', name: 'Administrator', permission: 'read_write' as const },
          { _id: 'manager', name: 'Manager', permission: 'read_write' as const },
          { _id: 'user', name: 'Standard User', permission: 'read' as const },
          { _id: 'guest', name: 'Guest User', permission: 'none' as const }
        ],
        formValues: {
          'field-1': 'Custom Test Data',
          'field-2': '+84123456789',
          'field-3': '<EMAIL>'
        },
        onFormSave: async (formData: DynamicLayoutRendererFormData) => {
          console.log('Custom form save:', formData);
          // Simulate validation error
          if (formData.values.length < 3) {
            throw new Error('Cần ít nhất 3 trường dữ liệu');
          }
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      };

      const result = await this.dynamicLayoutRendererModalService.openCustom(
        customModalData,
        'dialog' // Force dialog mode
      );

      if (result) {
        console.log('DynamicLayoutRenderer Custom Modal result:', result);
        this.flashMessageService.success(
          `Custom modal hoàn thành: ${result.action} (${result.hasChanges ? 'có thay đổi' : 'không thay đổi'})`
        );
      } else {
        console.log('DynamicLayoutRenderer Custom Modal cancelled');
        this.flashMessageService.info('Custom modal đã bị hủy');
      }
    } catch (error) {
      console.error('Lỗi khi mở DynamicLayoutRenderer Custom Modal:', error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithMatMenu method
   * Desktop: Hiển thị modal dạng overlay positioned giống mat-menu
   * Mobile: Hiển thị mat-bottom-sheet
   */
  testOpenWithMatMenu(event: MouseEvent): void {
    try {
      console.log('Testing ResponsiveModalService.openWithMatMenu...');

      // Extract trigger element từ event
      const triggerElement = event.target as HTMLElement;

      if (!triggerElement) {
        this.flashMessageService.error('Không tìm thấy trigger element');
        return;
      }

      const dialogRef = this.responsiveModalService.openWithMatMenu(
        SimpleTestComponent,
        triggerElement,
        {
          data: {
            message: 'Đây là test modal với mat-menu positioning',
            content: 'Desktop: Modal positioned giống mat-menu. Mobile: Bottom sheet.',
            testFeature: 'Mat Menu positioning'
          },
          maxWidth: '320px',
          maxHeight: '400px',
          hasBackdrop: true,
          disableClose: false
        },
        {
          message: 'Test data cho mat-menu modal',
          userName: 'Test User',
          action: 'mat-menu test'
        }
      );

      // Handle result dựa trên type của dialogRef
      if ('afterClosed' in dialogRef) {
        // Desktop: MatDialogRef
        dialogRef.afterClosed().subscribe((result: any) => {
          if (result) {
            console.log('Mat-menu modal result:', result);
            this.flashMessageService.success(`Mat-menu modal: ${result.action || 'completed'}`);
          } else {
            console.log('Mat-menu modal cancelled');
            this.flashMessageService.info('Mat-menu modal đã bị hủy');
          }
        });
      } else if ('afterDismissed' in dialogRef) {
        // Mobile: MatBottomSheetRef
        (dialogRef.afterDismissed() as any).subscribe((result: any) => {
          if (result) {
            console.log('Mat-menu bottom sheet result:', result);
            this.flashMessageService.success(`Mat-menu bottom sheet: ${result.action || 'completed'}`);
          } else {
            console.log('Mat-menu bottom sheet dismissed');
            this.flashMessageService.info('Mat-menu bottom sheet đã bị đóng');
          }
        });
      }

    } catch (error) {
      console.error('Lỗi khi test mat-menu modal:', error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithMatMenu với different trigger positions
   */
  testMatMenuPositioning(event: MouseEvent, testCase: string): void {
    try {
      console.log(`Testing mat-menu positioning: ${testCase}...`);

      const triggerElement = event.target as HTMLElement;

      if (!triggerElement) {
        this.flashMessageService.error('Không tìm thấy trigger element');
        return;
      }

      const dialogRef = this.responsiveModalService.openWithMatMenu(
        SimpleTestComponent,
        triggerElement,
        {
          data: {
            message: `Test positioning: ${testCase}`,
            content: `Modal được trigger từ ${testCase}. Kiểm tra positioning và viewport collision detection.`,
            testFeature: `Mat Menu ${testCase} positioning`
          },
          maxWidth: '350px',
          maxHeight: '300px',
          hasBackdrop: true,
          disableClose: false,
          panelClass: [`mat-menu-test-${testCase.toLowerCase().replace(/\s+/g, '-')}`]
        },
        {
          testCase: testCase,
          triggerPosition: {
            left: triggerElement.getBoundingClientRect().left,
            top: triggerElement.getBoundingClientRect().top,
            width: triggerElement.getBoundingClientRect().width,
            height: triggerElement.getBoundingClientRect().height
          }
        }
      );

      // Handle result
      const handleResult = (result: any) => {
        if (result) {
          console.log(`Mat-menu ${testCase} result:`, result);
          this.flashMessageService.success(`${testCase}: ${result.action || 'completed'}`);
        } else {
          console.log(`Mat-menu ${testCase} cancelled`);
          this.flashMessageService.info(`${testCase} đã bị hủy`);
        }
      };

      if ('afterClosed' in dialogRef) {
        dialogRef.afterClosed().subscribe(handleResult);
      } else if ('afterDismissed' in dialogRef) {
        (dialogRef.afterDismissed() as any).subscribe(handleResult);
      }

    } catch (error) {
      console.error(`Lỗi khi test mat-menu positioning (${testCase}):`, error);
      this.flashMessageService.error(`Lỗi ${testCase}: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithMatMenu với edge cases
   */
  testMatMenuEdgeCases(): void {
    try {
      console.log('Testing mat-menu edge cases...');

      // Test case 1: Null trigger element
      try {
        this.responsiveModalService.openWithMatMenu(
          SimpleTestComponent,
          null as any,
          { data: { message: 'This should fail' } }
        );
      } catch (error) {
        console.log('✅ Null trigger element correctly handled:', error);
        this.flashMessageService.success('Edge case 1: Null trigger element handled correctly');
      }

      // Test case 2: Invalid trigger element
      try {
        this.responsiveModalService.openWithMatMenu(
          SimpleTestComponent,
          {} as any,
          { data: { message: 'This should fail' } }
        );
      } catch (error) {
        console.log('✅ Invalid trigger element correctly handled:', error);
        this.flashMessageService.success('Edge case 2: Invalid trigger element handled correctly');
      }

      // Test case 3: Valid trigger element with minimal config
      const testButton = document.createElement('button');
      testButton.style.position = 'fixed';
      testButton.style.top = '50px';
      testButton.style.left = '50px';
      testButton.style.width = '100px';
      testButton.style.height = '40px';
      document.body.appendChild(testButton);

      const dialogRef = this.responsiveModalService.openWithMatMenu(
        SimpleTestComponent,
        testButton,
        {},
        { message: 'Minimal config test' }
      );

      // Cleanup test element after a short delay
      setTimeout(() => {
        document.body.removeChild(testButton);
      }, 100);

      const handleResult = (result: any) => {
        if (result) {
          console.log('✅ Minimal config test result:', result);
          this.flashMessageService.success('Edge case 3: Minimal config handled correctly');
        } else {
          console.log('✅ Minimal config test cancelled');
          this.flashMessageService.info('Edge case 3: Minimal config test cancelled');
        }
      };

      if ('afterClosed' in dialogRef) {
        dialogRef.afterClosed().subscribe(handleResult);
      } else if ('afterDismissed' in dialogRef) {
        (dialogRef.afterDismissed() as any).subscribe(handleResult);
      }

    } catch (error) {
      console.error('Lỗi khi test mat-menu edge cases:', error);
      this.flashMessageService.error(`Edge cases error: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithPopover method
   * Desktop: Hiển thị modal dạng overlay positioned giống PrimeNG Popover
   * Mobile: Hiển thị mat-bottom-sheet
   */
  async testOpenWithPopover(event: MouseEvent): Promise<void> {
    try {
      console.log('Testing ResponsiveModalService.openWithPopover...');

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Popover Modal Test',
          actions: {
            useDefault: true
          },
          enableClose: true,
          data: {
            message: 'Đây là test modal với PrimeNG Popover positioning',
            content: 'Desktop: Modal positioned giống PrimeNG Popover. Mobile: Bottom sheet.',
            testFeature: 'PrimeNG Popover positioning'
          }
        }
      );

      if (result) {
        console.log('Popover modal result:', result);
        this.flashMessageService.success(`Popover modal: ${(result as any).action || 'completed'}`);
      } else {
        console.log('Popover modal cancelled');
        this.flashMessageService.info('Popover modal đã bị hủy');
      }

    } catch (error) {
      console.error('Lỗi khi test popover modal:', error);
      this.flashMessageService.error(`Lỗi: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithPopover với different trigger positions
   */
  async testPopoverPositioning(event: MouseEvent, testCase: string): Promise<void> {
    try {
      console.log(`Testing popover positioning: ${testCase}...`);

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: `Popover Test - ${testCase}`,
          actions: {
            useDefault: true
          },
          enableClose: true,
          data: {
            message: `Test positioning: ${testCase}`,
            content: `Modal được trigger từ ${testCase}. Kiểm tra PrimeNG Popover positioning và viewport collision detection.`,
            testFeature: `Popover ${testCase} positioning`
          },
          overlayModalPosition: 'bottom-left' // Default PrimeNG Popover position
        }
      );

      if (result) {
        console.log(`Popover ${testCase} result:`, result);
        this.flashMessageService.success(`${testCase}: ${(result as any).action || 'completed'}`);
      } else {
        console.log(`Popover ${testCase} cancelled`);
        this.flashMessageService.info(`${testCase} đã bị hủy`);
      }

    } catch (error) {
      console.error(`Lỗi khi test popover positioning (${testCase}):`, error);
      this.flashMessageService.error(`Lỗi ${testCase}: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService.openWithPopover với edge cases
   */
  async testPopoverEdgeCases(event: MouseEvent): Promise<void> {
    try {
      console.log('Testing popover edge cases...');

      // Test case 1: No trigger element (should fallback to StandardOverlayModalService)
      const fakeEvent = new MouseEvent('click', {
        clientX: 100,
        clientY: 100
      });

      const result1 = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        fakeEvent,
        {
          title: 'Popover Fallback Test',
          data: {
            message: 'Test fallback to StandardOverlayModalService',
            content: 'Khi không có trigger element, sẽ fallback về StandardOverlayModalService.',
            testFeature: 'Popover fallback mechanism'
          }
        }
      );

      if (result1) {
        console.log('✅ Popover fallback test result:', result1);
        this.flashMessageService.success('Edge case 1: Popover fallback handled correctly');
      }

      // Test case 2: Force mobile mode
      const result2 = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Force Mobile Mode Test',
          data: {
            message: 'Test force mobile mode',
            content: 'Force sử dụng bottom-sheet ngay cả trên desktop.',
            testFeature: 'Force mobile mode'
          }
        },
        'bottom-sheet' // Force mobile mode
      );

      if (result2) {
        console.log('✅ Force mobile mode test result:', result2);
        this.flashMessageService.success('Edge case 2: Force mobile mode handled correctly');
      }

    } catch (error) {
      console.error('Lỗi khi test popover edge cases:', error);
      this.flashMessageService.error(`Popover edge cases error: ${error}`);
    }
  }

  // ===================================================================
  // THREADS-LIKE MAT-MENU ANIMATION METHODS
  // ===================================================================

  /**
   * Handle khi Threads-like menu được mở
   * @param menuType - Loại menu ('basic' hoặc 'enhanced')
   */
  onThreadsMenuOpened(menuType: string): void {
    console.log(`🎭 Threads menu opened: ${menuType}`);
    this.threadsMenuStatus[menuType] = 'Opening...';

    // Simulate animation completion
    setTimeout(() => {
      this.threadsMenuStatus[menuType] = 'Open';
    }, 300);

    // Show flash message
    this.flashMessageService.info(`Threads ${menuType} menu opened with animation`);
  }

  /**
   * Handle khi Threads-like menu được đóng
   * @param menuType - Loại menu ('basic' hoặc 'enhanced')
   */
  onThreadsMenuClosed(menuType: string): void {
    console.log(`🎭 Threads menu closed: ${menuType}`);
    this.threadsMenuStatus[menuType] = 'Closing...';

    // Simulate animation completion
    setTimeout(() => {
      this.threadsMenuStatus[menuType] = 'Closed';
    }, 250);

    // Show flash message
    this.flashMessageService.info(`Threads ${menuType} menu closed with animation`);
  }

  // ===================================================================
  // RESPONSIVEMODALSERVICE THREADS ANIMATION INTEGRATION METHODS
  // ===================================================================

  /**
   * Test ResponsiveModalService với Threads animation basic
   */
  async testResponsiveModalWithThreadsBasic(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing ResponsiveModalService with Threads basic animation...');
      this.responsiveModalThreadsStatus.lastTest = 'Basic Integration';
      this.responsiveModalThreadsStatus.animationType = 'Basic';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'ResponsiveModal với Threads Basic',
          data: {
            message: 'Test ResponsiveModalService với Threads basic animation',
            testData: { timestamp: new Date(), type: 'responsive-modal-threads-basic' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'basic'
          }
        }
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('ResponsiveModal Threads basic animation test completed');
      console.log('🚀 ResponsiveModal Threads basic result:', result);

    } catch (error) {
      console.error('Lỗi khi test ResponsiveModal Threads basic:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`ResponsiveModal Threads basic error: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService với Threads animation enhanced
   */
  async testResponsiveModalWithThreadsEnhanced(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing ResponsiveModalService with Threads enhanced animation...');
      this.responsiveModalThreadsStatus.lastTest = 'Enhanced Integration';
      this.responsiveModalThreadsStatus.animationType = 'Enhanced';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'ResponsiveModal với Threads Enhanced',
          data: {
            message: 'Test ResponsiveModalService với Threads enhanced animation (blur effect)',
            testData: { timestamp: new Date(), type: 'responsive-modal-threads-enhanced' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'enhanced'
          }
        }
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('ResponsiveModal Threads enhanced animation test completed');
      console.log('🚀 ResponsiveModal Threads enhanced result:', result);

    } catch (error) {
      console.error('Lỗi khi test ResponsiveModal Threads enhanced:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`ResponsiveModal Threads enhanced error: ${error}`);
    }
  }

  /**
   * Test ResponsiveModalService với Threads animation disabled
   */
  async testResponsiveModalWithThreadsDisabled(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing ResponsiveModalService with Threads animation disabled...');
      this.responsiveModalThreadsStatus.lastTest = 'Disabled Animation';
      this.responsiveModalThreadsStatus.animationType = 'Disabled';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'ResponsiveModal không có Threads Animation',
          data: {
            message: 'Test ResponsiveModalService với Threads animation disabled',
            testData: { timestamp: new Date(), type: 'responsive-modal-no-threads' }
          },
          threadsAnimation: {
            enabled: false
          }
        }
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('ResponsiveModal without Threads animation test completed');
      console.log('🚀 ResponsiveModal no Threads result:', result);

    } catch (error) {
      console.error('Lỗi khi test ResponsiveModal no Threads:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`ResponsiveModal no Threads error: ${error}`);
    }
  }

  /**
   * Test Mat-Menu với Threads animation basic
   */
  async testMatMenuWithThreadsBasic(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing Mat-Menu with Threads basic animation...');
      this.responsiveModalThreadsStatus.lastTest = 'Mat-Menu Basic';
      this.responsiveModalThreadsStatus.animationType = 'Basic';
      this.responsiveModalThreadsStatus.mode = 'Mat-Menu';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      // Create fake ElementRef from event target
      const element = event.target as HTMLElement;
      const elementRef = { nativeElement: element };

      const result = await new Promise((resolve) => {
        const dialogRef = this.responsiveModalService.openWithMatMenu(
          SimpleTestComponent,
          elementRef,
          {
            data: {
              message: 'Test Mat-Menu với Threads basic animation',
              testData: { timestamp: new Date(), type: 'mat-menu-threads-basic' }
            },
            threadsAnimation: {
              enabled: true,
              type: 'basic'
            }
          }
        );

        // Handle both MatDialogRef and MatBottomSheetRef
        if ('afterClosed' in dialogRef) {
          dialogRef.afterClosed().subscribe((result: any) => resolve(result));
        } else if ('afterDismissed' in dialogRef) {
          (dialogRef as any).afterDismissed().subscribe((result: any) => resolve(result));
        } else {
          resolve(undefined);
        }
      });

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Mat-Menu Threads basic animation test completed');
      console.log('🚀 Mat-Menu Threads basic result:', result);

    } catch (error) {
      console.error('Lỗi khi test Mat-Menu Threads basic:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Mat-Menu Threads basic error: ${error}`);
    }
  }

  /**
   * Test Mat-Menu với Threads animation enhanced
   */
  async testMatMenuWithThreadsEnhanced(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing Mat-Menu with Threads enhanced animation...');
      this.responsiveModalThreadsStatus.lastTest = 'Mat-Menu Enhanced';
      this.responsiveModalThreadsStatus.animationType = 'Enhanced';
      this.responsiveModalThreadsStatus.mode = 'Mat-Menu';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      // Create fake ElementRef from event target
      const element = event.target as HTMLElement;
      const elementRef = { nativeElement: element };

      const result = await new Promise((resolve) => {
        const dialogRef = this.responsiveModalService.openWithMatMenu(
          SimpleTestComponent,
          elementRef,
          {
            data: {
              message: 'Test Mat-Menu với Threads enhanced animation (blur effect)',
              testData: { timestamp: new Date(), type: 'mat-menu-threads-enhanced' }
            },
            threadsAnimation: {
              enabled: true,
              type: 'enhanced'
            }
          }
        );

        // Handle both MatDialogRef and MatBottomSheetRef
        if ('afterClosed' in dialogRef) {
          dialogRef.afterClosed().subscribe((result: any) => resolve(result));
        } else if ('afterDismissed' in dialogRef) {
          (dialogRef as any).afterDismissed().subscribe((result: any) => resolve(result));
        } else {
          resolve(undefined);
        }
      });

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Mat-Menu Threads enhanced animation test completed');
      console.log('🚀 Mat-Menu Threads enhanced result:', result);

    } catch (error) {
      console.error('Lỗi khi test Mat-Menu Threads enhanced:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Mat-Menu Threads enhanced error: ${error}`);
    }
  }

  /**
   * Test Mat-Menu với custom Threads animation classes
   */
  async testMatMenuWithThreadsCustom(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing Mat-Menu with custom Threads animation classes...');
      this.responsiveModalThreadsStatus.lastTest = 'Mat-Menu Custom';
      this.responsiveModalThreadsStatus.animationType = 'Custom';
      this.responsiveModalThreadsStatus.mode = 'Mat-Menu';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      // Create fake ElementRef from event target
      const element = event.target as HTMLElement;
      const elementRef = { nativeElement: element };

      const result = await new Promise((resolve) => {
        const dialogRef = this.responsiveModalService.openWithMatMenu(
          SimpleTestComponent,
          elementRef,
          {
            data: {
              message: 'Test Mat-Menu với custom Threads animation classes',
              testData: { timestamp: new Date(), type: 'mat-menu-threads-custom' }
            },
            threadsAnimation: {
              enabled: true,
              type: 'basic',
              customClasses: {
                container: 'apply-threads-menu-enhanced',
                menu: 'threads-menu-slide-up'
              }
            }
          }
        );

        // Handle both MatDialogRef and MatBottomSheetRef
        if ('afterClosed' in dialogRef) {
          dialogRef.afterClosed().subscribe((result: any) => resolve(result));
        } else if ('afterDismissed' in dialogRef) {
          (dialogRef as any).afterDismissed().subscribe((result: any) => resolve(result));
        } else {
          resolve(undefined);
        }
      });

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Mat-Menu custom Threads animation test completed');
      console.log('🚀 Mat-Menu Threads custom result:', result);

    } catch (error) {
      console.error('Lỗi khi test Mat-Menu Threads custom:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Mat-Menu Threads custom error: ${error}`);
    }
  }

  /**
   * Test Popover với Threads animation basic
   */
  async testPopoverWithThreadsBasic(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing Popover with Threads basic animation...');
      this.responsiveModalThreadsStatus.lastTest = 'Popover Basic';
      this.responsiveModalThreadsStatus.animationType = 'Basic';
      this.responsiveModalThreadsStatus.mode = 'Popover';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Popover với Threads Basic',
          data: {
            message: 'Test Popover với Threads basic animation',
            testData: { timestamp: new Date(), type: 'popover-threads-basic' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'basic'
          }
        }
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Popover Threads basic animation test completed');
      console.log('🚀 Popover Threads basic result:', result);

    } catch (error) {
      console.error('Lỗi khi test Popover Threads basic:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Popover Threads basic error: ${error}`);
    }
  }

  /**
   * Test Popover với Threads animation enhanced
   */
  async testPopoverWithThreadsEnhanced(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing Popover with Threads enhanced animation...');
      this.responsiveModalThreadsStatus.lastTest = 'Popover Enhanced';
      this.responsiveModalThreadsStatus.animationType = 'Enhanced';
      this.responsiveModalThreadsStatus.mode = 'Popover';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Popover với Threads Enhanced',
          data: {
            message: 'Test Popover với Threads enhanced animation (blur effect)',
            testData: { timestamp: new Date(), type: 'popover-threads-enhanced' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'enhanced'
          }
        }
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Popover Threads enhanced animation test completed');
      console.log('🚀 Popover Threads enhanced result:', result);

    } catch (error) {
      console.error('Lỗi khi test Popover Threads enhanced:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Popover Threads enhanced error: ${error}`);
    }
  }

  /**
   * Test Popover với mobile mode (force bottom sheet)
   */
  async testPopoverWithThreadsMobile(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing Popover with mobile mode (force bottom sheet)...');
      this.responsiveModalThreadsStatus.lastTest = 'Popover Mobile';
      this.responsiveModalThreadsStatus.animationType = 'Basic';
      this.responsiveModalThreadsStatus.mode = 'Mobile Bottom Sheet';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Popover Mobile Mode',
          data: {
            message: 'Test Popover với mobile mode (force bottom sheet) và Threads animation',
            testData: { timestamp: new Date(), type: 'popover-mobile-threads' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'basic'
          }
        },
        'bottom-sheet' // Force mobile mode
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Popover mobile mode Threads animation test completed');
      console.log('🚀 Popover mobile Threads result:', result);

    } catch (error) {
      console.error('Lỗi khi test Popover mobile Threads:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Popover mobile Threads error: ${error}`);
    }
  }

  /**
   * Test responsive desktop mode
   */
  async testResponsiveDesktop(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing responsive desktop mode...');
      this.responsiveModalThreadsStatus.lastTest = 'Desktop Mode';
      this.responsiveModalThreadsStatus.mode = 'Desktop';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Desktop Mode Test',
          data: {
            message: 'Test desktop mode với mat-menu và Threads animation',
            testData: { timestamp: new Date(), type: 'desktop-mode-threads' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'basic'
          }
        },
        'dialog' // Force desktop mode
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Desktop mode test completed');
      console.log('🚀 Desktop mode result:', result);

    } catch (error) {
      console.error('Lỗi khi test desktop mode:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Desktop mode error: ${error}`);
    }
  }

  /**
   * Test responsive mobile mode
   */
  async testResponsiveMobile(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing responsive mobile mode...');
      this.responsiveModalThreadsStatus.lastTest = 'Mobile Mode';
      this.responsiveModalThreadsStatus.mode = 'Mobile';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Mobile Mode Test',
          data: {
            message: 'Test mobile mode với bottom sheet và Threads animation',
            testData: { timestamp: new Date(), type: 'mobile-mode-threads' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'basic'
          }
        },
        'bottom-sheet' // Force mobile mode
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Mobile mode test completed');
      console.log('🚀 Mobile mode result:', result);

    } catch (error) {
      console.error('Lỗi khi test mobile mode:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Mobile mode error: ${error}`);
    }
  }

  /**
   * Test responsive auto mode
   */
  async testResponsiveAuto(event: MouseEvent): Promise<void> {
    try {
      console.log('🚀 Testing responsive auto mode...');
      this.responsiveModalThreadsStatus.lastTest = 'Auto Mode';
      this.responsiveModalThreadsStatus.mode = 'Auto';
      this.responsiveModalThreadsStatus.integration = 'Testing...';

      const result = await this.responsiveModalService.openWithPopover(
        SimpleTestComponent,
        event,
        {
          title: 'Auto Responsive Test',
          data: {
            message: 'Test auto responsive mode (tự động theo viewport) với Threads animation',
            testData: { timestamp: new Date(), type: 'auto-responsive-threads' }
          },
          threadsAnimation: {
            enabled: true,
            type: 'basic'
          }
        }
        // Không force mode, để tự động detect
      );

      this.responsiveModalThreadsStatus.integration = 'Success';
      this.flashMessageService.success('Auto responsive mode test completed');
      console.log('🚀 Auto responsive result:', result);

    } catch (error) {
      console.error('Lỗi khi test auto responsive:', error);
      this.responsiveModalThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Auto responsive error: ${error}`);
    }
  }

  // ===================================================================
  // STANDARDBOTTOMSHEETSERVICE THREADS ANIMATION INTEGRATION METHODS
  // ===================================================================

  /**
   * Test StandardBottomSheetService basic với Threads animation
   */
  async testStandardBottomSheetBasic(): Promise<void> {
    try {
      console.log('🎭 Testing StandardBottomSheetService basic with Threads animation...');
      this.standardBottomSheetThreadsStatus.lastTest = 'Basic Test';
      this.standardBottomSheetThreadsStatus.integration = 'Testing...';

      // Check if .app-drawer exists
      const appDrawer = document.querySelector('.app-drawer');
      this.standardBottomSheetThreadsStatus.appDrawerFound = appDrawer ? 'Found' : 'Not Found';

      if (appDrawer) {
        this.standardBottomSheetThreadsStatus.animationActive = 'Ready';
      }

      const result = await this.standardBottomSheetService.openAsync(
        SimpleTestComponent,
        {
          title: 'StandardBottomSheet Basic Test',
          data: {
            message: 'Test StandardBottomSheetService với Threads animation tự động',
            testData: { timestamp: new Date(), type: 'standard-bottom-sheet-basic' }
          }
        }
      );

      this.standardBottomSheetThreadsStatus.integration = 'Success';
      this.standardBottomSheetThreadsStatus.animationActive = 'Completed';
      this.flashMessageService.success('StandardBottomSheetService basic test completed');
      console.log('🎭 StandardBottomSheetService basic result:', result);

    } catch (error) {
      console.error('Lỗi khi test StandardBottomSheetService basic:', error);
      this.standardBottomSheetThreadsStatus.integration = 'Error';
      this.standardBottomSheetThreadsStatus.animationActive = 'Error';
      this.flashMessageService.error(`StandardBottomSheetService basic error: ${error}`);
    }
  }

  /**
   * Test StandardBottomSheetService với data
   */
  async testStandardBottomSheetWithData(): Promise<void> {
    try {
      console.log('🎭 Testing StandardBottomSheetService with data...');
      this.standardBottomSheetThreadsStatus.lastTest = 'With Data Test';
      this.standardBottomSheetThreadsStatus.integration = 'Testing...';

      // Check animation readiness
      const appDrawer = document.querySelector('.app-drawer');
      this.standardBottomSheetThreadsStatus.appDrawerFound = appDrawer ? 'Found' : 'Not Found';
      this.standardBottomSheetThreadsStatus.animationActive = 'Active';

      const testData = {
        message: 'Test StandardBottomSheetService với complex data và Threads animation',
        testData: {
          timestamp: new Date(),
          type: 'standard-bottom-sheet-data',
          complexData: {
            items: ['Item 1', 'Item 2', 'Item 3'],
            settings: { theme: 'dark', animation: 'threads' },
            metadata: { version: '1.0', author: 'TestThemeComponent' }
          }
        }
      };

      const result = await this.standardBottomSheetService.openAsync(
        SimpleTestComponent,
        {
          title: 'StandardBottomSheet với Complex Data',
          data: testData,
          enableClose: true
        }
      );

      this.standardBottomSheetThreadsStatus.integration = 'Success';
      this.standardBottomSheetThreadsStatus.animationActive = 'Completed';
      this.flashMessageService.success('StandardBottomSheetService with data test completed');
      console.log('🎭 StandardBottomSheetService with data result:', result);

    } catch (error) {
      console.error('Lỗi khi test StandardBottomSheetService with data:', error);
      this.standardBottomSheetThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`StandardBottomSheetService with data error: ${error}`);
    }
  }

  /**
   * Test StandardBottomSheetService async method
   */
  async testStandardBottomSheetAsync(): Promise<void> {
    try {
      console.log('🎭 Testing StandardBottomSheetService async method...');
      this.standardBottomSheetThreadsStatus.lastTest = 'Async Method Test';
      this.standardBottomSheetThreadsStatus.integration = 'Testing...';
      this.standardBottomSheetThreadsStatus.animationActive = 'Active';

      // Test openAsync method specifically
      const startTime = Date.now();

      const result = await this.standardBottomSheetService.openAsync(
        SimpleTestComponent,
        {
          title: 'Async Method Test',
          data: {
            message: 'Test async/await pattern với StandardBottomSheetService và Threads animation',
            testData: {
              timestamp: new Date(),
              type: 'standard-bottom-sheet-async',
              startTime: startTime
            }
          },
          actions: {
            useDefault: true
          }
        }
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      this.standardBottomSheetThreadsStatus.integration = 'Success';
      this.standardBottomSheetThreadsStatus.animationActive = 'Completed';
      this.flashMessageService.success(`Async method test completed in ${duration}ms`);
      console.log('🎭 StandardBottomSheetService async result:', result, `Duration: ${duration}ms`);

    } catch (error) {
      console.error('Lỗi khi test StandardBottomSheetService async:', error);
      this.standardBottomSheetThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`StandardBottomSheetService async error: ${error}`);
    }
  }

  /**
   * Test multiple StandardBottomSheetService instances
   */
  async testStandardBottomSheetMultiple(): Promise<void> {
    try {
      console.log('🎭 Testing multiple StandardBottomSheetService instances...');
      this.standardBottomSheetThreadsStatus.lastTest = 'Multiple Sheets Test';
      this.standardBottomSheetThreadsStatus.integration = 'Testing...';
      this.standardBottomSheetThreadsStatus.animationActive = 'Multiple Active';

      // Test opening multiple bottom sheets (should handle properly)
      const promises = [];

      for (let i = 1; i <= 3; i++) {
        const promise = this.standardBottomSheetService.openAsync(
          SimpleTestComponent,
          {
            title: `Bottom Sheet ${i}`,
            data: {
              message: `Test multiple StandardBottomSheetService instance ${i}`,
              testData: {
                timestamp: new Date(),
                type: 'standard-bottom-sheet-multiple',
                instanceNumber: i
              }
            }
          }
        );
        promises.push(promise);

        // Delay between opens để test animation conflicts
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Wait for all to complete
      const results = await Promise.all(promises);

      this.standardBottomSheetThreadsStatus.integration = 'Success';
      this.standardBottomSheetThreadsStatus.animationActive = 'All Completed';
      this.flashMessageService.success('Multiple StandardBottomSheetService test completed');
      console.log('🎭 Multiple StandardBottomSheetService results:', results);

    } catch (error) {
      console.error('Lỗi khi test multiple StandardBottomSheetService:', error);
      this.standardBottomSheetThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Multiple StandardBottomSheetService error: ${error}`);
    }
  }

  /**
   * Test quick open/close StandardBottomSheetService
   */
  async testStandardBottomSheetQuickOpen(): Promise<void> {
    try {
      console.log('🎭 Testing quick open/close StandardBottomSheetService...');
      this.standardBottomSheetThreadsStatus.lastTest = 'Quick Open/Close Test';
      this.standardBottomSheetThreadsStatus.integration = 'Testing...';
      this.standardBottomSheetThreadsStatus.animationActive = 'Quick Active';

      // Open and close quickly to test animation cleanup
      const bottomSheetRef = this.standardBottomSheetService.open(
        SimpleTestComponent,
        {
          title: 'Quick Open/Close Test',
          data: {
            message: 'Test quick open/close để kiểm tra animation cleanup',
            testData: {
              timestamp: new Date(),
              type: 'standard-bottom-sheet-quick'
            }
          }
        }
      );

      // Close after 1 second
      setTimeout(() => {
        bottomSheetRef.dismiss();
        console.log('🎭 Quick closed StandardBottomSheetService');
      }, 1000);

      const result = await firstValueFrom(bottomSheetRef.afterDismissed());

      this.standardBottomSheetThreadsStatus.integration = 'Success';
      this.standardBottomSheetThreadsStatus.animationActive = 'Quick Completed';
      this.flashMessageService.success('Quick open/close test completed');
      console.log('🎭 Quick StandardBottomSheetService result:', result);

    } catch (error) {
      console.error('Lỗi khi test quick StandardBottomSheetService:', error);
      this.standardBottomSheetThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Quick StandardBottomSheetService error: ${error}`);
    }
  }

  /**
   * Test stress StandardBottomSheetService
   */
  async testStandardBottomSheetStress(): Promise<void> {
    try {
      console.log('🎭 Testing stress StandardBottomSheetService...');
      this.standardBottomSheetThreadsStatus.lastTest = 'Stress Test';
      this.standardBottomSheetThreadsStatus.integration = 'Testing...';
      this.standardBottomSheetThreadsStatus.animationActive = 'Stress Active';

      // Stress test: Open và close nhiều lần liên tiếp
      const iterations = 5;
      const results = [];

      for (let i = 1; i <= iterations; i++) {
        console.log(`🎭 Stress test iteration ${i}/${iterations}`);

        const startTime = Date.now();
        const result = await this.standardBottomSheetService.openAsync(
          SimpleTestComponent,
          {
            title: `Stress Test ${i}/${iterations}`,
            data: {
              message: `Stress test iteration ${i} - Test animation performance`,
              testData: {
                timestamp: new Date(),
                type: 'standard-bottom-sheet-stress',
                iteration: i,
                totalIterations: iterations
              }
            }
          }
        );
        const endTime = Date.now();

        results.push({
          iteration: i,
          duration: endTime - startTime,
          result: result
        });

        // Short delay between iterations
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      this.standardBottomSheetThreadsStatus.integration = 'Success';
      this.standardBottomSheetThreadsStatus.animationActive = 'Stress Completed';

      const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
      this.flashMessageService.success(`Stress test completed. Avg duration: ${avgDuration.toFixed(0)}ms`);
      console.log('🎭 Stress StandardBottomSheetService results:', results);

    } catch (error) {
      console.error('Lỗi khi test stress StandardBottomSheetService:', error);
      this.standardBottomSheetThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Stress StandardBottomSheetService error: ${error}`);
    }
  }

  /**
   * Test animation timing
   */
  async testAnimationTiming(speed: 'fast' | 'normal' | 'slow'): Promise<void> {
    try {
      console.log(`🎭 Testing animation timing: ${speed}...`);
      this.standardBottomSheetThreadsStatus.lastTest = `Timing Test (${speed})`;
      this.standardBottomSheetThreadsStatus.integration = 'Testing...';
      this.standardBottomSheetThreadsStatus.animationActive = `${speed} Active`;

      const timingMessages = {
        fast: 'Fast animation timing - Scale 50% → Slide up nhanh',
        normal: 'Normal timing - 280ms slide-up, 250ms scale (Threads-like)',
        slow: 'Slow animation timing - Debug mode để quan sát animation'
      };

      const result = await this.standardBottomSheetService.openAsync(
        SimpleTestComponent,
        {
          title: `Animation Timing Test (${speed.toUpperCase()})`,
          data: {
            message: timingMessages[speed],
            testData: {
              timestamp: new Date(),
              type: 'standard-bottom-sheet-timing',
              speed: speed,
              expectedBehavior: speed === 'normal' ? 'Threads-like timing' : `${speed} timing`
            }
          }
        }
      );

      this.standardBottomSheetThreadsStatus.integration = 'Success';
      this.standardBottomSheetThreadsStatus.animationActive = `${speed} Completed`;
      this.flashMessageService.success(`Animation timing test (${speed}) completed`);
      console.log(`🎭 Animation timing (${speed}) result:`, result);

    } catch (error) {
      console.error(`Lỗi khi test animation timing (${speed}):`, error);
      this.standardBottomSheetThreadsStatus.integration = 'Error';
      this.flashMessageService.error(`Animation timing (${speed}) error: ${error}`);
    }
  }
}
