import { Component, Inject, OnInit, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FlatTreeControl } from '@angular/cdk/tree';
import { MatTreeModule, MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { TranslateModule } from '@ngx-translate/core';
import { WarehouseLocation } from '@shared/models/api/warehouse-entities.dto';
import { mockWarehouseLocations } from '@mock/shared/list.mock';
import { WarehouseLocationPickerModalData, WarehouseLocationPickerModalResult } from './models/warehouse-location-picker-modal.model';
import { StrictModalComponent } from '@/shared/components/responsive-modal/models/view/modal-component.interface';

/**
 * Interface cho node phẳng trong tree vị trí
 */
interface LocationFlatNode {
  expandable: boolean;
  name: string;
  level: number;
  id: string;
  path: string[];
}

/**
 * Component modal để chọn vị trí từ cấu trúc tree
 */
@Component({
  selector: 'app-warehouse-location-picker-modal',
  templateUrl: './warehouse-location-picker-modal.component.html',
  styleUrls: ['./warehouse-location-picker-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatTreeModule,
    TranslateModule
  ]
})
export class WarehouseLocationPickerModalComponent implements OnInit, StrictModalComponent<WarehouseLocationPickerModalData, WarehouseLocationPickerModalResult> {
  /**
   * Vị trí đã chọn
   */
  selectedLocationId?: string;

  /**
   * TreeControl cho tree
   */
  treeControl = new FlatTreeControl<LocationFlatNode>(
    node => node.level,
    node => node.expandable
  );

  /**
   * Transformer cho tree
   */
  private transformer = (node: WarehouseLocation, level: number): LocationFlatNode => ({
    expandable: this.isExpandable(node),
    name: node.name,
    level,
    id: node._id,
    path: node.path
  });

  /**
   * Flattener cho tree
   */
  private treeFlattener = new MatTreeFlattener(
    this.transformer,
    node => node.level,
    node => node.expandable,
    node => this.getChildren(node)
  );

  /**
   * Data source cho tree
   */
  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

  /**
   * Dữ liệu từ inject
   */
  data: WarehouseLocationPickerModalData;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: WarehouseLocationPickerModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: WarehouseLocationPickerModalData,
    @Optional() private dialogRef?: MatDialogRef<WarehouseLocationPickerModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<WarehouseLocationPickerModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || { warehouseId: '' };

    this.selectedLocationId = this.data.selectedLocationId;

    // Sử dụng locations từ data hoặc từ mock data
    const allLocations = this.data.locations || [...mockWarehouseLocations];

    // Lọc vị trí theo kho hàng
    const warehouseLocations = allLocations.filter(loc => loc.warehouse._id === this.data.warehouseId);

    // Tìm các node gốc (không có parentId)
    const rootLocations = warehouseLocations.filter(loc => !loc.parentId);

    // Cập nhật datasource
    this.dataSource.data = rootLocations;
  }

  /**
   * Khởi tạo
   */
  ngOnInit(): void {
    // Mở rộng tất cả các node
    setTimeout(() => {
      this.treeControl.expandAll();
    });
  }

  /**
   * Kiểm tra một node có con hay không
   */
  hasChild = (_: number, node: LocationFlatNode) => node.expandable;

  /**
   * Kiểm tra một node có thể mở rộng không
   */
  private isExpandable(node: WarehouseLocation): boolean {
    // Sử dụng locations từ data hoặc từ mock data
    const locations = this.data.locations || mockWarehouseLocations;
    return locations.some((loc: WarehouseLocation) => loc.parentId === node._id);
  }

  /**
   * Lấy các node con của một node
   */
  private getChildren(node: WarehouseLocation): WarehouseLocation[] {
    // Sử dụng locations từ data hoặc từ mock data
    const locations = this.data.locations || mockWarehouseLocations;
    return locations.filter((loc: WarehouseLocation) => loc.parentId === node._id);
  }

  /**
   * Chọn một vị trí
   */
  selectLocation(locationId: string): void {
    this.selectedLocationId = locationId;
  }

  /**
   * Hủy bỏ
   */
  onCancel(): void {
    this.close(undefined);
  }

  /**
   * Lưu
   */
  onSave(): void {
    this.close(this.selectedLocationId);
  }

  /**
   * Đóng modal
   */
  private close(result: string | undefined): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }

  // ===== StrictModalComponent Interface Implementation =====

  /**
   * Required method: getModalResult()
   * Trả về kết quả từ modal component
   */
  getModalResult(): WarehouseLocationPickerModalResult {
    return this.selectedLocationId;
  }

  /**
   * Required method: isValid()
   * Validate dữ liệu trước khi đóng modal
   */
  isValid(): boolean {
    // Warehouse location picker modal luôn valid vì có thể không chọn location nào
    return true;
  }

  /**
   * Optional method: updateData()
   * Cập nhật data cho component
   */
  updateData(data: WarehouseLocationPickerModalData): void {
    this.data = data;
    this.selectedLocationId = data.selectedLocationId;

    // Sử dụng locations từ data hoặc từ mock data
    const allLocations = data.locations || [...mockWarehouseLocations];

    // Lọc vị trí theo kho hàng
    const warehouseLocations = allLocations.filter(loc => loc.warehouse._id === data.warehouseId);

    // Tìm các node gốc (không có parentId)
    const rootLocations = warehouseLocations.filter(loc => !loc.parentId);

    // Cập nhật datasource
    this.dataSource.data = rootLocations;

    // Mở rộng tất cả các node
    setTimeout(() => {
      this.treeControl.expandAll();
    });
  }

  /**
   * Optional method: onModalOpen()
   * Được gọi khi modal được mở
   */
  onModalOpen(): void {
    // Mở rộng tất cả các node khi modal mở
    setTimeout(() => {
      this.treeControl.expandAll();
    }, 100);
  }

  /**
   * Optional method: onModalClose()
   * Được gọi trước khi modal đóng
   */
  onModalClose(): boolean {
    // Luôn cho phép đóng modal
    return true;
  }
}
