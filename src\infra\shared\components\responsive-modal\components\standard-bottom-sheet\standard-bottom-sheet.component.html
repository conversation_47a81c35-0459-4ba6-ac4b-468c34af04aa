<!-- Bottom Sheet Container với responsive Bootstrap classes -->
<div class="standard-bottom-sheet-container">

  <!-- Bottom Sheet Title Section -->
  <div class="standard-bottom-sheet-title d-flex justify-content-between align-items-center">

    <!-- Title Content - String hoặc Template -->
    <div class="title-content flex-grow-1">
      <!-- Hiển thị title dạng string -->
      @if (isTitleString()) {
        <h3 class="mb-0 fw-bold">{{ title() }}</h3>
      }

      <!-- Hiển thị title dạng template -->
      @if (isTitleTemplate() && title()) {
        <ng-container
          [ngTemplateOutlet]="$any(title())"
          [ngTemplateOutletContext]="{ $implicit: getComponentData() }">
        </ng-container>
      }

      <!-- Default title nếu không có title được cung cấp -->
      @if (!title()) {
        <h3 class="mb-0 fw-bold">{{ 'STANDARD_BOTTOM_SHEET.DEFAULT_TITLE' | translate }}</h3>
      }
    </div>

    <!-- Close Button -->
    @if (enableClose()) {
      <button
        mat-icon-button
        class="close-button ms-2"
        (click)="onClose()"
        [attr.aria-label]="'STANDARD_BOTTOM_SHEET.CLOSE_BUTTON_ARIA' | translate"
        type="button">
        <mat-icon>close</mat-icon>
      </button>
    }
  </div>

  <!-- Bottom Sheet Content Section -->
  <div class="standard-bottom-sheet-content">
    <!-- Dynamic Component Container -->
    <div #dynamicComponentContainer></div>

    <!-- Fallback content projection nếu không có dynamic component -->
    <ng-content></ng-content>
  </div>

  <!-- Bottom Sheet Actions Section -->
  <div class="standard-bottom-sheet-actions d-flex justify-content-end gap-2 mt-3">

    <!-- Default Actions - Hủy và Xác nhận -->
    @if (useDefault()) {
      <div class="default-actions d-flex gap-2">
        <!-- Nút Hủy -->
        <button
          mat-button
          class="cancel-button"
          (click)="onCancel()"
          type="button">
          {{ 'COMMON.CANCEL' | translate }}
        </button>

        <!-- Nút Xác nhận -->
        <button
          mat-raised-button
          color="primary"
          class="confirm-button"
          [disabled]="!isComponentValid()"
          (click)="onConfirm()"
          type="button">
          {{ 'COMMON.SAVE' | translate }}
        </button>
      </div>
    }

    <!-- Custom Actions Template -->
    @if (!useDefault() && customActionsTemplate()) {
      <div class="custom-actions">
        <ng-container
          [ngTemplateOutlet]="$any(customActionsTemplate())"
          [ngTemplateOutletContext]="{
            $implicit: getComponentData(),
            onCustomAction: onCustomAction.bind(this),
            onClose: onClose.bind(this)
          }">
        </ng-container>
      </div>
    }

    <!-- Fallback nếu không có actions -->
    @if (!useDefault() && !customActionsTemplate()) {
      <div class="fallback-actions">
        <button
          mat-button
          class="close-button"
          (click)="onClose()"
          type="button">
          {{ 'STANDARD_BOTTOM_SHEET.ACTIONS.CLOSE' | translate }}
        </button>
      </div>
    }
  </div>

</div>
